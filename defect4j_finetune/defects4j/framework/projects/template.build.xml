<!--
Copyright (c) 2014-2024 <PERSON>, <PERSON><PERSON><PERSON>, and Defect<PERSON><PERSON><PERSON> contributors.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERC<PERSON>NTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

################################################################################
               This is the project-specific build file for <PID>.

It defines project-specific properties and targets, and imports the build file
of the checked-out project version.
#############################################################################-->
<project name="D4j-<PID>" basedir="${basedir}">
    <!-- TODO: Make sure that the following properties and paths are defined:
           - source.home        ->  Directory of sources
           - test.home          ->  Directory of test sources
           - build.home         ->  Root directory for compiled (test) classes
           - test.classpath     ->  Classpath to compile tests
           - compile.classpath  ->  Classpath to compile sources
    -->

    <!-- Set path for dependencies -->
    <property name="maven.repo.local" value="${d4j.dir.projects}/<PID>/lib/" />
    <property name="libdir" value="${maven.repo.local}" />

    <!-- Do not download maven dependencies for configured projects and bugs -->
    <property name="maven.settings.offline" value="true" />

    <!-- Generate all debugging information -->
    <property name="compile.debug" value="yes" />

    <!-- Include existing project build file -->
    <import file="${d4j.workdir}/build.xml"/>

    <!-- Create a compile.tests target if necessary (compile-tests is a commonly
-         used target name in older project versions) -->
    <scriptdef name="hastarget" language="javascript">
        <attribute name="targetname" />
        <attribute name="newtargetname" />
        <attribute name="setproperty" />
        <![CDATA[
            var targetname    = attributes.get("targetname");
            var newtargetname = attributes.get("newtargetname");
            var setproperty   = attributes.get("setproperty");
            if(project.getTargets().containsKey(targetname)) {
                var newTarget = new org.apache.tools.ant.Target();
                newTarget.addDependency(targetname);
                newTarget.setName(newtargetname);
                project.addTarget(newtargetname, newTarget);
                project.setProperty(setproperty, "yes");
            }
        ]]>
    </scriptdef>

    <hastarget targetname="compile-tests" newtargetname="compile.tests" setproperty="oldLayout" />

    <!-- Project properties targets -->
    <if> <isset property="ant.refid:compile.classpath" />
          <then>
            <property name="test.dir" value="${test.home}" />
          </then>
          <else>
            <property name="oldversion"    value="yes" />
            <path id="compile.classpath"   refid="build.classpath" />
            <path id="test.classpath"      refid="build.test.classpath" />

            <property name="dir.src.classes"  value="${maven.build.srcDir.0}"/>
            <property name="dir.src.tests"    value="${maven.build.testDir.0}"/>
            <property name="test.dir"         value="${maven.build.testDir.0}" />
            <property name="source.home"      value="${maven.build.srcDir.0}"/>
            <property name="d4j.test.dir"     value="${maven.build.testDir.0}"/>
          </else>
    </if>
    <property name="test.home" value="${maven.build.testOutputDir}" />
    <condition property="classes.dir" value="${maven.build.outputDir}" else="${build.classes}">
        <isset property="maven.build.outputDir" />
    </condition>
    <condition property="test.classes.dir" value="${maven.build.testOutputDir}" else="${build.tests}">
        <isset property="maven.build.testOutputDir" />
    </condition>

    <property name="build.home" value="${classes.dir}/.." />

    <!-- Classpath to run developer-written tests -->
    <path id="d4j.test.classpath">
        <path refid="test.classpath"/>
        <pathelement location="${classes.dir}"/>
        <pathelement location="${test.classes.dir}"/>
        <pathelement path="${junit.jar}" />
        <!--
        <pathelement path="${dependency.jar}" />
        -->
    </path>

    <!-- List of all developer-written tests that reliably pass on the fixed version -->
    <!-- Manually generalize test patterns if the build-file analyzer produces incorrect outputs -->
    <fileset id="all.manual.tests" dir="${d4j.dir.src.tests}" excludes="${d4j.tests.exclude}">
        <!--###ADD_INC_EXC### this line will be automatically replaced -->
        <!--include name="____" /-->
        <!--exclude name="____" /-->
    </fileset>

</project>
