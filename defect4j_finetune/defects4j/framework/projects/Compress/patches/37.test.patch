diff --git a/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java b/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java
index 015748b6..84f2e1df 100644
--- a/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java
@@ -23,7 +23,6 @@ import static org.apache.commons.compress.AbstractTestCase.mkdir;
 import static org.apache.commons.compress.AbstractTestCase.rmdir;
 import static org.junit.Assert.assertArrayEquals;
 import static org.junit.Assert.assertEquals;
-import static org.junit.Assert.assertNull;
 import static org.junit.Assert.assertTrue;
 import static org.junit.Assert.fail;
 
@@ -298,21 +297,6 @@ public class TarArchiveInputStreamTest {
         }
     }
 
-    /**
-     * @link "https://issues.apache.org/jira/browse/COMPRESS-355"
-     */
-    @Test
-    public void survivesBlankLinesInPaxHeader() throws Exception {
-        final TarArchiveInputStream is = getTestStream("/COMPRESS-355.tar");
-        try {
-            final TarArchiveEntry entry = is.getNextTarEntry();
-            assertEquals("package/package.json", entry.getName());
-            assertNull(is.getNextTarEntry());
-        } finally {
-            is.close();
-        }
-    }
-
     private TarArchiveInputStream getTestStream(final String name) {
         return new TarArchiveInputStream(
                 TarArchiveInputStreamTest.class.getResourceAsStream(name));
