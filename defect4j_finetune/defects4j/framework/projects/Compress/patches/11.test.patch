diff --git a/src/test/java/org/apache/commons/compress/archivers/ArchiveStreamFactoryTest.java b/src/test/java/org/apache/commons/compress/archivers/ArchiveStreamFactoryTest.java
deleted file mode 100644
index 88a1160a..00000000
--- a/src/test/java/org/apache/commons/compress/archivers/ArchiveStreamFactoryTest.java
+++ /dev/null
@@ -1,42 +0,0 @@
-/*
- * Licensed to the Apache Software Foundation (ASF) under one
- * or more contributor license agreements.  See the NOTICE file
- * distributed with this work for additional information
- * regarding copyright ownership.  The ASF licenses this file
- * to you under the Apache License, Version 2.0 (the
- * "License"); you may not use this file except in compliance
- * with the License.  You may obtain a copy of the License at
- *
- * http://www.apache.org/licenses/LICENSE-2.0
- *
- * Unless required by applicable law or agreed to in writing,
- * software distributed under the License is distributed on an
- * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
- * KIND, either express or implied.  See the License for the
- * specific language governing permissions and limitations
- * under the License.
- */
-package org.apache.commons.compress.archivers;
-
-import java.io.ByteArrayInputStream;
-import org.junit.Test;
-import static org.junit.Assert.assertTrue;
-import static org.junit.Assert.fail;
-
-public class ArchiveStreamFactoryTest {
-
-    /**
-     * see https://issues.apache.org/jira/browse/COMPRESS-171
-     */
-    @Test
-    public void shortTextFilesAreNoTARs() throws Exception {
-        try {
-            new ArchiveStreamFactory()
-                .createArchiveInputStream(new ByteArrayInputStream("This certainly is not a tar archive, really, no kidding".getBytes()));
-            fail("created an input stream for a non-archive");
-        } catch (ArchiveException ae) {
-            assertTrue(ae.getMessage().startsWith("No Archiver found"));
-        }
-    }
-
-}
\ No newline at end of file
