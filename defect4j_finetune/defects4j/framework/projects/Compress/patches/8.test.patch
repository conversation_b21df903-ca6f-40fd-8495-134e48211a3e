diff --git a/src/test/java/org/apache/commons/compress/archivers/tar/TarUtilsTest.java b/src/test/java/org/apache/commons/compress/archivers/tar/TarUtilsTest.java
index d541341e..f7558b19 100644
--- a/src/test/java/org/apache/commons/compress/archivers/tar/TarUtilsTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/tar/TarUtilsTest.java
@@ -50,66 +50,20 @@ public class TarUtilsTest extends TestCase {
         buffer[buffer.length-1]=0;
         value = TarUtils.parseOctal(buffer,0, buffer.length);
         assertEquals(MAX_OCTAL, value);
-        buffer=new byte[]{0,0};
-        value = TarUtils.parseOctal(buffer,0, buffer.length);
-        assertEquals(0, value);        
-        buffer=new byte[]{0,' '};
-        value = TarUtils.parseOctal(buffer,0, buffer.length);
-        assertEquals(0, value);        
-    }
-
-    public void testParseOctalInvalid() throws Exception{
-        byte [] buffer;
-        buffer=new byte[0]; // empty byte array
-        try {
-            TarUtils.parseOctal(buffer,0, buffer.length);
-            fail("Expected IllegalArgumentException - should be at least 2 bytes long");
-        } catch (IllegalArgumentException expected) {
-        }
-        buffer=new byte[]{0}; // 1-byte array
-        try {
-            TarUtils.parseOctal(buffer,0, buffer.length);
-            fail("Expected IllegalArgumentException - should be at least 2 bytes long");
-        } catch (IllegalArgumentException expected) {
-        }
-        buffer=new byte[]{0,0,' '}; // not all NULs
-        try {
-            TarUtils.parseOctal(buffer,0, buffer.length);
-            fail("Expected IllegalArgumentException - not all NULs");
-        } catch (IllegalArgumentException expected) {
-        }
-        buffer=new byte[]{' ',0,0,0}; // not all NULs
-        try {
-            TarUtils.parseOctal(buffer,0, buffer.length);
-            fail("Expected IllegalArgumentException - not all NULs");
-        } catch (IllegalArgumentException expected) {
-        }
         buffer = "abcdef ".getBytes("UTF-8"); // Invalid input
         try {
             TarUtils.parseOctal(buffer,0, buffer.length);
             fail("Expected IllegalArgumentException");
         } catch (IllegalArgumentException expected) {
         }
-        buffer = "77777777777".getBytes("UTF-8"); // Invalid input - no trailer
-        try {
-            TarUtils.parseOctal(buffer,0, buffer.length);
-            fail("Expected IllegalArgumentException - no trailer");
-        } catch (IllegalArgumentException expected) {
-        }
-        buffer = " 0 07 ".getBytes("UTF-8"); // Invalid - embedded space
-        try {
-            TarUtils.parseOctal(buffer,0, buffer.length);
-            fail("Expected IllegalArgumentException - embedded space");
-        } catch (IllegalArgumentException expected) {
-        }
-        buffer = " 0\00007 ".getBytes("UTF-8"); // Invalid - embedded NUL
+        buffer = "77777777777".getBytes("UTF-8"); // Invalid input
         try {
             TarUtils.parseOctal(buffer,0, buffer.length);
-            fail("Expected IllegalArgumentException - embedded NUL");
+            fail("Expected IllegalArgumentException");
         } catch (IllegalArgumentException expected) {
         }
     }
-
+    
     private void checkRoundTripOctal(final long value) {
         byte [] buffer = new byte[12];
         long parseValue;
