diff --git a/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java b/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java
index d76360f6..680cb8cf 100644
--- a/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java
@@ -20,12 +20,10 @@ package org.apache.commons.compress.archivers.tar;
 
 import static org.junit.Assert.assertEquals;
 import static org.junit.Assert.assertTrue;
-import static org.junit.Assert.fail;
 
 import java.io.ByteArrayInputStream;
 import java.io.File;
 import java.io.FileInputStream;
-import java.io.IOException;
 import java.net.URI;
 import java.net.URL;
 import java.util.Calendar;
@@ -122,24 +120,4 @@ public class TarArchiveInputStreamTest {
         }
     }
 
-    @Test
-    public void testCompress197() throws Exception {
-        TarArchiveInputStream tar = getTestStream("/COMPRESS-197.tar");
-        try {
-            TarArchiveEntry entry = tar.getNextTarEntry();
-            while (entry != null) {
-                entry = tar.getNextTarEntry();
-            }
-        } catch (IOException e) {
-            fail("COMPRESS-197: " + e.getMessage());
-        } finally {
-            tar.close();
-        }
-    }
-
-    private TarArchiveInputStream getTestStream(String name) {
-        return new TarArchiveInputStream(
-                TarArchiveInputStreamTest.class.getResourceAsStream(name));
-    }
-
-}
+}
\ No newline at end of file
