diff --git a/src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java b/src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java
index 27b2a00d..31ef5368 100644
--- a/src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java
+++ b/src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java
@@ -50,50 +50,30 @@ public class TarUtils {
      */
     public static long parseOctal(final byte[] buffer, final int offset, final int length) {
         long    result = 0;
+        boolean stillPadding = true;
         int     end = offset + length;
         int     start = offset;
 
-        if (length < 2){
-            throw new IllegalArgumentException("Length "+length+" must be at least 2");
-        }
-
-        boolean allNUL = true;
         for (int i = start; i < end; i++){
-            if (buffer[i] != 0){
-                allNUL = false;
+            final byte currentByte = buffer[i];
+            if (currentByte == 0) {
                 break;
             }
-        }
-        if (allNUL) {
-            return 0L;
-        }
 
         // Skip leading spaces
-        while (start < end){
-            if (buffer[start] == ' '){
-                start++;
-            } else {
+            if (currentByte == (byte) ' ' || currentByte == '0') {
+                if (stillPadding) {
+                   continue;
+            }
+                if (currentByte == (byte) ' ') {
                 break;
+                }
             }
-        }
 
         // Must have trailing NUL or space
-        byte trailer;
-        trailer = buffer[end-1];
-        if (trailer == 0 || trailer == ' '){
-            end--;
-        } else {
-            throw new IllegalArgumentException(
-                    exceptionMessage(buffer, offset, length, end-1, trailer));
-        }
         // May have additional NUL or space
-        trailer = buffer[end-1];
-        if (trailer == 0 || trailer == ' '){
-            end--;
-        }
 
-        for ( ;start < end; start++) {
-            final byte currentByte = buffer[start];
+            stillPadding = false;
             // CheckStyle:MagicNumber OFF
             if (currentByte < '0' || currentByte > '7'){
                 throw new IllegalArgumentException(
