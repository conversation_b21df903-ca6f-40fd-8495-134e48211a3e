diff --git a/src/main/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntry.java b/src/main/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntry.java
index db07960a..88cf183f 100644
--- a/src/main/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntry.java
+++ b/src/main/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntry.java
@@ -509,10 +509,6 @@ public class ZipArchiveEntry extends java.util.zip.ZipEntry
      * @param name the name to use
      */
     protected void setName(String name) {
-        if (name != null && getPlatform() == PLATFORM_FAT
-            && name.indexOf("/") == -1) {
-            name = name.replace('\\', '/');
-        }
         this.name = name;
     }
 
