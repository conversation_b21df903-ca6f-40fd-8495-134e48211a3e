diff --git a/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java b/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java
index ca53b8b4..f4fca534 100644
--- a/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java
@@ -235,27 +235,6 @@ public class TarArchiveInputStreamTest {
         }
     }
 
-    @Test
-    public void shouldReadBigGid() throws Exception {
-        ByteArrayOutputStream bos = new ByteArrayOutputStream();
-        TarArchiveOutputStream tos = new TarArchiveOutputStream(bos);
-        tos.setBigNumberMode(TarArchiveOutputStream.BIGNUMBER_POSIX);
-        TarArchiveEntry t = new TarArchiveEntry("name");
-        t.setGroupId(4294967294l);
-        t.setSize(1);
-        tos.putArchiveEntry(t);
-        tos.write(30);
-        tos.closeArchiveEntry();
-        tos.close();
-        byte[] data = bos.toByteArray();
-        ByteArrayInputStream bis = new ByteArrayInputStream(data);
-        TarArchiveInputStream tis =
-            new TarArchiveInputStream(bis);
-        t = tis.getNextTarEntry();
-        assertEquals(4294967294l, t.getLongGroupId());
-        tis.close();
-    }
-
     private TarArchiveInputStream getTestStream(String name) {
         return new TarArchiveInputStream(
                 TarArchiveInputStreamTest.class.getResourceAsStream(name));
