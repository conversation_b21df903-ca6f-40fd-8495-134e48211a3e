diff --git a/src/test/java/org/apache/commons/compress/utils/ChecksumCalculatingInputStreamTest.java b/src/test/java/org/apache/commons/compress/utils/ChecksumCalculatingInputStreamTest.java
index a9e52464..b97b09eb 100644
--- a/src/test/java/org/apache/commons/compress/utils/ChecksumCalculatingInputStreamTest.java
+++ b/src/test/java/org/apache/commons/compress/utils/ChecksumCalculatingInputStreamTest.java
@@ -22,7 +22,6 @@ import java.io.BufferedInputStream;
 import java.io.ByteArrayInputStream;
 import java.io.IOException;
 import java.util.zip.Adler32;
-import java.util.zip.CRC32;
 
 import static org.junit.Assert.*;
 
@@ -107,30 +106,5 @@ public class ChecksumCalculatingInputStreamTest {
     }
 
 
-    @Test(expected = NullPointerException.class)
-    public void testClassInstantiationWithParameterBeingNullThrowsNullPointerExceptionOne() {
-
-        ChecksumCalculatingInputStream checksumCalculatingInputStream = new ChecksumCalculatingInputStream(null,null);
-
-
-    }
-
-
-    @Test(expected = NullPointerException.class)
-    public void testClassInstantiationWithParameterBeingNullThrowsNullPointerExceptionTwo() {
-
-        ChecksumCalculatingInputStream checksumCalculatingInputStream = new ChecksumCalculatingInputStream(null,new ByteArrayInputStream(new byte[1]));
-
-
-    }
-
-
-    @Test(expected = NullPointerException.class)
-    public void testClassInstantiationWithParameterBeingNullThrowsNullPointerExceptionThree() {
-
-        ChecksumCalculatingInputStream checksumCalculatingInputStream = new ChecksumCalculatingInputStream(new CRC32(),null);
-
-    }
-
 
 }
\ No newline at end of file
