diff --git a/src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java b/src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java
index 8aba2e24..069b0391 100644
--- a/src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java
+++ b/src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java
@@ -95,11 +95,10 @@ public class TarUtils {
         int          end = offset + length;
 
         for (int i = offset; i < end; ++i) {
-            byte b = buffer[i];
-            if (b == 0) { // Trailing null
+            if (buffer[i] == 0) {
                 break;
             }
-            result.append((char) (b & 0xFF)); // Allow for sign-extension
+            result.append((char) buffer[i]);
         }
 
         return result.toString();
