diff --git a/src/test/java/org/apache/commons/compress/archivers/zip/X7875_NewUnixTest.java b/src/test/java/org/apache/commons/compress/archivers/zip/X7875_NewUnixTest.java
index 03326f13..97b87e44 100644
--- a/src/test/java/org/apache/commons/compress/archivers/zip/X7875_NewUnixTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/zip/X7875_NewUnixTest.java
@@ -27,7 +27,6 @@ import java.util.Enumeration;
 import java.util.zip.ZipException;
 
 import static org.apache.commons.compress.AbstractTestCase.getFile;
-import static org.junit.Assert.assertArrayEquals;
 import static org.junit.Assert.assertEquals;
 import static org.junit.Assert.assertFalse;
 import static org.junit.Assert.assertTrue;
@@ -208,6 +207,13 @@ public class X7875_NewUnixTest {
         assertEquals(expectedUID, xf.getUID());
         assertEquals(expectedGID, xf.getGID());
 
+        // Initial central parse (init with garbage to avoid defaults causing test to pass).
+        xf.setUID(54321);
+        xf.setGID(12345);
+        xf.parseFromCentralDirectoryData(expected, 0, expected.length);
+        assertEquals(expectedUID, xf.getUID());
+        assertEquals(expectedGID, xf.getGID());
+
         xf.setUID(uid);
         xf.setGID(gid);
         if (expected.length < 5) {
@@ -233,9 +239,22 @@ public class X7875_NewUnixTest {
         assertEquals(expectedUID, xf.getUID());
         assertEquals(expectedGID, xf.getGID());
 
-        assertEquals(0, xf.getCentralDirectoryLength().getValue());
+        // Do the same as above, but with Central Directory data:
+        xf.setUID(uid);
+        xf.setGID(gid);
+        if (expected.length < 5) {
+            // We never emit zero-length entries.
+            assertEquals(5, xf.getCentralDirectoryLength().getValue());
+        } else {
+            assertEquals(expected.length, xf.getCentralDirectoryLength().getValue());
+        }
         result = xf.getCentralDirectoryData();
-        assertArrayEquals(new byte[0], result);
+        if (expected.length < 5) {
+            // We never emit zero-length entries.
+            assertTrue(Arrays.equals(new byte[]{1,1,0,1,0}, result));
+        } else {
+            assertTrue(Arrays.equals(expected, result));
+        }
 
         // And now we re-parse:
         xf.parseFromCentralDirectoryData(result, 0, result.length);
