diff --git a/src/main/java/org/apache/commons/compress/archivers/ArchiveStreamFactory.java b/src/main/java/org/apache/commons/compress/archivers/ArchiveStreamFactory.java
index c831b60b..a30cd5a8 100644
--- a/src/main/java/org/apache/commons/compress/archivers/ArchiveStreamFactory.java
+++ b/src/main/java/org/apache/commons/compress/archivers/ArchiveStreamFactory.java
@@ -237,7 +237,6 @@ public class ArchiveStreamFactory {
                 return new TarArchiveInputStream(in);
             }
             // COMPRESS-117 - improve auto-recognition
-            if (signatureLength >= 512) {
             try {
                 TarArchiveInputStream tais = new TarArchiveInputStream(new ByteArrayInputStream(tarheader));
                 tais.getNextEntry();
@@ -247,7 +246,6 @@ public class ArchiveStreamFactory {
                 // autodetection, simply not a TAR
                 // ignored
             }
-            }
         } catch (IOException e) {
             throw new ArchiveException("Could not use reset and mark operations.", e);
         }
