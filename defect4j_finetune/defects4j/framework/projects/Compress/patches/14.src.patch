diff --git a/src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java b/src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java
index d989b8a7..8239480f 100644
--- a/src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java
+++ b/src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java
@@ -62,7 +62,14 @@ public class TarUtils {
             throw new IllegalArgumentException("Length "+length+" must be at least 2");
         }
 
-        if (buffer[start] == 0) {
+        boolean allNUL = true;
+        for (int i = start; i < end; i++){
+            if (buffer[i] != 0){
+                allNUL = false;
+                break;
+            }
+        }
+        if (allNUL) {
             return 0L;
         }
 
