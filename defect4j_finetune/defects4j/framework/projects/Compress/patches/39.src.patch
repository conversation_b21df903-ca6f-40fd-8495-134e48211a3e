diff --git a/src/main/java/org/apache/commons/compress/utils/ArchiveUtils.java b/src/main/java/org/apache/commons/compress/utils/ArchiveUtils.java
index cd0165fd..0a755ce3 100644
--- a/src/main/java/org/apache/commons/compress/utils/ArchiveUtils.java
+++ b/src/main/java/org/apache/commons/compress/utils/ArchiveUtils.java
@@ -28,7 +28,6 @@ import org.apache.commons.compress.archivers.ArchiveEntry;
  */
 public class ArchiveUtils {
 
-    private static final int MAX_SANITIZED_NAME_LENGTH = 255;
 
     /** Private constructor to prevent instantiation of this utility class. */
     private ArchiveUtils(){
@@ -271,13 +270,7 @@ public class ArchiveUtils {
      * @since Compress 1.12
      */
     public static String sanitize(String s) {
-        final char[] cs = s.toCharArray();
-        final char[] chars = cs.length <= MAX_SANITIZED_NAME_LENGTH ? cs : Arrays.copyOf(cs, MAX_SANITIZED_NAME_LENGTH);
-        if (cs.length > MAX_SANITIZED_NAME_LENGTH) {
-            for (int i = MAX_SANITIZED_NAME_LENGTH - 3; i < MAX_SANITIZED_NAME_LENGTH; i++) {
-                chars[i] = '.';
-            }
-        }
+        final char[] chars = s.toCharArray();
         final int len = chars.length;
         final StringBuilder sb = new StringBuilder();
         for (int i = 0; i < len; i++) {
