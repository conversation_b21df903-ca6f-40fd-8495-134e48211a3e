diff --git a/src/main/java/org/apache/commons/compress/archivers/zip/ZipArchiveInputStream.java b/src/main/java/org/apache/commons/compress/archivers/zip/ZipArchiveInputStream.java
index 9a3aa37f..38ae9568 100644
--- a/src/main/java/org/apache/commons/compress/archivers/zip/ZipArchiveInputStream.java
+++ b/src/main/java/org/apache/commons/compress/archivers/zip/ZipArchiveInputStream.java
@@ -181,7 +181,6 @@ public class ZipArchiveInputStream extends ArchiveInputStream {
         this.allowStoredEntriesWithDataDescriptor =
             allowStoredEntriesWithDataDescriptor;
         // haven't read anything so far
-        buf.limit(0);
     }
 
     public ZipArchiveEntry getNextZipEntry() throws IOException {
