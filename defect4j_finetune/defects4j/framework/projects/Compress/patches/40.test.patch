diff --git a/src/test/java/org/apache/commons/compress/utils/BitInputStreamTest.java b/src/test/java/org/apache/commons/compress/utils/BitInputStreamTest.java
index 11ff9564..6f90af30 100644
--- a/src/test/java/org/apache/commons/compress/utils/BitInputStreamTest.java
+++ b/src/test/java/org/apache/commons/compress/utils/BitInputStreamTest.java
@@ -118,57 +118,6 @@ public class BitInputStreamTest {
         bis.close();
     }
 
-    /**
-     * @see "https://issues.apache.org/jira/browse/COMPRESS-363"
-     */
-    @Test
-    public void littleEndianWithOverflow() throws Exception {
-        ByteArrayInputStream in = new ByteArrayInputStream(new byte[] {
-                87, // 01010111
-                45, // 00101101
-                66, // 01000010
-                15, // 00001111
-                90, // 01011010
-                29, // 00011101
-                88, // 01011000
-                61, // 00111101
-                33, // 00100001
-                74  // 01001010
-            });
-        BitInputStream bin = new BitInputStream(in, ByteOrder.LITTLE_ENDIAN);
-        assertEquals(23, // 10111
-                     bin.readBits(5));
-        assertEquals(714595605644185962l, // 0001-00111101-01011000-00011101-01011010-00001111-01000010-00101101-010
-                     bin.readBits(63));
-        assertEquals(1186, // 01001010-0010
-                     bin.readBits(12));
-        assertEquals(-1 , bin.readBits(1));
-    }
-
-    @Test
-    public void bigEndianWithOverflow() throws Exception {
-        ByteArrayInputStream in = new ByteArrayInputStream(new byte[] {
-                87, // 01010111
-                45, // 00101101
-                66, // 01000010
-                15, // 00001111
-                90, // 01011010
-                29, // 00011101
-                88, // 01011000
-                61, // 00111101
-                33, // 00100001
-                74  // 01001010
-            });
-        BitInputStream bin = new BitInputStream(in, ByteOrder.BIG_ENDIAN);
-        assertEquals(10, // 01010
-                     bin.readBits(5));
-        assertEquals(8274274654740644818l, //111-00101101-01000010-00001111-01011010-00011101-01011000-00111101-0010
-                     bin.readBits(63));
-        assertEquals(330, // 0001-01001010
-                     bin.readBits(12));
-        assertEquals(-1 , bin.readBits(1));
-    }
-
     private ByteArrayInputStream getStream() {
         return new ByteArrayInputStream(new byte[] {
                 (byte) 0xF8,  // 11111000
