diff --git a/src/test/java/org/apache/commons/compress/AbstractTestCase.java b/src/test/java/org/apache/commons/compress/AbstractTestCase.java
index c824e85d..7ca82f7d 100644
--- a/src/test/java/org/apache/commons/compress/AbstractTestCase.java
+++ b/src/test/java/org/apache/commons/compress/AbstractTestCase.java
@@ -155,7 +155,6 @@ public abstract class AbstractTestCase extends TestCase {
             addArchiveEntry(out, "something/bla", file6);
             addArchiveEntry(out, "test with spaces.txt", file6);
 
-            out.finish();
             return archive;
         } finally {
             if (out != null) {
@@ -197,7 +196,6 @@ public abstract class AbstractTestCase extends TestCase {
             archive = File.createTempFile("empty", "." + archivename);
             stream = new FileOutputStream(archive);
             out = factory.createArchiveOutputStream(archivename, stream);
-            out.finish();
         } finally {
             if (out != null) {
                 out.close();
@@ -225,7 +223,6 @@ public abstract class AbstractTestCase extends TestCase {
             out = factory.createArchiveOutputStream(archivename, stream);
             // Use short file name so does not cause problems for ar
             addArchiveEntry(out, "test1.xml", getFile("test1.xml"));
-            out.finish();
         } finally {
             if (out != null) {
                 out.close();
diff --git a/src/test/java/org/apache/commons/compress/archivers/CpioTestCase.java b/src/test/java/org/apache/commons/compress/archivers/CpioTestCase.java
index e7e6b8c1..a0bb2941 100644
--- a/src/test/java/org/apache/commons/compress/archivers/CpioTestCase.java
+++ b/src/test/java/org/apache/commons/compress/archivers/CpioTestCase.java
@@ -72,7 +72,7 @@ public final class CpioTestCase extends AbstractTestCase {
             os.putArchiveEntry(entry);
             IOUtils.copy(new FileInputStream(file2), os);
             os.closeArchiveEntry();
-            os.finish();
+
             os.close();
             out.close();
         }
diff --git a/src/test/java/org/apache/commons/compress/archivers/jar/JarArchiveOutputStreamTest.java b/src/test/java/org/apache/commons/compress/archivers/jar/JarArchiveOutputStreamTest.java
index d8f577e0..b518f76b 100644
--- a/src/test/java/org/apache/commons/compress/archivers/jar/JarArchiveOutputStreamTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/jar/JarArchiveOutputStreamTest.java
@@ -41,7 +41,6 @@ public class JarArchiveOutputStreamTest extends TestCase {
             out.closeArchiveEntry();
             out.putArchiveEntry(new ZipArchiveEntry("bar/"));
             out.closeArchiveEntry();
-            out.finish();
             out.close();
             out = null;
 
diff --git a/src/test/java/org/apache/commons/compress/archivers/zip/UTF8ZipFilesTest.java b/src/test/java/org/apache/commons/compress/archivers/zip/UTF8ZipFilesTest.java
index 7fd8163f..6731fbe7 100644
--- a/src/test/java/org/apache/commons/compress/archivers/zip/UTF8ZipFilesTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/zip/UTF8ZipFilesTest.java
@@ -273,8 +273,6 @@ public class UTF8ZipFilesTest extends AbstractTestCase {
             zos.putArchiveEntry(ze);
             zos.write("ascii".getBytes("US-ASCII"));
             zos.closeArchiveEntry();
-            
-            zos.finish();
         } finally {
             if (zos != null) {
                 try {
