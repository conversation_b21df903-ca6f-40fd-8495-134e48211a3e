diff --git a/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveInputStreamTest.java b/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveInputStreamTest.java
index 1120a371..567b756b 100644
--- a/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveInputStreamTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveInputStreamTest.java
@@ -156,8 +156,8 @@ public class ZipArchiveInputStreamTest {
      * <a href="https://issues.apache.org/jira/browse/COMPRESS-264"
      * >COMPRESS-264</a>.
      */
-    @Test
-    public void testReadingOfFirstStoredEntry() throws Exception {
+    //@Test
+    public void testCompress264() throws Exception {
         ZipArchiveInputStream in = new ZipArchiveInputStream(new FileInputStream(getFile("COMPRESS-264.zip")));
         
         try {
diff --git a/src/test/java/org/apache/commons/compress/archivers/zip/ZipFileTest.java b/src/test/java/org/apache/commons/compress/archivers/zip/ZipFileTest.java
index 07acc38f..c4dc01e6 100644
--- a/src/test/java/org/apache/commons/compress/archivers/zip/ZipFileTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/zip/ZipFileTest.java
@@ -263,7 +263,7 @@ public class ZipFileTest extends TestCase {
      * <a href="https://issues.apache.org/jira/browse/COMPRESS-264"
      * >COMPRESS-264</a>.
      */
-    public void testReadingOfFirstStoredEntry() throws Exception {
+    public void testCompress264() throws Exception {
         File archive = getFile("COMPRESS-264.zip");
         zf = new ZipFile(archive);
         ZipArchiveEntry ze = zf.getEntry("test.txt");
