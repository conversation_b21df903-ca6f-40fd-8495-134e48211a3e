diff --git a/src/test/java/org/apache/commons/compress/archivers/tar/TarUtilsTest.java b/src/test/java/org/apache/commons/compress/archivers/tar/TarUtilsTest.java
index 9210c54e..7d6eb77d 100644
--- a/src/test/java/org/apache/commons/compress/archivers/tar/TarUtilsTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/tar/TarUtilsTest.java
@@ -64,9 +64,6 @@ public class TarUtilsTest extends TestCase {
         buffer=new byte[]{0,' '};
         value = TarUtils.parseOctal(buffer,0, buffer.length);
         assertEquals(0, value);
-        buffer=new byte[]{' ',0};
-        value = TarUtils.parseOctal(buffer,0, buffer.length);
-        assertEquals(0, value);
     }
 
     public void testParseOctalInvalid() throws Exception{
@@ -83,6 +80,12 @@ public class TarUtilsTest extends TestCase {
             fail("Expected IllegalArgumentException - should be at least 2 bytes long");
         } catch (IllegalArgumentException expected) {
         }
+        buffer=new byte[]{' ',0,0,0}; // not all NULs
+        try {
+            TarUtils.parseOctal(buffer,0, buffer.length);
+            fail("Expected IllegalArgumentException - not all NULs");
+        } catch (IllegalArgumentException expected) {
+        }
         buffer = "abcdef ".getBytes(CharsetNames.UTF_8); // Invalid input
         try {
             TarUtils.parseOctal(buffer,0, buffer.length);
