diff --git a/src/test/java/org/apache/commons/compress/DetectArchiverTestCase.java b/src/test/java/org/apache/commons/compress/DetectArchiverTestCase.java
index 8e1e7bf7..ad189023 100644
--- a/src/test/java/org/apache/commons/compress/DetectArchiverTestCase.java
+++ b/src/test/java/org/apache/commons/compress/DetectArchiverTestCase.java
@@ -55,13 +55,6 @@ public final class DetectArchiverTestCase extends AbstractTestCase {
         assertTrue(tar instanceof TarArchiveInputStream);
     }
 
-    @Test
-    public void testCOMPRESS335() throws Exception {
-        final ArchiveInputStream tar = getStreamFor("COMPRESS-335.tar");
-        assertNotNull(tar);
-        assertTrue(tar instanceof TarArchiveInputStream);
-    }
-
     @Test
     public void testDetection() throws Exception {
 
