diff --git a/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java b/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java
index 6ddb5262..eb8b549f 100644
--- a/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveInputStreamTest.java
@@ -18,16 +18,10 @@
 
 package org.apache.commons.compress.archivers.tar;
 
-import java.io.File;
-import java.io.FileInputStream;
 import java.io.StringReader;
-import java.net.URI;
-import java.net.URL;
-import java.util.Date;
 import java.util.Map;
 import org.junit.Test;
 import static org.junit.Assert.assertEquals;
-import static org.junit.Assert.assertTrue;
 
 public class TarArchiveInputStreamTest {
 
@@ -46,23 +40,4 @@ public class TarArchiveInputStreamTest {
         assertEquals(1, headers.size());
         assertEquals("line1\nline2\nand3", headers.get("comment"));
     }
-
-    @Test
-    public void workaroundForBrokenTimeHeader() throws Exception {
-        URL tar = getClass().getResource("/simple-aix-native-tar.tar");
-        TarArchiveInputStream in = null;
-        try {
-            in = new TarArchiveInputStream(new FileInputStream(new File(new URI(tar.toString()))));
-            TarArchiveEntry tae = in.getNextTarEntry();
-            tae = in.getNextTarEntry();
-            assertEquals("sample/link-to-txt-file.lnk", tae.getName());
-            assertEquals(new Date(0), tae.getLastModifiedDate());
-            assertTrue(tae.isSymbolicLink());
-        } finally {
-            if (in != null) {
-                in.close();
-            }
-        }
-    }        
-
 }
\ No newline at end of file
diff --git a/src/test/java/org/apache/commons/compress/archivers/tar/TarUtilsTest.java b/src/test/java/org/apache/commons/compress/archivers/tar/TarUtilsTest.java
index 28f11e09..93977675 100644
--- a/src/test/java/org/apache/commons/compress/archivers/tar/TarUtilsTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/tar/TarUtilsTest.java
@@ -72,6 +72,12 @@ public class TarUtilsTest extends TestCase {
             fail("Expected IllegalArgumentException - should be at least 2 bytes long");
         } catch (IllegalArgumentException expected) {
         }
+        buffer=new byte[]{0,0,' '}; // not all NULs
+        try {
+            TarUtils.parseOctal(buffer,0, buffer.length);
+            fail("Expected IllegalArgumentException - not all NULs");
+        } catch (IllegalArgumentException expected) {
+        }
         buffer=new byte[]{' ',0,0,0}; // not all NULs
         try {
             TarUtils.parseOctal(buffer,0, buffer.length);
