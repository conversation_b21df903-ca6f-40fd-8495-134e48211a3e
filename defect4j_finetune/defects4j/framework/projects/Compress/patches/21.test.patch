diff --git a/src/test/java/org/apache/commons/compress/archivers/sevenz/SevenZOutputFileTest.java b/src/test/java/org/apache/commons/compress/archivers/sevenz/SevenZOutputFileTest.java
index af83d953..519bde30 100644
--- a/src/test/java/org/apache/commons/compress/archivers/sevenz/SevenZOutputFileTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/sevenz/SevenZOutputFileTest.java
@@ -171,113 +171,4 @@ public class SevenZOutputFileTest extends AbstractTestCase {
         }
     }
 
-    public void testSixEmptyFiles() throws Exception {
-        testCompress252(6, 0);
-    }
-
-    public void testSixFilesSomeNotEmpty() throws Exception {
-        testCompress252(6, 2);
-    }
-
-    public void testSevenEmptyFiles() throws Exception {
-        testCompress252(7, 0);
-    }
-
-    public void testSevenFilesSomeNotEmpty() throws Exception {
-        testCompress252(7, 2);
-    }
-
-    public void testEightEmptyFiles() throws Exception {
-        testCompress252(8, 0);
-    }
-
-    public void testEightFilesSomeNotEmpty() throws Exception {
-        testCompress252(8, 2);
-    }
-
-    public void testNineEmptyFiles() throws Exception {
-        testCompress252(9, 0);
-    }
-
-    public void testNineFilesSomeNotEmpty() throws Exception {
-        testCompress252(9, 2);
-    }
-
-    private void testCompress252(int numberOfFiles, int numberOfNonEmptyFiles)
-        throws Exception {
-        int nonEmptyModulus = numberOfNonEmptyFiles != 0
-            ? numberOfFiles / numberOfNonEmptyFiles
-            : numberOfFiles + 1;
-        output = new File(dir, "COMPRESS252-" + numberOfFiles + "-" + numberOfNonEmptyFiles + ".7z");
-        SevenZOutputFile archive = new SevenZOutputFile(output);
-        try {
-            addDir(archive);
-            for (int i = 0; i < numberOfFiles; i++) {
-                addFile(archive, i, (i + 1) % nonEmptyModulus == 0);
-            }
-        } finally {
-            archive.close();
-        }
-        verifyCompress252(output, numberOfFiles, numberOfNonEmptyFiles);
-    }
-
-    private void verifyCompress252(File output, int numberOfFiles, int numberOfNonEmptyFiles)
-        throws Exception {
-        SevenZFile archive = new SevenZFile(output);
-        int filesFound = 0;
-        int nonEmptyFilesFound = 0;
-        try {
-            verifyDir(archive);
-            Boolean b = verifyFile(archive, filesFound++);
-            while (b != null) {
-                if (Boolean.TRUE.equals(b)) {
-                    nonEmptyFilesFound++;
-                }
-                b = verifyFile(archive, filesFound++);
-            }
-        } finally {
-            archive.close();
-        }
-        assertEquals(numberOfFiles + 1, filesFound);
-        assertEquals(numberOfNonEmptyFiles, nonEmptyFilesFound);
-    }
-
-    private void addDir(SevenZOutputFile archive) throws Exception {
-        SevenZArchiveEntry entry = archive.createArchiveEntry(dir, "foo/");
-        archive.putArchiveEntry(entry);
-        archive.closeArchiveEntry();
-    }
-
-    private void verifyDir(SevenZFile archive) throws Exception {
-        SevenZArchiveEntry entry = archive.getNextEntry();
-        assertNotNull(entry);
-        assertEquals("foo/", entry.getName());
-        assertTrue(entry.isDirectory());
-    }
-
-    private void addFile(SevenZOutputFile archive, int index, boolean nonEmpty)
-        throws Exception {
-        SevenZArchiveEntry entry = new SevenZArchiveEntry();
-        entry.setName("foo/" + index + ".txt");
-        archive.putArchiveEntry(entry);
-        archive.write(nonEmpty ? new byte[] { 17 } : new byte[0]);
-        archive.closeArchiveEntry();
-    }
-
-    private Boolean verifyFile(SevenZFile archive, int index) throws Exception {
-        SevenZArchiveEntry entry = archive.getNextEntry();
-        if (entry == null) {
-            return null;
-        }
-        assertEquals("foo/" + index + ".txt", entry.getName());
-        assertEquals(false, entry.isDirectory());
-        if (entry.getSize() == 0) {
-            return false;
-        }
-        assertEquals(1, entry.getSize());
-        assertEquals(17, archive.read());
-        assertEquals(-1, archive.read());
-        return true;
-    }
-
 }
