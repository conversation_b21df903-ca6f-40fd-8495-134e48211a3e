diff --git a/src/main/java/org/apache/commons/compress/archivers/sevenz/Coders.java b/src/main/java/org/apache/commons/compress/archivers/sevenz/Coders.java
index 5e4f3e9d..0bc5a1ee 100644
--- a/src/main/java/org/apache/commons/compress/archivers/sevenz/Coders.java
+++ b/src/main/java/org/apache/commons/compress/archivers/sevenz/Coders.java
@@ -109,7 +109,7 @@ class Coders {
             byte propsByte = coder.properties[0];
             long dictSize = coder.properties[1];
             for (int i = 1; i < 4; i++) {
-                dictSize |= (coder.properties[i + 1] & 0xffl) << (8 * i);
+                dictSize |= (coder.properties[i + 1] << (8 * i));
             }
             if (dictSize > LZMAInputStream.DICT_SIZE_MAX) {
                 throw new IOException("Dictionary larger than 4GiB maximum size");
