diff --git a/src/test/java/org/apache/commons/compress/archivers/zip/X5455_ExtendedTimestampTest.java b/src/test/java/org/apache/commons/compress/archivers/zip/X5455_ExtendedTimestampTest.java
index a0d9666c..06c77d3c 100644
--- a/src/test/java/org/apache/commons/compress/archivers/zip/X5455_ExtendedTimestampTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/zip/X5455_ExtendedTimestampTest.java
@@ -50,7 +50,7 @@ public class X5455_ExtendedTimestampTest {
     private final static ZipShort X5455 = new ZipShort(0x5455);
 
     private final static ZipLong ZERO_TIME = new ZipLong(0);
-    private final static ZipLong MAX_TIME_SECONDS = new ZipLong(Integer.MAX_VALUE);
+    private final static ZipLong MAX_TIME_SECONDS = new ZipLong(0xFFFFFFFFL);
     private final static SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd/HH:mm:ss Z");
 
     static {
@@ -142,19 +142,51 @@ public class X5455_ExtendedTimestampTest {
                     }
                     if (year >= 0) {
                         switch (year) {
+                            case 2107:
+                                if (!zipTimeUsesExtendedTimestamp) {
+                                    // Zip time is okay up to 2107.
+                                    assertEquals(year + "-01-01/00:00:02 +0000", zipTime);
+                                }
+                                // But the X5455 data has overflowed:
+                                assertEquals("1970-11-24/17:31:45 +0000", modTime);
+                                assertEquals("1970-11-24/17:31:47 +0000", accTime);
+                                break;
+                            case 2108:
+                                if (!zipTimeUsesExtendedTimestamp) {
+                                    // Zip time is still okay at Jan 1st midnight (UTC) in 2108
+                                    // because we created the zip file in pacific time zone, so it's
+                                    // actually still 2107 in the zip file!
+                                    assertEquals(year + "-01-01/00:00:02 +0000", zipTime);
+                                }
+                                // The X5455 data is still overflowed, of course:
+                                assertEquals("1971-11-24/17:31:45 +0000", modTime);
+                                assertEquals("1971-11-24/17:31:47 +0000", accTime);
+                                break;
+                            case 2109:
+                                // All three timestamps have overflowed by 2109.
+                                if (!zipTimeUsesExtendedTimestamp) {
+                                    assertEquals("1981-01-01/00:00:02 +0000", zipTime);
+                                }
+                                assertEquals("1972-11-24/17:31:45 +0000", modTime);
+                                assertEquals("1972-11-24/17:31:47 +0000", accTime);
+
+                                // Hmmm.... looks like one could examine both DOS time
+                                // and the Unix time together to hack a nice workaround to
+                                // get timestamps past 2106 in a reverse-compatible way.
+
+                                break;
                             default:
                                 if (!zipTimeUsesExtendedTimestamp) {
-                                    // X5455 time is good from epoch (1970) to 2037.
+                                    // X5455 time is good from epoch (1970) to 2106.
                                     // Zip time is good from 1980 to 2107.
                                     if (year < 1980) {
                                         assertEquals("1980-01-01/08:00:00 +0000", zipTime);
                                     } else {
                                         assertEquals(year + "-01-01/00:00:02 +0000", zipTime);
                                     }
-                                }if(year <2038) {
+                                }
                                 assertEquals(year + "-01-01/00:00:01 +0000", modTime);
                                 assertEquals(year + "-01-01/00:00:03 +0000", accTime);
-                            }
                                 break;
                         }
                     }
@@ -203,10 +235,9 @@ public class X5455_ExtendedTimestampTest {
         cal.set(Calendar.DATE, 1);
         cal.set(Calendar.HOUR_OF_DAY, 0);
         cal.set(Calendar.MINUTE, 0);
-        cal.set(Calendar.SECOND, 0);
         cal.set(Calendar.MILLISECOND, 0);
-        final long timeMillis = cal.getTimeInMillis();
-        final ZipLong time = new ZipLong(timeMillis / 1000);
+        final Date timeMillis = cal.getTime();
+        final ZipLong time = new ZipLong(timeMillis.getTime() / 1000);
 
         // set too big
         try {
@@ -220,15 +251,14 @@ public class X5455_ExtendedTimestampTest {
         // get/set modify time
         xf.setModifyTime(time);
         assertEquals(time, xf.getModifyTime());
-        Date xfModifyJavaTime = xf.getModifyJavaTime();
-        assertEquals(timeMillis, xfModifyJavaTime.getTime());
-        xf.setModifyJavaTime(new Date(timeMillis));
+        assertEquals(timeMillis, xf.getModifyJavaTime());
+        xf.setModifyJavaTime(timeMillis);
         assertEquals(time, xf.getModifyTime());
-        assertEquals(timeMillis, xf.getModifyJavaTime().getTime());
+        assertEquals(timeMillis, xf.getModifyJavaTime());
         // Make sure milliseconds get zeroed out:
-        xf.setModifyJavaTime(new Date(timeMillis + 123));
+        xf.setModifyJavaTime(new Date(timeMillis.getTime() + 123));
         assertEquals(time, xf.getModifyTime());
-        assertEquals(timeMillis, xf.getModifyJavaTime().getTime());
+        assertEquals(timeMillis, xf.getModifyJavaTime());
         // Null
         xf.setModifyTime(null);
         assertNull(xf.getModifyJavaTime());
@@ -238,14 +268,14 @@ public class X5455_ExtendedTimestampTest {
         // get/set access time
         xf.setAccessTime(time);
         assertEquals(time, xf.getAccessTime());
-        assertEquals(timeMillis, xf.getAccessJavaTime().getTime());
-        xf.setAccessJavaTime(new Date(timeMillis));
+        assertEquals(timeMillis, xf.getAccessJavaTime());
+        xf.setAccessJavaTime(timeMillis);
         assertEquals(time, xf.getAccessTime());
-        assertEquals(timeMillis, xf.getAccessJavaTime().getTime());
+        assertEquals(timeMillis, xf.getAccessJavaTime());
         // Make sure milliseconds get zeroed out:
-        xf.setAccessJavaTime(new Date(timeMillis + 123));
+        xf.setAccessJavaTime(new Date(timeMillis.getTime() + 123));
         assertEquals(time, xf.getAccessTime());
-        assertEquals(timeMillis, xf.getAccessJavaTime().getTime());
+        assertEquals(timeMillis, xf.getAccessJavaTime());
         // Null
         xf.setAccessTime(null);
         assertNull(xf.getAccessJavaTime());
@@ -255,14 +285,14 @@ public class X5455_ExtendedTimestampTest {
         // get/set create time
         xf.setCreateTime(time);
         assertEquals(time, xf.getCreateTime());
-        assertEquals(timeMillis, xf.getCreateJavaTime().getTime());
-        xf.setCreateJavaTime(new Date(timeMillis));
+        assertEquals(timeMillis, xf.getCreateJavaTime());
+        xf.setCreateJavaTime(timeMillis);
         assertEquals(time, xf.getCreateTime());
-        assertEquals(timeMillis, xf.getCreateJavaTime().getTime());
+        assertEquals(timeMillis, xf.getCreateJavaTime());
         // Make sure milliseconds get zeroed out:
-        xf.setCreateJavaTime(new Date(timeMillis + 123));
+        xf.setCreateJavaTime(new Date(timeMillis.getTime() + 123));
         assertEquals(time, xf.getCreateTime());
-        assertEquals(timeMillis, xf.getCreateJavaTime().getTime());
+        assertEquals(timeMillis, xf.getCreateJavaTime());
         // Null
         xf.setCreateTime(null);
         assertNull(xf.getCreateJavaTime());
@@ -358,15 +388,15 @@ public class X5455_ExtendedTimestampTest {
         final byte[] CR_CENTRAL = {4}; // central data only dontains the CR flag and no actual data
 
         final byte[] MOD_ZERO = {1, 0, 0, 0, 0};
-        final byte[] MOD_MAX = {1, -1, -1, -1, 0x7f};
+        final byte[] MOD_MAX = {1, -1, -1, -1, -1};
         final byte[] AC_ZERO = {2, 0, 0, 0, 0};
-        final byte[] AC_MAX = {2, -1, -1, -1, 0x7f};
+        final byte[] AC_MAX = {2, -1, -1, -1, -1};
         final byte[] CR_ZERO = {4, 0, 0, 0, 0};
-        final byte[] CR_MAX = {4, -1, -1, -1, 0x7f};
+        final byte[] CR_MAX = {4, -1, -1, -1, -1};
         final byte[] MOD_AC_ZERO = {3, 0, 0, 0, 0, 0, 0, 0, 0};
-        final byte[] MOD_AC_MAX = {3, -1, -1, -1, 0x7f, -1, -1, -1, 0x7f};
+        final byte[] MOD_AC_MAX = {3, -1, -1, -1, -1, -1, -1, -1, -1};
         final byte[] MOD_AC_CR_ZERO = {7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
-        final byte[] MOD_AC_CR_MAX = {7, -1, -1, -1, 0x7f, -1, -1, -1, 0x7f, -1, -1, -1, 0x7f};
+        final byte[] MOD_AC_CR_MAX = {7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1};
 
         parseReparse(null, NULL_FLAGS, NULL_FLAGS);
         parseReparse(ZERO_TIME, MOD_ZERO, MOD_ZERO);
