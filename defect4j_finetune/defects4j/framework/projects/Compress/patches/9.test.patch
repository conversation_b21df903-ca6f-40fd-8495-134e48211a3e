diff --git a/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveOutputStreamTest.java b/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveOutputStreamTest.java
deleted file mode 100644
index 6241cde3..00000000
--- a/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveOutputStreamTest.java
+++ /dev/null
@@ -1,57 +0,0 @@
-/*
- *  Licensed to the Apache Software Foundation (ASF) under one or more
- *  contributor license agreements.  See the NOTICE file distributed with
- *  this work for additional information regarding copyright ownership.
- *  The ASF licenses this file to You under the Apache License, Version 2.0
- *  (the "License"); you may not use this file except in compliance with
- *  the License.  You may obtain a copy of the License at
- *
- *      http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- *
- */
-
-package org.apache.commons.compress.archivers.tar;
-
-import java.io.File;
-import java.io.FileInputStream;
-import java.io.FileOutputStream;
-
-import org.apache.commons.compress.AbstractTestCase;
-import org.apache.commons.compress.archivers.ArchiveOutputStream;
-import org.apache.commons.compress.archivers.ArchiveStreamFactory;
-
-public class TarArchiveOutputStreamTest extends AbstractTestCase {
-
-    public void testCount() throws Exception {
-        File f = File.createTempFile("commons-compress-tarcount", ".tar");
-        f.deleteOnExit();
-        FileOutputStream fos = new FileOutputStream(f);
-
-        ArchiveOutputStream tarOut = new ArchiveStreamFactory()
-            .createArchiveOutputStream(ArchiveStreamFactory.TAR, fos);
-
-        File file1 = getFile("test1.xml");
-        TarArchiveEntry sEntry = new TarArchiveEntry(file1);
-        tarOut.putArchiveEntry(sEntry);
-
-        FileInputStream in = new FileInputStream(file1);
-        byte[] buf = new byte[8192];
-
-        int read = 0;
-        while ((read = in.read(buf)) > 0) {
-            tarOut.write(buf, 0, read);
-        }
-
-        in.close();
-        tarOut.closeArchiveEntry();
-        tarOut.close();
-
-        assertEquals(f.length(), tarOut.getBytesWritten());
-    }
-}
\ No newline at end of file
