diff --git a/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntryTest.java b/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntryTest.java
index 1ce7822a..c50defe4 100644
--- a/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntryTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntryTest.java
@@ -219,14 +219,4 @@ public class ZipArchiveEntryTest extends TestCase {
         assertFalse(entry.isSupportedCompressionMethod());
     }
 
-    /**
-     * Test case for
-     * <a href="https://issues.apache.org/jira/browse/COMPRESS-94"
-     * >COMPRESS-94</a>.
-     */
-    public void testNotEquals() {
-        ZipArchiveEntry entry1 = new ZipArchiveEntry("foo");
-        ZipArchiveEntry entry2 = new ZipArchiveEntry("bar");
-        assertFalse(entry1.equals(entry2));
-    }
 }
