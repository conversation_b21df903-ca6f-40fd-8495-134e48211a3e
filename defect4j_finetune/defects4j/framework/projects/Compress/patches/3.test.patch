diff --git a/src/test/java/org/apache/commons/compress/IOMethodsTest.java b/src/test/java/org/apache/commons/compress/IOMethodsTest.java
index eabf80d1..2dce20f9 100644
--- a/src/test/java/org/apache/commons/compress/IOMethodsTest.java
+++ b/src/test/java/org/apache/commons/compress/IOMethodsTest.java
@@ -104,20 +104,14 @@ public class IOMethodsTest extends AbstractTestCase {
         for (int i=0; i<byteTest.length; i++){
             aos1.write(byteTest[i]);            
         }
-        aos1.closeArchiveEntry();
         aos1.close();
-
         aos2.write(byteTest);
-        aos2.closeArchiveEntry();
         aos2.close();
-        
         aos3.write(byteTest, 0, byteTest.length);
-        aos3.closeArchiveEntry();
         aos3.close();
         assertEquals("out1!=out2",out1.toString(),out2.toString());
         assertEquals("out1!=out3",out1.toString(),out3.toString());
     }
-    
     private void compareReads(String archiverName) throws Exception {
         OutputStream out1 = new ByteArrayOutputStream();
         OutputStream out2 = new ByteArrayOutputStream();
diff --git a/src/test/java/org/apache/commons/compress/archivers/ArchiveOutputStreamTest.java b/src/test/java/org/apache/commons/compress/archivers/ArchiveOutputStreamTest.java
deleted file mode 100644
index 7a273baa..00000000
--- a/src/test/java/org/apache/commons/compress/archivers/ArchiveOutputStreamTest.java
+++ /dev/null
@@ -1,73 +0,0 @@
-package org.apache.commons.compress.archivers;
-
-import java.io.ByteArrayOutputStream;
-import java.io.IOException;
-import java.io.OutputStream;
-
-import org.apache.commons.compress.AbstractTestCase;
-import org.apache.commons.compress.archivers.ar.ArArchiveEntry;
-import org.apache.commons.compress.archivers.cpio.CpioArchiveEntry;
-import org.apache.commons.compress.archivers.jar.JarArchiveEntry;
-import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
-import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
-
-public class ArchiveOutputStreamTest extends AbstractTestCase {
-
-    protected void setUp() throws Exception {
-        super.setUp();
-    }
-
-    protected void tearDown() throws Exception {
-        super.tearDown();
-    }
-
-    public void testFinish() throws Exception {
-        OutputStream out1 = new ByteArrayOutputStream();
-        
-        ArchiveOutputStream aos1 = factory.createArchiveOutputStream("zip", out1);
-        aos1.putArchiveEntry(new ZipArchiveEntry("dummy"));
-        try {
-            aos1.finish();
-            fail("After putArchive should follow closeArchive");
-        } catch (IOException io) {
-            // Exception expected
-        }
-        
-        aos1 = factory.createArchiveOutputStream("jar", out1);
-        aos1.putArchiveEntry(new JarArchiveEntry("dummy"));
-        try {
-            aos1.finish();
-            fail("After putArchive should follow closeArchive");
-        } catch (IOException io) {
-            // Exception expected
-        }
-        
-        aos1 = factory.createArchiveOutputStream("ar", out1);
-        aos1.putArchiveEntry(new ArArchiveEntry("dummy", 100));
-        try {
-            aos1.finish();
-            fail("After putArchive should follow closeArchive");
-        } catch (IOException io) {
-            // Exception expected
-        }
-        
-        aos1 = factory.createArchiveOutputStream("cpio", out1);
-        aos1.putArchiveEntry(new CpioArchiveEntry("dummy"));
-        try {
-            aos1.finish();
-            fail("After putArchive should follow closeArchive");
-        } catch (IOException io) {
-            // Exception expected
-        }
-        
-        aos1 = factory.createArchiveOutputStream("tar", out1);
-        aos1.putArchiveEntry(new TarArchiveEntry("dummy"));
-        try {
-            aos1.finish();
-            fail("After putArchive should follow closeArchive");
-        } catch (IOException io) {
-            // Exception expected
-        }
-    }
-
-}
