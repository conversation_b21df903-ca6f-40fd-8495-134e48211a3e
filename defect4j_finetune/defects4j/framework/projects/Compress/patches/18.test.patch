diff --git a/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveOutputStreamTest.java b/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveOutputStreamTest.java
index 7a508206..4ab18057 100644
--- a/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveOutputStreamTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/tar/TarArchiveOutputStreamTest.java
@@ -372,59 +372,4 @@ public class TarArchiveOutputStreamTest extends AbstractTestCase {
         tin.close();
     }
 
-    /**
-     * @see https://issues.apache.org/jira/browse/COMPRESS-203
-     */
-    public void testWriteLongDirectoryNameGnuMode() throws Exception {
-        testWriteLongDirectoryName(TarArchiveOutputStream.LONGFILE_GNU);
-    }
-
-    /**
-     * @see https://issues.apache.org/jira/browse/COMPRESS-203
-     */
-    public void testWriteLongDirectoryNamePosixMode() throws Exception {
-        testWriteLongDirectoryName(TarArchiveOutputStream.LONGFILE_POSIX);
-    }
-
-    private void testWriteLongDirectoryName(int mode) throws Exception {
-        String n = "01234567890123456789012345678901234567890123456789"
-            + "01234567890123456789012345678901234567890123456789"
-            + "01234567890123456789012345678901234567890123456789/";
-        TarArchiveEntry t = new TarArchiveEntry(n);
-        ByteArrayOutputStream bos = new ByteArrayOutputStream();
-        TarArchiveOutputStream tos = new TarArchiveOutputStream(bos, "ASCII");
-        tos.setLongFileMode(mode);
-        tos.putArchiveEntry(t);
-        tos.closeArchiveEntry();
-        tos.close();
-        byte[] data = bos.toByteArray();
-        TarArchiveInputStream tin =
-            new TarArchiveInputStream(new ByteArrayInputStream(data));
-        TarArchiveEntry e = tin.getNextTarEntry();
-        assertEquals(n, e.getName());
-        assertTrue(e.isDirectory());
-        tin.close();
-    }
-
-    /**
-     * @see https://issues.apache.org/jira/browse/COMPRESS-203
-     */
-    public void testWriteNonAsciiDirectoryNamePosixMode() throws Exception {
-        String n = "f\u00f6\u00f6/";
-        TarArchiveEntry t = new TarArchiveEntry(n);
-        ByteArrayOutputStream bos = new ByteArrayOutputStream();
-        TarArchiveOutputStream tos = new TarArchiveOutputStream(bos);
-        tos.setAddPaxHeadersForNonAsciiNames(true);
-        tos.putArchiveEntry(t);
-        tos.closeArchiveEntry();
-        tos.close();
-        byte[] data = bos.toByteArray();
-        TarArchiveInputStream tin =
-            new TarArchiveInputStream(new ByteArrayInputStream(data));
-        TarArchiveEntry e = tin.getNextTarEntry();
-        assertEquals(n, e.getName());
-        assertTrue(e.isDirectory());
-        tin.close();
-    }
-
 }
