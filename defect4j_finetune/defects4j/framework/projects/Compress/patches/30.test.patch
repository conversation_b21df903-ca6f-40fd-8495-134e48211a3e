diff --git a/src/test/java/org/apache/commons/compress/compressors/bzip2/BZip2CompressorInputStreamTest.java b/src/test/java/org/apache/commons/compress/compressors/bzip2/BZip2CompressorInputStreamTest.java
index d15d74a6..2ee4567d 100644
--- a/src/test/java/org/apache/commons/compress/compressors/bzip2/BZip2CompressorInputStreamTest.java
+++ b/src/test/java/org/apache/commons/compress/compressors/bzip2/BZip2CompressorInputStreamTest.java
@@ -20,11 +20,8 @@ package org.apache.commons.compress.compressors.bzip2;
 
 import static org.apache.commons.compress.AbstractTestCase.getFile;
 
-import java.io.ByteArrayInputStream;
-import java.io.ByteArrayOutputStream;
 import java.io.FileInputStream;
 import java.io.IOException;
-import org.junit.Assert;
 import org.junit.Test;
 
 public class BZip2CompressorInputStreamTest {
@@ -39,34 +36,4 @@ public class BZip2CompressorInputStreamTest {
         }
     }
 
-    /**
-     * @see "https://issues.apache.org/jira/browse/COMPRESS-309"
-     */
-    @Test
-    public void readOfLength0ShouldReturn0() throws Exception {
-        // Create a big random piece of data
-        byte[] rawData = new byte[1048576];
-        for (int i=0; i < rawData.length; ++i) {
-            rawData[i] = (byte) Math.floor(Math.random()*256);
-        }
-
-        // Compress it
-        ByteArrayOutputStream baos = new ByteArrayOutputStream();
-        BZip2CompressorOutputStream bzipOut = new BZip2CompressorOutputStream(baos);
-        bzipOut.write(rawData);
-        bzipOut.flush();
-        bzipOut.close();
-        baos.flush();
-        baos.close();
-
-        // Try to read it back in
-        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
-        BZip2CompressorInputStream bzipIn = new BZip2CompressorInputStream(bais);
-        byte[] buffer = new byte[1024];
-        Assert.assertEquals(1024, bzipIn.read(buffer, 0, 1024));
-        Assert.assertEquals(0, bzipIn.read(buffer, 1024, 0));
-        Assert.assertEquals(1024, bzipIn.read(buffer, 0, 1024));
-        bzipIn.close();
-    }
-
 }
