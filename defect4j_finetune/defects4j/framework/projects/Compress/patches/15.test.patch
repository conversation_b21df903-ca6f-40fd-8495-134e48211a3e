diff --git a/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntryTest.java b/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntryTest.java
index c6561c14..9275c3c8 100644
--- a/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntryTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntryTest.java
@@ -235,20 +235,4 @@ public class ZipArchiveEntryTest extends TestCase {
         ZipArchiveEntry entry2 = new ZipArchiveEntry("bar");
         assertFalse(entry1.equals(entry2));
     }
-
-    /**
-     * Tests comment's influence on equals comparisons.
-     * @see https://issues.apache.org/jira/browse/COMPRESS-187
-     */
-    public void testNullCommentEqualsEmptyComment() {
-        ZipArchiveEntry entry1 = new ZipArchiveEntry("foo");
-        ZipArchiveEntry entry2 = new ZipArchiveEntry("foo");
-        ZipArchiveEntry entry3 = new ZipArchiveEntry("foo");
-        entry1.setComment(null);
-        entry2.setComment("");
-        entry3.setComment("bar");
-        assertEquals(entry1, entry2);
-        assertFalse(entry1.equals(entry3));
-        assertFalse(entry2.equals(entry3));
-    }
 }
