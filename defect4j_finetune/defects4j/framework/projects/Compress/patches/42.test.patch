diff --git a/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntryTest.java b/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntryTest.java
index be003df1..320b2ffd 100644
--- a/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntryTest.java
+++ b/src/test/java/org/apache/commons/compress/archivers/zip/ZipArchiveEntryTest.java
@@ -18,7 +18,6 @@
 
 package org.apache.commons.compress.archivers.zip;
 
-import static org.apache.commons.compress.AbstractTestCase.getFile;
 import static org.junit.Assert.*;
 
 import java.io.ByteArrayOutputStream;
@@ -269,15 +268,4 @@ public class ZipArchiveEntryTest {
         final ZipArchiveEntry copy = new ZipArchiveEntry(archiveEntry);
         assertEquals(archiveEntry, copy);
     }
-
-    /**
-     * @see "https://issues.apache.org/jira/browse/COMPRESS-379"
-     */
-    @Test
-    public void isUnixSymlinkIsFalseIfMoreThanOneFlagIsSet() throws Exception {
-        try (ZipFile zf = new ZipFile(getFile("COMPRESS-379.jar"))) {
-            ZipArchiveEntry ze = zf.getEntry("META-INF/maven/");
-            assertFalse(ze.isUnixSymlink());
-        }
-    }
 }
