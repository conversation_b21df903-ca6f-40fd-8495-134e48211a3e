diff --git a/src/test/java/org/apache/commons/compress/archivers/CpioTestCase.java b/src/test/java/org/apache/commons/compress/archivers/CpioTestCase.java
index aa0f68c2..1f724e1e 100644
--- a/src/test/java/org/apache/commons/compress/archivers/CpioTestCase.java
+++ b/src/test/java/org/apache/commons/compress/archivers/CpioTestCase.java
@@ -23,8 +23,6 @@ import java.io.FileInputStream;
 import java.io.FileOutputStream;
 import java.io.InputStream;
 import java.io.OutputStream;
-import java.util.HashMap;
-import java.util.Map;
 
 import org.apache.commons.compress.AbstractTestCase;
 import org.apache.commons.compress.archivers.cpio.CpioArchiveEntry;
@@ -32,73 +30,56 @@ import org.apache.commons.compress.utils.IOUtils;
 
 public final class CpioTestCase extends AbstractTestCase {
 
-    public void testCpioArchiveCreation() throws Exception {
-        final File output = new File(dir, "bla.cpio");
-
-        final File file1 = getFile("test1.xml");
-        final File file2 = getFile("test2.xml");
-
-        final OutputStream out = new FileOutputStream(output);
+	public void testCpioArchiveCreation() throws Exception {
+		final File output = new File(dir, "bla.cpio");
+		
+		final File file1 = getFile("test1.xml");
+		final File file2 = getFile("test2.xml");
+		
+		final OutputStream out = new FileOutputStream(output);
         final ArchiveOutputStream os = new ArchiveStreamFactory().createArchiveOutputStream("cpio", out);
-        os.putArchiveEntry(new CpioArchiveEntry("test1.xml", file1.length()));
-        IOUtils.copy(new FileInputStream(file1), os);
-        os.closeArchiveEntry();
-
-        os.putArchiveEntry(new CpioArchiveEntry("test2.xml", file2.length()));
-        IOUtils.copy(new FileInputStream(file2), os);
-        os.closeArchiveEntry();
-
-        os.close();
-    }
-
-    public void testCpioUnarchive() throws Exception {
-        final File output = new File(dir, "bla.cpio");
-        {
-            final File file1 = getFile("test1.xml");
-            final File file2 = getFile("test2.xml");
-
-            final OutputStream out = new FileOutputStream(output);
-            final ArchiveOutputStream os = new ArchiveStreamFactory().createArchiveOutputStream("cpio", out);
-            os.putArchiveEntry(new CpioArchiveEntry("test1.xml", file1.length()));
-            IOUtils.copy(new FileInputStream(file1), os);
-            os.closeArchiveEntry();
-
-            os.putArchiveEntry(new CpioArchiveEntry("test2.xml", file2.length()));
-            IOUtils.copy(new FileInputStream(file2), os);
-            os.closeArchiveEntry();
-
-            os.close();
-            out.close();
-        }
-
-        // Unarchive Operation
-        final File input = output;
-        final InputStream is = new FileInputStream(input);
-        final ArchiveInputStream in = new ArchiveStreamFactory().createArchiveInputStream("cpio", is);
-
-
-        Map result = new HashMap();
-        ArchiveEntry entry = null;
-        while ((entry = in.getNextEntry()) != null) {
-            File target = new File(dir, entry.getName());
-            final OutputStream out = new FileOutputStream(target);
-            IOUtils.copy(in, out);
-            out.close();
-            result.put(entry.getName(), target);
-        }
+		os.putArchiveEntry(new CpioArchiveEntry("test1.xml", file1.length()));
+		IOUtils.copy(new FileInputStream(file1), os);
+		os.closeArchiveEntry();
+		
+		os.putArchiveEntry(new CpioArchiveEntry("test2.xml", file2.length()));
+		IOUtils.copy(new FileInputStream(file2), os);
+		os.closeArchiveEntry();
+		
+		os.close();
+	}
+
+	public void testCpioUnarchive() throws Exception {
+		final File output = new File(dir, "bla.cpio");
+		{
+			final File file1 = getFile("test1.xml");
+			final File file2 = getFile("test2.xml");
+			
+			final OutputStream out = new FileOutputStream(output);
+	        final ArchiveOutputStream os = new ArchiveStreamFactory().createArchiveOutputStream("cpio", out);
+			os.putArchiveEntry(new CpioArchiveEntry("test1.xml", file1.length()));
+			IOUtils.copy(new FileInputStream(file1), os);
+			os.closeArchiveEntry();
+			
+			os.putArchiveEntry(new CpioArchiveEntry("test2.xml", file2.length()));
+			IOUtils.copy(new FileInputStream(file2), os);
+			os.closeArchiveEntry();
+			os.close();
+		}
+		
+		// Unarchive Operation
+		final File input = output;
+		final InputStream is = new FileInputStream(input);
+		final ArchiveInputStream in = new ArchiveStreamFactory().createArchiveInputStream("cpio", is);
+		final CpioArchiveEntry entry = (CpioArchiveEntry)in.getNextEntry();
+		
+		File target = new File(dir, entry.getName());
+        final OutputStream out = new FileOutputStream(target);
+        
+        IOUtils.copy(in, out);
+    
+        out.close();
         in.close();
-
-        int lineSepLength = System.getProperty("line.separator").length();
-
-        File t = (File)result.get("test1.xml");
-        assertTrue("Expected " + t.getAbsolutePath() + " to exist", t.exists());
-        assertEquals("length of " + t.getAbsolutePath(),
-                     72 + 4 * lineSepLength, t.length());
-
-        t = (File)result.get("test2.xml");
-        assertTrue("Expected " + t.getAbsolutePath() + " to exist", t.exists());
-        assertEquals("length of " + t.getAbsolutePath(),
-                     73 + 5 * lineSepLength, t.length());
-    }
+	}
 
 }
diff --git a/src/test/java/org/apache/commons/compress/archivers/cpio/CpioArchiveInputStreamTest.java b/src/test/java/org/apache/commons/compress/archivers/cpio/CpioArchiveInputStreamTest.java
deleted file mode 100644
index ee8aab7b..00000000
--- a/src/test/java/org/apache/commons/compress/archivers/cpio/CpioArchiveInputStreamTest.java
+++ /dev/null
@@ -1,31 +0,0 @@
-package org.apache.commons.compress.archivers.cpio;
-
-import java.io.FileInputStream;
-
-import org.apache.commons.compress.AbstractTestCase;
-
-public class CpioArchiveInputStreamTest extends AbstractTestCase {
-
-    public void testCpioUnarchive() throws Exception {
-        StringBuffer expected = new StringBuffer();
-        expected.append("./test1.xml<?xml version=\"1.0\"?>\n");
-        expected.append("<empty/>./test2.xml<?xml version=\"1.0\"?>\n");
-        expected.append("<empty/>\n");
-        
-
-        CpioArchiveInputStream in = 
-                new CpioArchiveInputStream(new FileInputStream(getFile("bla.cpio")));
-        CpioArchiveEntry entry= null;
-        
-        StringBuffer result = new StringBuffer();
-        while ((entry = (CpioArchiveEntry) in.getNextEntry()) != null) {
-            result.append(entry.getName());
-            int tmp;
-            while ((tmp = in.read()) != -1) {
-                result.append((char) tmp);
-             }
-         }
-         in.close();
-         assertEquals(result.toString(), expected.toString());
-    }    
-}
