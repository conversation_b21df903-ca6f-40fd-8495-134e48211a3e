#Generated by Maven Ant Plugin - DO NOT EDIT THIS FILE!
#Mon Nov 11 06:18:56 PST 2019
project.build.outputDirectory=${maven.build.outputDir}
project.build.directory=${maven.build.dir}
maven.test.reports=${maven.build.dir}/test-reports
maven.build.finalName=gson-1.6
maven.reporting.outputDirectory=${maven.build.dir}/site
maven.build.testResourceDir.0=gson/src/test/resources
maven.build.outputDir=${maven.build.dir}/classes
maven.build.resourceDir.0=gson/src/main/resources
maven.build.testOutputDir=${maven.build.dir}/test-classes
maven.repo.local=${user.home}/.m2/repository
maven.settings.offline=false
maven.build.dir=target
maven.settings.interactiveMode=true
maven.build.testDir.0=gson/src/test/java
maven.build.srcDir.0=gson/src/main/java
