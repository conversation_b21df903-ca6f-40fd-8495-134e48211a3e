#Generated by Maven Ant Plugin - DO NOT EDIT THIS FILE!
#Mon Nov 11 06:07:31 PST 2019
maven.settings.offline=false
maven.build.finalName=gson-2.7-SNAPSHOT
maven.build.resourceDir.0=gson/src/main/resources
maven.build.testOutputDir=${maven.build.dir}/test-classes
maven.build.testResourceDir.0=gson/src/test/resources
maven.reporting.outputDirectory=${maven.build.dir}/site
project.build.sourceEncoding=UTF-8
maven.build.srcDir.0=gson/src/main/java
sonatypeOssDistMgmtSnapshotsUrl=https\://oss.sonatype.org/content/repositories/snapshots/
project.build.directory=${maven.build.dir}
maven.test.reports=${maven.build.dir}/test-reports
maven.build.dir=target
java.version=1.6
project.build.outputDirectory=${maven.build.outputDir}
maven.build.testDir.0=gson/src/test/java
maven.settings.interactiveMode=true
maven.repo.local=${user.home}/.m2/repository
maven.build.outputDir=${maven.build.dir}/classes
