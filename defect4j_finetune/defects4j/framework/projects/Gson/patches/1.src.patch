diff --git a/gson/src/main/java/com/google/gson/TypeInfoFactory.java b/gson/src/main/java/com/google/gson/TypeInfoFactory.java
index 3085e8a..eaf3a10 100644
--- a/gson/src/main/java/com/google/gson/TypeInfoFactory.java
+++ b/gson/src/main/java/com/google/gson/TypeInfoFactory.java
@@ -90,16 +90,8 @@ final class TypeInfoFactory {
         int indexOfActualTypeArgument = getIndex(classTypeVariables, fieldTypeVariable);
         Type[] actualTypeArguments = objParameterizedType.getActualTypeArguments();
         return actualTypeArguments[indexOfActualTypeArgument];
-      } else if (typeToEvaluate instanceof TypeVariable<?>) {
-        Type theSearchedType = null;
 
-        do {
-          theSearchedType = extractTypeForHierarchy(parentType, (TypeVariable<?>) typeToEvaluate);
-        } while ((theSearchedType != null) && (theSearchedType instanceof TypeVariable<?>));
 
-        if (theSearchedType != null) {
-          return theSearchedType;
-        }
       }
 
       throw new UnsupportedOperationException("Expecting parameterized type, got " + parentType
@@ -114,43 +106,11 @@ final class TypeInfoFactory {
     }
   }
 
-  private static Type extractTypeForHierarchy(Type parentType, TypeVariable<?> typeToEvaluate) {
-    Class<?> rawParentType = null;
-    if (parentType instanceof Class<?>) {
-      rawParentType = (Class<?>) parentType;
-    } else if (parentType instanceof ParameterizedType) {
-      ParameterizedType parentTypeAsPT = (ParameterizedType) parentType;
-      rawParentType = (Class<?>) parentTypeAsPT.getRawType();
-    } else {
-      return null;
-    }
 
-    Type superClass = rawParentType.getGenericSuperclass();
-    if (superClass instanceof ParameterizedType
-        && ((ParameterizedType) superClass).getRawType() == typeToEvaluate.getGenericDeclaration()) {
       // Evaluate type on this type
-      TypeVariable<?>[] classTypeVariables =
-          ((Class<?>) ((ParameterizedType) superClass).getRawType()).getTypeParameters();
-      int indexOfActualTypeArgument = getIndex(classTypeVariables, typeToEvaluate);
-
-      Type[] actualTypeArguments = null;
-      if (parentType instanceof Class<?>) {
-        actualTypeArguments = ((ParameterizedType) superClass).getActualTypeArguments();
-      } else if (parentType instanceof ParameterizedType) {
-        actualTypeArguments = ((ParameterizedType) parentType).getActualTypeArguments();
-      } else {
-        return null;
-      }
 
-      return actualTypeArguments[indexOfActualTypeArgument];
-    }
 
-    Type searchedType = null;
-    if (superClass != null) {
-      searchedType = extractTypeForHierarchy(superClass, typeToEvaluate);
-    }
-    return searchedType;
-  }
+
 
   private static Type[] extractRealTypes(
       Type[] actualTypeArguments, Type parentType, Class<?> rawParentClass) {
