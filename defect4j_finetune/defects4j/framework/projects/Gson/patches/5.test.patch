diff --git a/gson/src/test/java/com/google/gson/DefaultDateTypeAdapterTest.java b/gson/src/test/java/com/google/gson/DefaultDateTypeAdapterTest.java
index 5ce65d74..d414e9c8 100644
--- a/gson/src/test/java/com/google/gson/DefaultDateTypeAdapterTest.java
+++ b/gson/src/test/java/com/google/gson/DefaultDateTypeAdapterTest.java
@@ -130,7 +130,6 @@ public class DefaultDateTypeAdapterTest extends TestCase {
     assertParsed("1970-01-01T00:00Z", adapter);
     assertParsed("1970-01-01T00:00:00+00:00", adapter);
     assertParsed("1970-01-01T01:00:00+01:00", adapter);
-    assertParsed("1970-01-01T01:00:00+01", adapter);
   }
   
   public void testDateSerialization() throws Exception {
