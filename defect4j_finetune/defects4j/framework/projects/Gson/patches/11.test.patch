diff --git a/gson/src/test/java/com/google/gson/functional/PrimitiveTest.java b/gson/src/test/java/com/google/gson/functional/PrimitiveTest.java
index 9da983a3..bb28ed1e 100644
--- a/gson/src/test/java/com/google/gson/functional/PrimitiveTest.java
+++ b/gson/src/test/java/com/google/gson/functional/PrimitiveTest.java
@@ -158,11 +158,6 @@ public class PrimitiveTest extends TestCase {
     assertEquals(1L, actual.longValue());
   }
 
-  public void testNumberAsStringDeserialization() {
-    Number value = gson.fromJson("\"18\"", Number.class);
-    assertEquals(18, value.intValue());
-  }
-
   public void testPrimitiveDoubleAutoboxedSerialization() {
     assertEquals("-122.08234335", gson.toJson(-122.08234335));
     assertEquals("122.08112002", gson.toJson(new Double(122.08112002)));
