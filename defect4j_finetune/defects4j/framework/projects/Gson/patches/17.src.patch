diff --git a/gson/src/main/java/com/google/gson/DefaultDateTypeAdapter.java b/gson/src/main/java/com/google/gson/DefaultDateTypeAdapter.java
index 3ce97fe8..1ab606c1 100644
--- a/gson/src/main/java/com/google/gson/DefaultDateTypeAdapter.java
+++ b/gson/src/main/java/com/google/gson/DefaultDateTypeAdapter.java
@@ -96,9 +96,8 @@ final class DefaultDateTypeAdapter extends TypeAdapter<Date> {
 
   @Override
   public Date read(JsonReader in) throws IOException {
-    if (in.peek() == JsonToken.NULL) {
-      in.nextNull();
-      return null;
+    if (in.peek() != JsonToken.STRING) {
+      throw new JsonParseException("The date should be a string value");
     }
     Date date = deserializeToDate(in.nextString());
     if (dateType == Date.class) {
