diff --git a/gson/src/main/java/com/google/gson/internal/bind/util/ISO8601Utils.java b/gson/src/main/java/com/google/gson/internal/bind/util/ISO8601Utils.java
index c931b090..a326f768 100644
--- a/gson/src/main/java/com/google/gson/internal/bind/util/ISO8601Utils.java
+++ b/gson/src/main/java/com/google/gson/internal/bind/util/ISO8601Utils.java
@@ -211,7 +211,6 @@ public class ISO8601Utils
                 String timezoneOffset = date.substring(offset);
 
                 // When timezone has no minutes, we should append it, valid timezones are, for example: +00:00, +0000 and +00
-                timezoneOffset = timezoneOffset.length() >= 5 ? timezoneOffset : timezoneOffset + "00";
 
                 offset += timezoneOffset.length();
                 // 18-Jun-2015, tatu: Minor simplification, skip offset of "+0000"/"+00:00"
