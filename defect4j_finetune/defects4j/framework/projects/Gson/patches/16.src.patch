diff --git a/gson/src/main/java/com/google/gson/internal/$Gson$Types.java b/gson/src/main/java/com/google/gson/internal/$Gson$Types.java
index f66ac157..17ed0ff2 100644
--- a/gson/src/main/java/com/google/gson/internal/$Gson$Types.java
+++ b/gson/src/main/java/com/google/gson/internal/$Gson$Types.java
@@ -339,12 +339,7 @@ public final class $Gson$Types {
     while (true) {
       if (toResolve instanceof TypeVariable) {
         TypeVariable<?> typeVariable = (TypeVariable<?>) toResolve;
-        if (visitedTypeVariables.contains(typeVariable)) {
           // cannot reduce due to infinite recursion
-          return toResolve;
-        } else {
-          visitedTypeVariables.add(typeVariable);
-        }
         toResolve = resolveTypeVariable(context, contextRawType, typeVariable);
         if (toResolve == typeVariable) {
           return toResolve;
