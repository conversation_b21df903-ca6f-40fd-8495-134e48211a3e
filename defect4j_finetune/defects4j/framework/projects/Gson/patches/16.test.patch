diff --git a/gson/src/test/java/com/google/gson/internal/bind/RecursiveTypesResolveTest.java b/gson/src/test/java/com/google/gson/internal/bind/RecursiveTypesResolveTest.java
index aaa577b5..4b4ce891 100644
--- a/gson/src/test/java/com/google/gson/internal/bind/RecursiveTypesResolveTest.java
+++ b/gson/src/test/java/com/google/gson/internal/bind/RecursiveTypesResolveTest.java
@@ -86,29 +86,4 @@ public class RecursiveTypesResolveTest extends TestCase {
     assertEquals($Gson$Types.subtypeOf(Object.class),
             $Gson$Types.subtypeOf($Gson$Types.supertypeOf(Number.class)));
   }
-
-  //
-  // tests for recursion while resolving type variables
-  //
-
-  private static class TestType<X> {
-    TestType<? super X> superType;
-  }
-
-  private static class TestType2<X, Y> {
-    TestType2<? super Y, ? super X> superReversedType;
-  }
-
-  public void testRecursiveTypeVariablesResolve1() throws Exception {
-    TypeAdapter<TestType> adapter = new Gson().getAdapter(TestType.class);
-    assertNotNull(adapter);
-  }
-
-  public void testRecursiveTypeVariablesResolve12() throws Exception {
-    TypeAdapter<TestType2> adapter = new Gson().getAdapter(TestType2.class);
-    assertNotNull(adapter);
-  }
 }
-
-
-
