<!--
Copyright (c) 2014-2024 <PERSON>, <PERSON><PERSON><PERSON>, and Defects<PERSON>J contributors.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

################################################################################
                     This is the main Defects4J build file.

It defines properties and targets valid for all projects. It also imports the
build file that defines all export targets (defect4j.export.xml) and the
project-specific build file ("project_id"/"project_id".build.xml) for the
"project_id" (Lang, Math, etc.) of the checked-out project version.
#############################################################################-->
<project name="Defects4J" basedir="${basedir}">
    <!-- Ensure basedir is properly set -->
    <property name="d4j.workdir" value="${basedir}" />
    <property name="d4j.properties" value="defects4j.build.properties" />
    <fail message="Directory ${d4j.workdir} is not a Defects4J working directory!">
        <condition>
            <not> <available file="${d4j.workdir}/${d4j.properties}" /> </not>
        </condition>
    </fail>
    <!-- Check whether d4j.home is set -->
    <fail message="Property d4j.home not set!" unless="d4j.home" />

    <!-- The default location for the projects meta data -->
    <property name="d4j.dir.projects" value="${d4j.home}/framework/projects" />

    <taskdef resource="net/sf/antcontrib/antlib.xml"
            classpath="${d4j.home}/framework/lib/ant-contrib.jar"/>

    <!-- Dependencies used by all projects -->
    <property name="junit.jar"  value="${d4j.home}/framework/projects/lib/junit-4.12-hamcrest-1.3.jar"/>
    <property name="cobertura.jar" value="${d4j.home}/framework/projects/lib/cobertura-2.0.3.jar"/>
    <property name="cobertura.lib" value="${d4j.home}/framework/projects/lib/cobertura-2.0.3-lib"/>

    <!-- Location of libraries of test generation tool-->
    <property name="d4j.dir.lib.testgen.gen" value="${d4j.home}/framework/lib/test_generation/generation"/>
    <!-- Location of runtime libraries of test generation tools-->
    <property name="d4j.dir.lib.testgen.rt" value="${d4j.home}/framework/lib/test_generation/runtime"/>
    <!-- All runtime libraries of test generation tools (necessary to run generated tests)-->
    <path id="d4j.lib.testgen.rt">
        <fileset dir="${d4j.dir.lib.testgen.rt}" includes="*-rt.jar" />
    </path>

    <!-- Location of customized JUnit formatter-->
    <property name="formatter.jar" value="${d4j.home}/framework/lib/formatter.jar"/>
    <!-- Directory of compiled test-gen classes -->
    <property name="d4j.dir.classes.testgen" value="${d4j.workdir}/.classes_testgen"/>
    <!-- Directory of instrumented classes (e.g., for coverage analysis)-->
    <property name="d4j.dir.classes.instrumented" value="${d4j.workdir}/.classes_instrumented"/>
    <!-- Directory of mutated classes-->
    <property name="d4j.dir.classes.mutated" value="${d4j.workdir}/.classes_mutated"/>
    <!-- Directory of coverage reports-->
    <property name="d4j.dir.coverage.report" value="${d4j.workdir}" />
    <!-- Data file that holds coverage information-->
    <property name="d4j.file.cobertura.ser" value="cobertura.ser" />

    <!-- Additional properties such as tests to exclude.
         This property file is automatically generated. -->
    <property file="${d4j.workdir}/${d4j.properties}"/>

    <!-- Include build file that defines all export targets -->
    <import file="${d4j.home}/framework/projects/defects4j.export.xml"/>

    <!-- Include project-specific build file -->
    <import file="${d4j.dir.projects}/${d4j.project.id}/${d4j.project.id}.build.xml"/>


<!--
     Check whether all properties and paths are configured
-->
    <target name="sanity.check" description="Check whether project version is correctly set up">
        <fail unless="d4j.home"/>
        <fail unless="d4j.workdir"/>
        <fail unless="d4j.project.id"/>
        <fail unless="d4j.bug.id"/>
        <fail unless="d4j.dir.src.classes"/>
        <fail unless="d4j.dir.src.tests"/>
        <fail unless="d4j.classes.modified"/>
        <fail unless="test.home"/>
        <fail unless="build.home"/>
        <fail unless="ant.refid:d4j.test.classpath"/>
        <fail unless="ant.refid:compile.classpath"/>
        <fail unless="ant.refid:all.manual.tests" />
    </target>

<!--
    Run developer-written tests
-->
    <target name="run.dev.tests" depends="compile.tests,update.all.tests" description="Run unit tests">
        <junit printsummary="yes" haltonfailure="no" haltonerror="no" fork="no" showOutput="true">
            <classpath>
                <!-- Make sure that instrumented classes appear at the beginning of the
                     classpath -->
                <pathelement location="${d4j.dir.classes.instrumented}" />
                <pathelement path="${formatter.jar}" />
                <pathelement path="${cobertura.jar}" />
                <path refid="d4j.test.classpath"/>
            </classpath>

            <sysproperty key="OUTFILE" value="${OUTFILE}"/>
            <formatter classname="edu.washington.cs.mut.testrunner.Formatter" usefile="false" />
            <test name="${test.entry.class}" methods="${test.entry.method}" if="test.entry.class" />
            <batchtest unless="test.entry.class">
                <fileset refid="all.manual.tests" />
            </batchtest>
        </junit>
        <!-- fail build in case we are running all classes, but there are none in the fileset -->
        <if> <not> <isset property="test.entry.class" /> </not> <then>
            <pathconvert refid="all.manual.tests" property="fileset.notempty" setonempty="false" />
            <fail unless="fileset.notempty" message="Fileset of tests to run is empty!" />
        </then> </if>
    </target>

<!--
    Run individual test and monitor class loader
    Test has to be provided as property "test.entry" (class::method)
    and the output is redirected to "test.output"
-->
    <target name="monitor.test" depends="compile.tests" description="Run a single test class or method and monitor class loader">
        <fail message="Property test.entry not set!" unless="test.entry" />
        <fail message="Property test.output not set!" unless="test.output" />

        <echo message="Logging loaded classes to ${test.output}" />

        <java fork="true" classname="edu.washington.cs.mut.testrunner.SingleTestRunner"
              output="${test.output}" failonerror="true" logError="true">

            <arg value="${test.entry}"/>
            <jvmarg value="-verbose:class"/>
            <classpath>
                <pathelement path="${formatter.jar}" />
                <pathelement path="${junit.jar}" />
                <pathelement path="${d4j.dir.classes.testgen}" />
                <path refid="d4j.test.classpath"/>
                <!-- Add dependencies to runtime libraries of test generation tools -->
                <path refid="d4j.lib.testgen.rt"/>
            </classpath>
        </java>
    </target>

<!--
    Mutate all classes
-->
    <target name="mutate" depends="check.classes.uptodate" unless="classes.mutated.uptodate">
        <property name="dir.classes.tmp" value=".classes.tmp"/>
        <!-- Backup original class files (might not yet exist) -->
        <move file="${classes.dir}" tofile="${dir.classes.tmp}" quiet="true" failonerror="false"/>

        <!-- Delete all previously mutated classes -->
        <delete dir="${d4j.dir.classes.mutated}" quiet="true" failonerror="false"/>

        <!--
        Set compiler property to change default compiler to compiler
        adapter "MajorCompiler". This simple ant compiler adapter will
        invoke a compiler executable named "major", which has to be on
        the PATH!

        TODO: Improve compiler adapter to explicitly override the output directory.
        -->
        <property name="build.compiler" value="major.ant.MajorCompiler"/>
        <property name="d4j.mutation" value="true"/>
        <!-- Call original compile target -->
        <antcall target="compile"/>
        <!-- Move mutated classes to dedicated directory -->
        <move file="${classes.dir}" tofile="${d4j.dir.classes.mutated}"/>
        <copy file="${d4j.workdir}/${d4j.properties}" todir="${d4j.dir.classes.mutated}" overwrite="true"/>
        <!-- Restore original class files -->
        <move file="${dir.classes.tmp}" tofile="${classes.dir}" quiet="true" failonerror="false"/>
    </target>

<!--
    Run mutation analysis
-->
    <target name="mutation.test" depends="compile.tests,update.all.tests" description="Perform mutation analysis">
        <!-- Test a generated test suite -->
        <if><isset property="d4j.test.include"/>
            <then>
                <fileset id="major.tests" dir="${d4j.test.dir}">
                    <include name="**/${d4j.test.include}"/>
                    <!-- Ignore helper classes that do not contain test cases -->
                    <exclude name="**/*_scaffolding.java"/>
                </fileset>
            </then>
            <else>
                <!-- Test existing test suite -->
                <fail message="File set all.manual.tests not set!" unless="ant.refid:all.manual.tests" />
                <fileset id="major.tests" refid="all.manual.tests" />
            </else>
        </if>

        <fail message="Property major.kill.log not set!" unless="major.kill.log" />

        <!-- Sort test classes by run time, unless a single test method is run -->
        <condition property="major.sort" value="original" else="sort_classes">
            <isset property="test.entry.method"/>
        </condition>

        <echo message="Running mutation analysis ..."/>
        <junit
            printsummary="false"
            showoutput="false"
            fork="no"

            mutationAnalysis="true"
            timeoutFactor="16"
            testOrder="${major.sort}"
            haltonfailure="true"
            mutantDetailsFile="${major.kill.log}"
            excludeMutantsFile="${major.exclude}"
            >

            <!--
            Setting haltonfailure to false leads to the exclusion of
            failing tests -> usually all tests should pass!
            -->

            <classpath>
                <pathelement location="${d4j.dir.classes.mutated}" />
                <pathelement path="${d4j.dir.classes.testgen}" />
                <path refid="d4j.test.classpath"/>
                <!-- Add dependencies to runtime libraries of test generation tools -->
                <path refid="d4j.lib.testgen.rt"/>
            </classpath>

            <test name="${test.entry.class}" methods="${test.entry.method}" if="test.entry.class" />
            <batchtest unless="test.entry.class" fork="no">
                <fileset refid="major.tests"/>
            </batchtest>
        </junit>
    </target>

<!--
    Compile generated unit tests
-->
    <target name="compile.gen.tests" depends="compile" description="Compile generated tests">
        <!-- d4j.test.dir, build.home, and classes.dir properties have to be provided by caller! -->
        <fail message="Property d4j.test.dir not set!" unless="d4j.test.dir" />
        <fail message="Property build.home not set!" unless="build.home" />
        <fail message="Property classes.dir not set!" unless="classes.dir" />

        <echo message="classes.dir: ${classes.dir}" />
        <echo message="build.home: ${build.home}" />

        <delete dir="${d4j.dir.classes.testgen}" quiet="true" />
        <mkdir dir="${d4j.dir.classes.testgen}" />
        <javac srcdir="${d4j.test.dir}"
               destdir="${d4j.dir.classes.testgen}"
               debug="true"
               deprecation="false"
               optimize="false">
               <compilerarg line="-Xmaxerrs 1" />
               <classpath>
                    <pathelement path="${junit.jar}" />
                    <path refid="d4j.test.classpath" />
                    <pathelement path="${classes.dir}" />
                    <!-- Add dependencies to runtime libraries of test generation tools -->
                    <path refid="d4j.lib.testgen.rt"/>
               </classpath>
        </javac>
    </target>

<!--
    Run generated tests
-->
    <target name="run.gen.tests"  depends="compile.gen.tests" description="Run generated tests">
        <!-- d4j.test.dir, d4j.test.include, and class.dir properties have to be provided by caller! -->
        <fail message="Property d4j.test.dir not set!" unless="d4j.test.dir" />
        <fail message="Property d4j.test.include not set!" unless="d4j.test.include" />
        <fail message="Property classes.dir not set!" unless="classes.dir" />

        <echo message="classes.dir: ${classes.dir}" />
        <echo message="build.home: ${build.home}" />

        <junit printsummary="no" haltonfailure="no" haltonerror="no" fork="yes" forkmode="perBatch" showOutput="true">
            <classpath>
                <!-- Make sure that instrumented classes appear at the beginning of the
                     classpath -->
                <pathelement location="${d4j.dir.classes.instrumented}" />
                <pathelement path="${d4j.dir.classes.testgen}" />
                <pathelement path="${formatter.jar}" />
                <pathelement path="${junit.jar}" />
                <pathelement path="${classes.dir}" />
                <path refid="d4j.test.classpath"/>
                <!-- Add dependencies to code coverage tools -->
                <pathelement path="${cobertura.jar}" />
                <!-- Add dependencies to runtime libraries of test generation tools -->
                <path refid="d4j.lib.testgen.rt"/>
            </classpath>

            <sysproperty key="OUTFILE" value="${OUTFILE}"/>
            <formatter classname="edu.washington.cs.mut.testrunner.Formatter" usefile="false" />
            <test name="${test.entry.class}" methods="${test.entry.method}" if="test.entry.class" />
                <batchtest unless="test.entry.class">
                    <fileset dir="${d4j.test.dir}">
                        <include name="**/${d4j.test.include}"/>
                        <!-- Ignore helper classes that do not contain test cases -->
                        <exclude name="**/*_scaffolding.java"/>
                    </fileset>
                </batchtest>
        </junit>
    </target>

<!--
    Instrument for cobertura
    TODO: Clean up the confusing classpath IDs and property names
-->
    <path id="cobertura.classpath">
        <pathelement location="${cobertura.jar}" />
        <fileset dir="${cobertura.lib}">
            <include name="**/*.jar" />
        </fileset>
    </path>
    <taskdef classpathref="cobertura.classpath" resource="tasks.properties" />

    <path id="cobertura.classpath.include" />

    <target name="coverage.instrument" depends="compile,check.classes.uptodate,reset.cobertura.ser" unless="classes.instrumented.uptodate">
        <pathconvert refid="cobertura.classpath" property="cobertura.classpath.property" />

        <delete dir="${d4j.dir.classes.instrumented}"/>
        <delete file="${d4j.workdir}/${d4j.file.cobertura.ser}"/>
        <cobertura-instrument failOnError="true" toDir="${d4j.dir.classes.instrumented}" datafile="${d4j.workdir}/${d4j.file.cobertura.ser}">
            <fileset dir="${classes.dir}" includes="${d4j.classes.instrument}" />
            <auxClasspath>
                <path refid="compile.classpath" />
            </auxClasspath>
        </cobertura-instrument>
        <!-- Backup instrumented classes and empty data file -->
        <copy file="${d4j.workdir}/${d4j.properties}" todir="${d4j.dir.classes.instrumented}" overwrite="true"/>
        <copy file="${d4j.workdir}/${d4j.file.cobertura.ser}" todir="${d4j.dir.classes.instrumented}" overwrite="true"/>
    </target>

    <target name="coverage.report">
        <mkdir dir="${d4j.dir.coverage.report}" />

        <!-- <cobertura-report format="html" destdir="${coverage.dir}" srcdir="${coverage.src.dir}" /> -->
        <cobertura-report format="xml" destdir="${d4j.dir.coverage.report}" srcdir="${coverage.src.dir}" />
    </target>

<!--
    Update list of all manual tests to only include relevant tests
-->
    <target name="update.all.tests" if="${d4j.relevant.tests.only}">
        <!-- TODO: Conversions in Ant a rather cumbersome and hacky. Also, the
                   build file should not rely on the current directory structure
              => D4J should provide properly filtered files in the working directory-->
        <loadfile srcfile="${d4j.dir.projects}/${d4j.project.id}/relevant_tests/${d4j.bug.id}"
                 property="rel.files">
            <filterchain>
                <tokenfilter>
                    <replaceregex pattern="\." replace="/" flags="g"/>
                </tokenfilter>
                <suffixlines suffix=".*,"/>
                <striplinebreaks/>
            </filterchain>
        </loadfile>
        <fileset id="all.manual.tests" dir="${d4j.dir.src.tests}" includes="${rel.files}"/>
    </target>

<!--
    Check whether instrumented classes are up to date for mutation analysis and
    code coverage analysis
-->
    <target name="check.classes.uptodate">
        <condition property="classes.mutated.uptodate" value="true">
            <filesmatch file1="${d4j.workdir}/${d4j.properties}" file2="${d4j.dir.classes.mutated}/${d4j.properties}"/>
        </condition>
        <condition property="classes.instrumented.uptodate" value="true">
            <filesmatch file1="${d4j.workdir}/${d4j.properties}" file2="${d4j.dir.classes.instrumented}/${d4j.properties}"/>
        </condition>
    </target>

<!--
    Reset Cobertura's data file if the instrumented classes can be re-used
-->
    <target name="reset.cobertura.ser" if="classes.instrumented.uptodate">
        <copy file="${d4j.dir.classes.instrumented}/${d4j.file.cobertura.ser}"
              todir="${d4j.workdir}" overwrite="true" />
    </target>

</project>
