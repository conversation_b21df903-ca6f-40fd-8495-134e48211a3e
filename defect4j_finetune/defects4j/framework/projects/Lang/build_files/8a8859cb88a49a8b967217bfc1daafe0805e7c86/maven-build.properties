#Generated by Ma<PERSON> Ant Plugin - DO NOT EDIT THIS FILE!
#Thu Mar 06 19:28:21 PST 2014
maven.build.testDir.0=src/test/java
maven.settings.offline=false
commons.encoding=iso-8859-1
commons.osgi.dynamicImport=
commons.docEncoding=iso-8859-1
maven.settings.interactiveMode=true
commons.osgi.private=
commons.release.version=2.5
commons.componentid=lang
maven.reporting.outputDirectory=${maven.build.dir}/site
commons.osgi.symbolicName=org.apache.commons.lang
project.build.sourceEncoding=UTF-8
maven.build.testOutputDir=${maven.build.dir}/test-classes
commons.binary.suffix=-bin
maven.build.finalName=commons-lang-3.0-SNAPSHOT
maven.test.reports=${maven.build.dir}/test-reports
commons.osgi.export=org.apache.commons.*;version\=3.0-SNAPSHOT;-noimport\:\=true
commons.release.name=commons-lang-2.5
maven.compile.target=1.5
distMgmtSnapshotsName=Apache Development Snapshot Repository
maven.repo.local=${user.home}/.m2/repository
maven.build.dir=target
distMgmtSnapshotsUrl=https\://repository.apache.org/content/repositories/snapshots
maven.build.outputDir=${maven.build.dir}/classes
project.reporting.outputEncoding=UTF-8
commons.osgi.import=\!net.jcip.annotations,*
project.build.directory=${maven.build.dir}
maven.build.resourceDir.0=.
commons.release.2.desc=
organization.logo=http\://www.apache.org/images/asf_logo_wide.gif
commons.manifestfile=target/osgi/MANIFEST.MF
commons.jira.pid=12310481
maven.build.srcDir.0=src/main/java
commons.rc.version=RC1
commons.release.2.binary.suffix=-bin
maven.build.testResourceDir.0=src/test/resources
maven.compile.source=1.5
commons.deployment.protocol=scp
project.build.outputDirectory=${maven.build.outputDir}
sourceReleaseAssemblyDescriptor=source-release
commons.release.desc=
commons.jira.id=LANG
