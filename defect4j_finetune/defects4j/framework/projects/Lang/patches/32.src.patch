diff --git a/src/main/java/org/apache/commons/lang3/builder/HashCodeBuilder.java b/src/main/java/org/apache/commons/lang3/builder/HashCodeBuilder.java
index 9f7e890..4ae351d 100644
--- a/src/main/java/org/apache/commons/lang3/builder/HashCodeBuilder.java
+++ b/src/main/java/org/apache/commons/lang3/builder/HashCodeBuilder.java
@@ -101,7 +101,12 @@ public class HashCodeBuilder {
      * 
      * @since 2.3
      */
-    private static final ThreadLocal<Set<IDKey>> REGISTRY = new ThreadLocal<Set<IDKey>>();
+    private static final ThreadLocal<Set<IDKey>> REGISTRY = new ThreadLocal<Set<IDKey>>() {
+        @Override
+        protected Set<IDKey> initialValue() {
+            return new HashSet<IDKey>();
+        }
+    };
 
     /*
      * N.B. we cannot store the actual objects in a HashSet, as that would use the very hashCode()
@@ -144,8 +149,7 @@ public class HashCodeBuilder {
      * @since 2.3
      */
     static boolean isRegistered(Object value) {
-        Set<IDKey> registry = getRegistry();
-        return registry != null && registry.contains(new IDKey(value));
+        return getRegistry().contains(new IDKey(value));
     }
 
     /**
@@ -515,11 +519,6 @@ public class HashCodeBuilder {
      *            The object to register.
      */
     static void register(Object value) {
-        synchronized (HashCodeBuilder.class) {
-            if (getRegistry() == null) {
-                REGISTRY.set(new HashSet<IDKey>());
-            }
-        }
         getRegistry().add(new IDKey(value));
     }
 
@@ -536,15 +535,7 @@ public class HashCodeBuilder {
      * @since 2.3
      */
     static void unregister(Object value) {
-        Set<IDKey> s = getRegistry();
-        if (s != null) {
-            s.remove(new IDKey(value));
-            synchronized (HashCodeBuilder.class) {
-                if (s.isEmpty()) {
-                    REGISTRY.remove();
-                }
-            }
-        }
+        getRegistry().remove(new IDKey(value));
     }
 
     /**
