diff --git a/src/test/org/apache/commons/lang/builder/EqualsBuilderTest.java b/src/test/org/apache/commons/lang/builder/EqualsBuilderTest.java
index 3455561..57c6459 100644
--- a/src/test/org/apache/commons/lang/builder/EqualsBuilderTest.java
+++ b/src/test/org/apache/commons/lang/builder/EqualsBuilderTest.java
@@ -16,7 +16,6 @@
  */
 package org.apache.commons.lang.builder;
 
-import java.math.BigDecimal;
 import java.util.Arrays;
 
 import junit.framework.Test;
@@ -377,14 +376,6 @@ public class EqualsBuilderTest extends TestCase {
         assertTrue(new EqualsBuilder().append(Float.POSITIVE_INFINITY, Float.POSITIVE_INFINITY).isEquals());
     }
 
-    // https://issues.apache.org/jira/browse/LANG-393
-    public void testBigDecimal() {
-        BigDecimal o1 = new BigDecimal("2.0");
-        BigDecimal o2 = new BigDecimal("2.00");
-        assertTrue(new EqualsBuilder().append(o1, o1).isEquals());
-        assertTrue(new EqualsBuilder().append(o1, o2).isEquals());
-    }
-
     public void testAccessors() {
         EqualsBuilder equalsBuilder = new EqualsBuilder();
         assertTrue(equalsBuilder.isEquals());
