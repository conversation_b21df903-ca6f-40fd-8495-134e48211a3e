diff --git a/src/test/java/org/apache/commons/lang3/math/NumberUtilsTest.java b/src/test/java/org/apache/commons/lang3/math/NumberUtilsTest.java
index bdd2a24..56b0597 100644
--- a/src/test/java/org/apache/commons/lang3/math/NumberUtilsTest.java
+++ b/src/test/java/org/apache/commons/lang3/math/NumberUtilsTest.java
@@ -246,43 +246,6 @@ public class NumberUtilsTest {
         assertEquals(BigDecimal.class, bigNum.getClass());
     }
 
-    @Test
-    public void TestLang747() {
-        assertEquals(Integer.valueOf(0x8000),      NumberUtils.createNumber("0x8000"));
-        assertEquals(Integer.valueOf(0x80000),     NumberUtils.createNumber("0x80000"));
-        assertEquals(Integer.valueOf(0x800000),    NumberUtils.createNumber("0x800000"));
-        assertEquals(Integer.valueOf(0x8000000),   NumberUtils.createNumber("0x8000000"));
-        assertEquals(Integer.valueOf(0x7FFFFFFF),  NumberUtils.createNumber("0x7FFFFFFF"));
-        assertEquals(Long.valueOf(0x80000000L),    NumberUtils.createNumber("0x80000000"));
-        assertEquals(Long.valueOf(0xFFFFFFFFL),    NumberUtils.createNumber("0xFFFFFFFF"));
-
-        // Leading zero tests
-        assertEquals(Integer.valueOf(0x8000000),   NumberUtils.createNumber("0x08000000"));
-        assertEquals(Integer.valueOf(0x7FFFFFFF),  NumberUtils.createNumber("0x007FFFFFFF"));
-        assertEquals(Long.valueOf(0x80000000L),    NumberUtils.createNumber("0x080000000"));
-        assertEquals(Long.valueOf(0xFFFFFFFFL),    NumberUtils.createNumber("0x00FFFFFFFF"));
-
-        assertEquals(Long.valueOf(0x800000000L),        NumberUtils.createNumber("0x800000000"));
-        assertEquals(Long.valueOf(0x8000000000L),       NumberUtils.createNumber("0x8000000000"));
-        assertEquals(Long.valueOf(0x80000000000L),      NumberUtils.createNumber("0x80000000000"));
-        assertEquals(Long.valueOf(0x800000000000L),     NumberUtils.createNumber("0x800000000000"));
-        assertEquals(Long.valueOf(0x8000000000000L),    NumberUtils.createNumber("0x8000000000000"));
-        assertEquals(Long.valueOf(0x80000000000000L),   NumberUtils.createNumber("0x80000000000000"));
-        assertEquals(Long.valueOf(0x800000000000000L),  NumberUtils.createNumber("0x800000000000000"));
-        assertEquals(Long.valueOf(0x7FFFFFFFFFFFFFFFL), NumberUtils.createNumber("0x7FFFFFFFFFFFFFFF"));
-        // N.B. Cannot use a hex constant such as 0x8000000000000000L here as that is interpreted as a negative long
-        assertEquals(new BigInteger("8000000000000000", 16), NumberUtils.createNumber("0x8000000000000000"));
-        assertEquals(new BigInteger("FFFFFFFFFFFFFFFF", 16), NumberUtils.createNumber("0xFFFFFFFFFFFFFFFF"));
-
-        // Leading zero tests
-        assertEquals(Long.valueOf(0x80000000000000L),   NumberUtils.createNumber("0x00080000000000000"));
-        assertEquals(Long.valueOf(0x800000000000000L),  NumberUtils.createNumber("0x0800000000000000"));
-        assertEquals(Long.valueOf(0x7FFFFFFFFFFFFFFFL), NumberUtils.createNumber("0x07FFFFFFFFFFFFFFF"));
-        // N.B. Cannot use a hex constant such as 0x8000000000000000L here as that is interpreted as a negative long
-        assertEquals(new BigInteger("8000000000000000", 16), NumberUtils.createNumber("0x00008000000000000000"));
-        assertEquals(new BigInteger("FFFFFFFFFFFFFFFF", 16), NumberUtils.createNumber("0x0FFFFFFFFFFFFFFFF"));
-    }
-
     @Test(expected=NumberFormatException.class)
     // Check that the code fails to create a valid number when preceeded by -- rather than -
     public void testCreateNumberFailure_1() {
