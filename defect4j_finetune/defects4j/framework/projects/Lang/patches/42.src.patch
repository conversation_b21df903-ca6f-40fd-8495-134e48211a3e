diff --git a/src/java/org/apache/commons/lang/Entities.java b/src/java/org/apache/commons/lang/Entities.java
index 0d8f7f4..900e1a8 100644
--- a/src/java/org/apache/commons/lang/Entities.java
+++ b/src/java/org/apache/commons/lang/Entities.java
@@ -825,15 +825,10 @@ class Entities {
     public void escape(Writer writer, String str) throws IOException {
         int len = str.length();
         for (int i = 0; i < len; i++) {
-            int c = Character.codePointAt(str, i); 
+            char c = str.charAt(i);
             String entityName = this.entityName(c);
             if (entityName == null) {
-                if (c >= 0x010000 && i < len - 1) {
-                    writer.write("&#");
-                    writer.write(Integer.toString(c, 10));
-                    writer.write(';');
-                    i++;
-                } else if (c > 0x7F) { 
+                if (c > 0x7F) {
                     writer.write("&#");
                     writer.write(Integer.toString(c, 10));
                     writer.write(';');
