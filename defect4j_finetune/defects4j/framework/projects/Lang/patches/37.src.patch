diff --git a/src/java/org/apache/commons/lang3/ArrayUtils.java b/src/java/org/apache/commons/lang3/ArrayUtils.java
index b9413e8..889497e 100644
--- a/src/java/org/apache/commons/lang3/ArrayUtils.java
+++ b/src/java/org/apache/commons/lang3/ArrayUtils.java
@@ -2959,16 +2959,8 @@ public class ArrayUtils {
         final Class<?> type1 = array1.getClass().getComponentType();
         T[] joinedArray = (T[]) Array.newInstance(type1, array1.length + array2.length);
         System.arraycopy(array1, 0, joinedArray, 0, array1.length);
-        try {
             System.arraycopy(array2, 0, joinedArray, array1.length, array2.length);
-        } catch (ArrayStoreException ase) {
             // Check if problem is incompatible types
-            final Class<?> type2 = array2.getClass().getComponentType();
-            if (!type1.isAssignableFrom(type2)){
-                throw new IllegalArgumentException("Cannot store "+type2.getName()+" in an array of "+type1.getName());
-            }
-            throw ase; // No, so rethrow original
-        }
         return joinedArray;
     }
 
