diff --git a/src/test/org/apache/commons/lang3/StringUtilsTest.java b/src/test/org/apache/commons/lang3/StringUtilsTest.java
index 7a74598..bb0acf8 100644
--- a/src/test/org/apache/commons/lang3/StringUtilsTest.java
+++ b/src/test/org/apache/commons/lang3/StringUtilsTest.java
@@ -1033,10 +1033,6 @@ public class StringUtilsTest extends TestCase {
                 "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "N", "O", "P", "Q", 
                 "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "A", "B", "C", "D", "E", "F", "G", 
                 "H", "I", "J", "K", "L", "M", "5", "6", "7", "8", "9", "1", "2", "3", "4"}));
-
-        // Test null safety inside arrays - LANG-552
-        assertEquals(StringUtils.replaceEach("aba", new String[]{"a"}, new String[]{null}),"aba");
-        assertEquals(StringUtils.replaceEach("aba", new String[]{"a", "b"}, new String[]{"c", null}),"cbc");
     }
 
     /**
