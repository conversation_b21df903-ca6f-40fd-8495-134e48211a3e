diff --git a/src/java/org/apache/commons/lang/StringEscapeUtils.java b/src/java/org/apache/commons/lang/StringEscapeUtils.java
index db2d7bd..4fda091 100644
--- a/src/java/org/apache/commons/lang/StringEscapeUtils.java
+++ b/src/java/org/apache/commons/lang/StringEscapeUtils.java
@@ -233,10 +233,6 @@ public class StringEscapeUtils {
                         out.write('\\');
                         out.write('\\');
                         break;
-                    case '/':
-                        out.write('\\');
-                        out.write('/');
-                        break;
                     default :
                         out.write(ch);
                         break;
