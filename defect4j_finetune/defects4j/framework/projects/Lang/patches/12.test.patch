diff --git a/src/test/java/org/apache/commons/lang3/RandomStringUtilsTest.java b/src/test/java/org/apache/commons/lang3/RandomStringUtilsTest.java
index 6c7b0c1..55ce99a 100644
--- a/src/test/java/org/apache/commons/lang3/RandomStringUtilsTest.java
+++ b/src/test/java/org/apache/commons/lang3/RandomStringUtilsTest.java
@@ -123,15 +123,9 @@ public class RandomStringUtilsTest extends junit.framework.TestCase {
 
         r1 = RandomStringUtils.random(0);
         assertEquals("random(0).equals(\"\")", "", r1);
-    }
 
-    public void testLANG805() {
-        long seed = System.currentTimeMillis();
-        assertEquals("aaa", RandomStringUtils.random(3,0,0,false,false,new char[]{'a'},new Random(seed)));
     }
-
     public void testExceptions() {
-        final char[] DUMMY = new char[]{'a'}; // valid char array
         try {
             RandomStringUtils.random(-1);
             fail();
@@ -141,11 +135,7 @@ public class RandomStringUtilsTest extends junit.framework.TestCase {
             fail();
         } catch (IllegalArgumentException ex) {}
         try {
-            RandomStringUtils.random(-1, DUMMY);
-            fail();
-        } catch (IllegalArgumentException ex) {}
-        try {
-            RandomStringUtils.random(1, new char[0]); // must not provide empty array => IAE
+            RandomStringUtils.random(-1, new char[0]);
             fail();
         } catch (IllegalArgumentException ex) {}
         try {
@@ -153,19 +143,15 @@ public class RandomStringUtilsTest extends junit.framework.TestCase {
             fail();
         } catch (IllegalArgumentException ex) {}
         try {
-            RandomStringUtils.random(-1, (String)null);
-            fail();
-        } catch (IllegalArgumentException ex) {}
-        try {
             RandomStringUtils.random(-1, 'a', 'z', false, false);
             fail();
         } catch (IllegalArgumentException ex) {}
         try {
-            RandomStringUtils.random(-1, 'a', 'z', false, false, DUMMY);
+            RandomStringUtils.random(-1, 'a', 'z', false, false, new char[0]);
             fail();
         } catch (IllegalArgumentException ex) {}
         try {
-            RandomStringUtils.random(-1, 'a', 'z', false, false, DUMMY, new Random());
+            RandomStringUtils.random(-1, 'a', 'z', false, false, new char[0], new Random());
             fail();
         } catch (IllegalArgumentException ex) {}
     }
