diff --git a/src/test/org/apache/commons/lang3/time/FastDateFormatTest.java b/src/test/org/apache/commons/lang3/time/FastDateFormatTest.java
index 4a20ddf..32bed7f 100644
--- a/src/test/org/apache/commons/lang3/time/FastDateFormatTest.java
+++ b/src/test/org/apache/commons/lang3/time/FastDateFormatTest.java
@@ -333,17 +333,4 @@ public class FastDateFormatTest extends TestCase {
         format = (FastDateFormat) SerializationUtils.deserialize( SerializationUtils.serialize( format ) );
         assertEquals(output, format.format(cal));
     }
-
-    public void testLang538() {
-        final String dateTime = "2009-10-16T16:42:16.000Z";
-
-        // more commonly constructed with: cal = new GregorianCalendar(2009, 9, 16, 8, 42, 16)
-        // for the unit test to work in any time zone, constructing with GMT-8 rather than default locale time zone
-        GregorianCalendar cal = new GregorianCalendar(TimeZone.getTimeZone("GMT-8"));
-        cal.clear();
-        cal.set(2009, 9, 16, 8, 42, 16);
-
-        FastDateFormat format = FastDateFormat.getInstance("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", TimeZone.getTimeZone("GMT"));
-        assertEquals("dateTime", dateTime, format.format(cal));
-    }
 }
