diff --git a/src/test/org/apache/commons/lang/StringUtilsEqualsIndexOfTest.java b/src/test/org/apache/commons/lang/StringUtilsEqualsIndexOfTest.java
index 7c160d3..411e1ee 100644
--- a/src/test/org/apache/commons/lang/StringUtilsEqualsIndexOfTest.java
+++ b/src/test/org/apache/commons/lang/StringUtilsEqualsIndexOfTest.java
@@ -16,8 +16,6 @@
  */
 package org.apache.commons.lang;
 
-import java.util.Locale;
-
 import junit.framework.Test;
 import junit.framework.TestCase;
 import junit.framework.TestSuite;
@@ -313,41 +311,7 @@ public class StringUtilsEqualsIndexOfTest extends TestCase {
         assertTrue(StringUtils.containsIgnoreCase("xabcz", "ABC"));
     }
 
-    public void testContainsIgnoreCase_LocaleIndependence() {
-        Locale orig = Locale.getDefault();
-
-        Locale[] locales = { Locale.ENGLISH, new Locale("tr"), Locale.getDefault() };
-
-        String[][] tdata = { 
-            { "i", "I" },
-            { "I", "i" },
-            { "\u03C2", "\u03C3" },
-            { "\u03A3", "\u03C2" },
-            { "\u03A3", "\u03C3" },
-        };
-
-        String[][] fdata = { 
-            { "\u00DF", "SS" },
-        };
-
-        try {
-            for (int i = 0; i < locales.length; i++) {
-                Locale.setDefault(locales[i]);
-                for (int j = 0; j < tdata.length; j++) {
-                    assertTrue(Locale.getDefault() + ": " + j + " " + tdata[j][0] + " " + tdata[j][1], StringUtils
-                            .containsIgnoreCase(tdata[j][0], tdata[j][1]));
-                }
-                for (int j = 0; j < fdata.length; j++) {
-                    assertFalse(Locale.getDefault() + ": " + j + " " + fdata[j][0] + " " + fdata[j][1], StringUtils
-                            .containsIgnoreCase(fdata[j][0], fdata[j][1]));
-                }
-            }
-        } finally {
-            Locale.setDefault(orig);
-        }
-    }
-
-    // -----------------------------------------------------------------------
+    //-----------------------------------------------------------------------
     public void testIndexOfAny_StringStringarray() {
         assertEquals(-1, StringUtils.indexOfAny(null, (String[]) null));
         assertEquals(-1, StringUtils.indexOfAny(null, FOOBAR_SUB_ARRAY));
