diff --git a/src/java/org/apache/commons/lang/text/StrBuilder.java b/src/java/org/apache/commons/lang/text/StrBuilder.java
index 4c14509..886d424 100644
--- a/src/java/org/apache/commons/lang/text/StrBuilder.java
+++ b/src/java/org/apache/commons/lang/text/StrBuilder.java
@@ -881,7 +881,7 @@ public class StrBuilder implements Cloneable {
             String str = (obj == null ? getNullText() : obj.toString());
             int strLen = str.length();
             if (strLen >= width) {
-                str.getChars(0, width, buffer, size);
+                str.getChars(0, strLen, buffer, size);
             } else {
                 int padLen = width - strLen;
                 str.getChars(0, strLen, buffer, size);
