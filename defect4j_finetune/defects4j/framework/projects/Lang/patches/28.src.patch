diff --git a/src/main/java/org/apache/commons/lang3/text/translate/NumericEntityUnescaper.java b/src/main/java/org/apache/commons/lang3/text/translate/NumericEntityUnescaper.java
index a21671a..c3cb869 100644
--- a/src/main/java/org/apache/commons/lang3/text/translate/NumericEntityUnescaper.java
+++ b/src/main/java/org/apache/commons/lang3/text/translate/NumericEntityUnescaper.java
@@ -60,13 +60,7 @@ public class NumericEntityUnescaper extends CharSequenceTranslator {
                 return 0;
             }
 
-            if(entityValue > 0xFFFF) {
-                char[] chrs = Character.toChars(entityValue);
-                out.write(chrs[0]);
-                out.write(chrs[1]);
-            } else {
                 out.write(entityValue);
-            }
             return 2 + (end - start) + (isHex ? 1 : 0) + 1;
         }
         return 0;
