diff --git a/src/test/java/org/apache/commons/lang3/SystemUtilsTest.java b/src/test/java/org/apache/commons/lang3/SystemUtilsTest.java
index b6bbf8f..10a9492 100644
--- a/src/test/java/org/apache/commons/lang3/SystemUtilsTest.java
+++ b/src/test/java/org/apache/commons/lang3/SystemUtilsTest.java
@@ -207,22 +207,22 @@ public class SystemUtilsTest extends TestCase {
     }
 
     public void testJavaVersionAsInt() {
-        assertEquals(0, SystemUtils.toJavaVersionInt(null));
-        assertEquals(0, SystemUtils.toJavaVersionInt(""));
-        assertEquals(0, SystemUtils.toJavaVersionInt("0"));
-        assertEquals(110, SystemUtils.toJavaVersionInt("1.1"));
-        assertEquals(120, SystemUtils.toJavaVersionInt("1.2"));
-        assertEquals(130, SystemUtils.toJavaVersionInt("1.3.0"));
-        assertEquals(131, SystemUtils.toJavaVersionInt("1.3.1"));
-        assertEquals(140, SystemUtils.toJavaVersionInt("1.4.0"));
-        assertEquals(141, SystemUtils.toJavaVersionInt("1.4.1"));
-        assertEquals(142, SystemUtils.toJavaVersionInt("1.4.2"));
-        assertEquals(150, SystemUtils.toJavaVersionInt("1.5.0"));
-        assertEquals(160, SystemUtils.toJavaVersionInt("1.6.0"));
-        assertEquals(131, SystemUtils.toJavaVersionInt("JavaVM-1.3.1"));
-        assertEquals(131, SystemUtils.toJavaVersionInt("1.3.1 subset"));
+        assertEquals(0, SystemUtils.toJavaVersionInt(null), 0.000001f);
+        assertEquals(0, SystemUtils.toJavaVersionInt(""), 0.000001f);
+        assertEquals(0, SystemUtils.toJavaVersionInt("0"), 0.000001f);
+        assertEquals(110, SystemUtils.toJavaVersionInt("1.1"), 0.000001f);
+        assertEquals(120, SystemUtils.toJavaVersionInt("1.2"), 0.000001f);
+        assertEquals(130, SystemUtils.toJavaVersionInt("1.3.0"), 0.000001f);
+        assertEquals(131, SystemUtils.toJavaVersionInt("1.3.1"), 0.000001f);
+        assertEquals(140, SystemUtils.toJavaVersionInt("1.4.0"), 0.000001f);
+        assertEquals(141, SystemUtils.toJavaVersionInt("1.4.1"), 0.000001f);
+        assertEquals(142, SystemUtils.toJavaVersionInt("1.4.2"), 0.000001f);
+        assertEquals(150, SystemUtils.toJavaVersionInt("1.5.0"), 0.000001f);
+        assertEquals(160, SystemUtils.toJavaVersionInt("1.6.0"), 0.000001f);
+        assertEquals(131, SystemUtils.toJavaVersionInt("JavaVM-1.3.1"), 0.000001f);
+        assertEquals(131, SystemUtils.toJavaVersionInt("1.3.1 subset"), 0.000001f);
         // This used to return 0f in [lang] version 2.5:
-        assertEquals(130, SystemUtils.toJavaVersionInt("XXX-1.3.x"));
+        assertEquals(130, SystemUtils.toJavaVersionInt("XXX-1.3.x"), 0.000001f);
     }
 
     public void testJavaVersionAtLeastFloat() {
