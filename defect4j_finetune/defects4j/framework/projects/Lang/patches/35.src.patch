diff --git a/src/main/java/org/apache/commons/lang3/ArrayUtils.java b/src/main/java/org/apache/commons/lang3/ArrayUtils.java
index 3a543ce..ac22f8f 100644
--- a/src/main/java/org/apache/commons/lang3/ArrayUtils.java
+++ b/src/main/java/org/apache/commons/lang3/ArrayUtils.java
@@ -3292,7 +3292,7 @@ public class ArrayUtils {
         } else if (element != null) {
             type = element.getClass();
         } else {
-            throw new IllegalArgumentException("Arguments cannot both be null");            
+            type = Object.class;
         }
         @SuppressWarnings("unchecked") // type must be T
         T[] newArray = (T[]) copyArrayGrow1(array, type);
@@ -3571,7 +3571,7 @@ public class ArrayUtils {
         } else if (element != null) {
             clss = element.getClass();
         } else {
-            throw new IllegalArgumentException("Array and element cannot both be null");            
+            return (T[]) new Object[] { null };
         }
         @SuppressWarnings("unchecked") // the add method creates an array of type clss, which is type T
         final T[] newArray = (T[]) add(array, index, element, clss);
