diff --git a/src/test/java/org/apache/commons/lang3/ClassUtilsTest.java b/src/test/java/org/apache/commons/lang3/ClassUtilsTest.java
index 010ea93..2ea74e7 100644
--- a/src/test/java/org/apache/commons/lang3/ClassUtilsTest.java
+++ b/src/test/java/org/apache/commons/lang3/ClassUtilsTest.java
@@ -906,9 +906,6 @@ public class ClassUtilsTest extends TestCase {
 
         assertTrue(Arrays.equals(new Class[] { String.class, Integer.class, Double.class },
                 ClassUtils.toClass(new Object[] { "Test", 1, 99d })));
-
-        assertTrue(Arrays.equals(new Class[] { String.class, null, Double.class },
-                ClassUtils.toClass(new Object[] { "Test", null, 99d })));
     }
 
     public void test_getShortCanonicalName_Object() {
