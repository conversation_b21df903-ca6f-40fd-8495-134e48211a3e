diff --git a/src/test/java/org/apache/commons/lang3/StringUtilsTest.java b/src/test/java/org/apache/commons/lang3/StringUtilsTest.java
index bad56e9..b11a633 100644
--- a/src/test/java/org/apache/commons/lang3/StringUtilsTest.java
+++ b/src/test/java/org/apache/commons/lang3/StringUtilsTest.java
@@ -2181,14 +2181,4 @@ public class StringUtilsTest {
         expectedBytes = expectedString.getBytes(encoding);
         assertEquals(expectedString, StringUtils.toString(expectedBytes, encoding));
     }
-    
-    @Test
-    public void testEscapeSurrogatePairs() throws Exception {
-        assertEquals("\uD83D\uDE30", StringEscapeUtils.escapeCsv("\uD83D\uDE30"));
-        // Examples from https://en.wikipedia.org/wiki/UTF-16
-        assertEquals("\uD800\uDC00", StringEscapeUtils.escapeCsv("\uD800\uDC00"));
-        assertEquals("\uD834\uDD1E", StringEscapeUtils.escapeCsv("\uD834\uDD1E"));
-        assertEquals("\uDBFF\uDFFD", StringEscapeUtils.escapeCsv("\uDBFF\uDFFD"));
-        
-    }
 }
