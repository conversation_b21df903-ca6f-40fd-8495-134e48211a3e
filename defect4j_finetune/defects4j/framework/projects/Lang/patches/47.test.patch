diff --git a/src/test/org/apache/commons/lang/text/StrBuilderTest.java b/src/test/org/apache/commons/lang/text/StrBuilderTest.java
index d589308..67e1ce1 100644
--- a/src/test/org/apache/commons/lang/text/StrBuilderTest.java
+++ b/src/test/org/apache/commons/lang/text/StrBuilderTest.java
@@ -1749,17 +1749,4 @@ public class StrBuilderTest extends TestCase {
         assertEquals( "The indexOf(char) method is looking beyond the end of the string", -1, sb.indexOf('h'));
     }
 
-    //-----------------------------------------------------------------------
-    public void testLang412Right() {
-        StrBuilder sb = new StrBuilder();
-        sb.appendFixedWidthPadRight(null, 10, '*');
-        assertEquals( "Failed to invoke appendFixedWidthPadRight correctly", "**********", sb.toString());
-    }
-
-    public void testLang412Left() {
-        StrBuilder sb = new StrBuilder();
-        sb.appendFixedWidthPadLeft(null, 10, '*');
-        assertEquals( "Failed to invoke appendFixedWidthPadLeft correctly", "**********", sb.toString());
-    }
-
 }
