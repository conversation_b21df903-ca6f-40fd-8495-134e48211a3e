diff --git a/src/test/org/apache/commons/lang/StringEscapeUtilsTest.java b/src/test/org/apache/commons/lang/StringEscapeUtilsTest.java
index 18e6233..e993ae6 100644
--- a/src/test/org/apache/commons/lang/StringEscapeUtilsTest.java
+++ b/src/test/org/apache/commons/lang/StringEscapeUtilsTest.java
@@ -415,18 +415,4 @@ public class StringEscapeUtilsTest extends TestCase {
             fail("Threw: " + e);
         }
     }
-
-    // https://issues.apache.org/jira/browse/LANG-480
-    public void testEscapeHtmlHighUnicode() throws java.io.UnsupportedEncodingException {
-        // this is the utf8 representation of the character:
-        // COUNTING ROD UNIT DIGIT THREE
-        // in unicode
-        // codepoint: U+1D362
-        byte[] data = new byte[] { (byte)0xF0, (byte)0x9D, (byte)0x8D, (byte)0xA2 };
-
-        String escaped = StringEscapeUtils.escapeHtml( new String(data, "UTF8") );
-        String unescaped = StringEscapeUtils.unescapeHtml( escaped );
-
-        assertEquals( "High unicode was not escaped correctly", "&#119650;", escaped);
-    }
 }
