diff --git a/src/test/java/org/apache/commons/lang3/LocaleUtilsTest.java b/src/test/java/org/apache/commons/lang3/LocaleUtilsTest.java
index b30b3e3..84e3b66 100644
--- a/src/test/java/org/apache/commons/lang3/LocaleUtilsTest.java
+++ b/src/test/java/org/apache/commons/lang3/LocaleUtilsTest.java
@@ -493,53 +493,7 @@ public class LocaleUtilsTest  {
      */
     @Test
     public void testLang328() {
-        assertValidToLocale("fr__P", "fr", "", "P");
         assertValidToLocale("fr__POSIX", "fr", "", "POSIX");
     }
 
-    /**
-     * Tests #LANG-865, strings starting with an underscore.
-     */
-    @Test
-    public void testLang865() {
-        assertValidToLocale("_GB", "", "GB", "");
-        assertValidToLocale("_GB_P", "", "GB", "P");
-        assertValidToLocale("_GB_POSIX", "", "GB", "POSIX");
-        try {
-            LocaleUtils.toLocale("_G");
-            fail("Must be at least 3 chars if starts with underscore");
-        } catch (final IllegalArgumentException iae) {
-        }
-        try {
-            LocaleUtils.toLocale("_Gb");
-            fail("Must be uppercase if starts with underscore");
-        } catch (final IllegalArgumentException iae) {
-        }
-        try {
-            LocaleUtils.toLocale("_gB");
-            fail("Must be uppercase if starts with underscore");
-        } catch (final IllegalArgumentException iae) {
-        }
-        try {
-            LocaleUtils.toLocale("_1B");
-            fail("Must be letter if starts with underscore");
-        } catch (final IllegalArgumentException iae) {
-        }
-        try {
-            LocaleUtils.toLocale("_G1");
-            fail("Must be letter if starts with underscore");
-        } catch (final IllegalArgumentException iae) {
-        }
-        try {
-            LocaleUtils.toLocale("_GB_");
-            fail("Must be at least 5 chars if starts with underscore");
-        } catch (final IllegalArgumentException iae) {
-        }
-        try {
-            LocaleUtils.toLocale("_GBAP");
-            fail("Must have underscore after the country if starts with underscore and is at least 5 chars");
-        } catch (final IllegalArgumentException iae) {
-        }
-    }
-
 }
