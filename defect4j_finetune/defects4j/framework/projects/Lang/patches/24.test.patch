diff --git a/src/test/java/org/apache/commons/lang3/math/NumberUtilsTest.java b/src/test/java/org/apache/commons/lang3/math/NumberUtilsTest.java
index bb94ab3..779eb74 100644
--- a/src/test/java/org/apache/commons/lang3/math/NumberUtilsTest.java
+++ b/src/test/java/org/apache/commons/lang3/math/NumberUtilsTest.java
@@ -1139,10 +1139,6 @@ public class NumberUtilsTest extends TestCase {
         // LANG-521
         val = "2.";
         assertTrue("isNumber(String) LANG-521 failed", NumberUtils.isNumber(val));
-
-        // LANG-664
-        val = "1.1L";
-        assertFalse("isNumber(String) LANG-664 failed", NumberUtils.isNumber(val));
     }
 
     private boolean checkCreateNumber(String val) {
