diff --git a/src/test/java/org/apache/commons/lang3/time/DateUtilsTest.java b/src/test/java/org/apache/commons/lang3/time/DateUtilsTest.java
index 3fc737a..4fb8ec2 100644
--- a/src/test/java/org/apache/commons/lang3/time/DateUtilsTest.java
+++ b/src/test/java/org/apache/commons/lang3/time/DateUtilsTest.java
@@ -227,14 +227,6 @@ public class DateUtilsTest extends TestCase {
         cal2.set(2004, 6, 9, 13, 45, 0);
         cal2.set(Calendar.MILLISECOND, 0);
         assertEquals(true, DateUtils.isSameLocalTime(cal1, cal2));
-
-        Calendar cal3 = Calendar.getInstance();
-        Calendar cal4 = Calendar.getInstance();
-        cal3.set(2004, 6, 9, 4,  0, 0);
-        cal4.set(2004, 6, 9, 16, 0, 0);
-        cal3.set(Calendar.MILLISECOND, 0);
-        cal4.set(Calendar.MILLISECOND, 0);
-        assertFalse("LANG-677", DateUtils.isSameLocalTime(cal3, cal4));
         
         cal2.set(2004, 6, 9, 11, 45, 0);
         assertEquals(false, DateUtils.isSameLocalTime(cal1, cal2));
