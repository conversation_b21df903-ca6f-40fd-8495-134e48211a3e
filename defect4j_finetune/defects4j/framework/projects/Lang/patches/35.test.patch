diff --git a/src/test/java/org/apache/commons/lang3/ArrayUtilsAddTest.java b/src/test/java/org/apache/commons/lang3/ArrayUtilsAddTest.java
index 8d2e0e1..e38e481 100644
--- a/src/test/java/org/apache/commons/lang3/ArrayUtilsAddTest.java
+++ b/src/test/java/org/apache/commons/lang3/ArrayUtilsAddTest.java
@@ -221,25 +221,6 @@ public class ArrayUtilsAddTest extends TestCase {
         assertTrue(Arrays.equals((new Object[]{null}), newArray));
         assertEquals(Object.class, newArray.getClass().getComponentType());
     }
-    
-    public void testLANG571(){
-        String[] stringArray=null;
-        String aString=null;
-        try {
-            @SuppressWarnings("unused")
-            String[] sa = ArrayUtils.add(stringArray, aString);
-            fail("Should have caused IllegalArgumentException");
-        } catch (IllegalArgumentException iae){
-            //expected
-        }
-        try {
-            @SuppressWarnings("unused")
-            String[] sa = ArrayUtils.add(stringArray, 0, aString);
-            fail("Should have caused IllegalArgumentException");
-        } catch (IllegalArgumentException iae){
-            //expected
-        }
-    }
 
     public void testAddObjectArrayToObjectArray() {
         assertNull(ArrayUtils.addAll((Object[]) null, (Object[]) null));
