diff --git a/src/java/org/apache/commons/lang/time/StopWatch.java b/src/java/org/apache/commons/lang/time/StopWatch.java
index 0f0786a..8f39421 100644
--- a/src/java/org/apache/commons/lang/time/StopWatch.java
+++ b/src/java/org/apache/commons/lang/time/StopWatch.java
@@ -115,9 +115,7 @@ public class StopWatch {
         if(this.runningState != STATE_RUNNING && this.runningState != STATE_SUSPENDED) {
             throw new IllegalStateException("Stopwatch is not running. ");
         }
-        if(this.runningState == STATE_RUNNING) {
             stopTime = System.currentTimeMillis();
-        }
         this.runningState = STATE_STOPPED;
     }
 
