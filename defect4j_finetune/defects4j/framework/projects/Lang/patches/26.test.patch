diff --git a/src/test/java/org/apache/commons/lang3/time/FastDateFormatTest.java b/src/test/java/org/apache/commons/lang3/time/FastDateFormatTest.java
index 3853cdd..45156e3 100644
--- a/src/test/java/org/apache/commons/lang3/time/FastDateFormatTest.java
+++ b/src/test/java/org/apache/commons/lang3/time/FastDateFormatTest.java
@@ -322,16 +322,4 @@ public class FastDateFormatTest extends TestCase {
         FastDateFormat format = FastDateFormat.getInstance("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", TimeZone.getTimeZone("GMT"));
         assertEquals("dateTime", dateTime, format.format(cal));
     }
-
-    public void testLang645() {
-        Locale locale = new Locale("sv", "SE");
-
-        Calendar cal = Calendar.getInstance();
-        cal.set(2010, 0, 1, 12, 0, 0);
-        Date d = cal.getTime();
-
-        FastDateFormat fdf = FastDateFormat.getInstance("EEEE', week 'ww", locale);
-
-        assertEquals("fredag, week 53", fdf.format(d));
-    }
 }
