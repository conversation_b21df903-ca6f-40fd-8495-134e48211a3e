diff --git a/src/java/org/apache/commons/lang/time/FastDateFormat.java b/src/java/org/apache/commons/lang/time/FastDateFormat.java
index d1caaa8..8a26f28 100644
--- a/src/java/org/apache/commons/lang/time/FastDateFormat.java
+++ b/src/java/org/apache/commons/lang/time/FastDateFormat.java
@@ -282,14 +282,16 @@ public class FastDateFormat extends Format {
             key = new Pair(key, timeZone);
         }
 
-        if (locale == null) {
-            locale = Locale.getDefault();
+        if (locale != null) {
+            key = new Pair(key, locale);
         }
 
-        key = new Pair(key, locale);
 
         FastDateFormat format = (FastDateFormat) cDateInstanceCache.get(key);
         if (format == null) {
+            if (locale == null) {
+                locale = Locale.getDefault();
+            }
             try {
                 SimpleDateFormat formatter = (SimpleDateFormat) DateFormat.getDateInstance(style, locale);
                 String pattern = formatter.toPattern();
@@ -460,13 +462,15 @@ public class FastDateFormat extends Format {
         if (timeZone != null) {
             key = new Pair(key, timeZone);
         }
-        if (locale == null) {
-            locale = Locale.getDefault();
+        if (locale != null) {
+            key = new Pair(key, locale);
         }
-        key = new Pair(key, locale);
 
         FastDateFormat format = (FastDateFormat) cDateTimeInstanceCache.get(key);
         if (format == null) {
+            if (locale == null) {
+                locale = Locale.getDefault();
+            }
             try {
                 SimpleDateFormat formatter = (SimpleDateFormat) DateFormat.getDateTimeInstance(dateStyle, timeStyle,
                         locale);
