diff --git a/src/test/java/org/apache/commons/lang3/math/FractionTest.java b/src/test/java/org/apache/commons/lang3/math/FractionTest.java
index 8e21e7c..3116f4e 100644
--- a/src/test/java/org/apache/commons/lang3/math/FractionTest.java
+++ b/src/test/java/org/apache/commons/lang3/math/FractionTest.java
@@ -330,11 +330,6 @@ public class FractionTest extends TestCase {
             f = Fraction.getReducedFraction(-7, Integer.MIN_VALUE);  
             fail("Expecting ArithmeticException");
         } catch (ArithmeticException ex) {}      
-
-        // LANG-662
-        f = Fraction.getReducedFraction(Integer.MIN_VALUE, 2);
-        assertEquals(Integer.MIN_VALUE / 2, f.getNumerator());
-        assertEquals(1, f.getDenominator());
     }
 
     public void testFactory_double() {
@@ -648,11 +643,6 @@ public class FractionTest extends TestCase {
         assertEquals(0, result.getNumerator());
         assertEquals(1, result.getDenominator());
         assertSame(result, Fraction.ZERO);
-
-        f = Fraction.getFraction(Integer.MIN_VALUE, 2);
-        result = f.reduce();
-        assertEquals(Integer.MIN_VALUE / 2, result.getNumerator());
-        assertEquals(1, result.getDenominator());
     }
     
     public void testInvert() {
