diff --git a/src/java/org/apache/commons/lang/builder/EqualsBuilder.java b/src/java/org/apache/commons/lang/builder/EqualsBuilder.java
index 6901c8e..a783b35 100644
--- a/src/java/org/apache/commons/lang/builder/EqualsBuilder.java
+++ b/src/java/org/apache/commons/lang/builder/EqualsBuilder.java
@@ -377,8 +377,8 @@ public class EqualsBuilder {
         }
         Class lhsClass = lhs.getClass();
         if (!lhsClass.isArray()) {
             // The simple case, not an array, just test the element
-            isEquals = lhs.equals(rhs);
+            isEquals = lhs.equals(rhs);
         } else if (lhs.getClass() != rhs.getClass()) {
             // Here when we compare different dimensions, for example: a boolean[][] to a boolean[] 
             this.setEquals(false);
