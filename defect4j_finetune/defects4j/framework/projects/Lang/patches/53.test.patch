diff --git a/src/test/org/apache/commons/lang/time/DateUtilsTest.java b/src/test/org/apache/commons/lang/time/DateUtilsTest.java
index 6ded24d..6f6a434 100644
--- a/src/test/org/apache/commons/lang/time/DateUtilsTest.java
+++ b/src/test/org/apache/commons/lang/time/DateUtilsTest.java
@@ -693,69 +693,6 @@ public class DateUtilsTest extends TestCase {
     }
 
     /**
-     * Tests the Changes Made by LANG-346 to the DateUtils.modify() private method invoked
-     * by DateUtils.round().
-     */
-    public void testRoundLang346() throws Exception
-    {
-        TimeZone.setDefault(defaultZone);
-        dateTimeParser.setTimeZone(defaultZone);
-        Calendar testCalendar = Calendar.getInstance();
-        testCalendar.set(2007, 6, 2, 8, 8, 50);
-        Date date = testCalendar.getTime();
-        assertEquals("Minute Round Up Failed",
-                     dateTimeParser.parse("July 2, 2007 08:09:00.000"),
-                     DateUtils.round(date, Calendar.MINUTE));
-
-        testCalendar.set(2007, 6, 2, 8, 8, 20);
-        date = testCalendar.getTime();
-        assertEquals("Minute No Round Failed",
-                     dateTimeParser.parse("July 2, 2007 08:08:00.000"),
-                     DateUtils.round(date, Calendar.MINUTE));
-
-        testCalendar.set(2007, 6, 2, 8, 8, 50);
-        testCalendar.set(Calendar.MILLISECOND, 600);
-        date = testCalendar.getTime();
-
-        assertEquals("Second Round Up with 600 Milli Seconds Failed",
-                     dateTimeParser.parse("July 2, 2007 08:08:51.000"),
-                     DateUtils.round(date, Calendar.SECOND));
-
-        testCalendar.set(2007, 6, 2, 8, 8, 50);
-        testCalendar.set(Calendar.MILLISECOND, 200);
-        date = testCalendar.getTime();
-        assertEquals("Second Round Down with 200 Milli Seconds Failed",
-                     dateTimeParser.parse("July 2, 2007 08:08:50.000"),
-                     DateUtils.round(date, Calendar.SECOND));
-
-        testCalendar.set(2007, 6, 2, 8, 8, 20);
-        testCalendar.set(Calendar.MILLISECOND, 600);
-        date = testCalendar.getTime();
-        assertEquals("Second Round Up with 200 Milli Seconds Failed",
-                     dateTimeParser.parse("July 2, 2007 08:08:21.000"),
-                     DateUtils.round(date, Calendar.SECOND));
-
-        testCalendar.set(2007, 6, 2, 8, 8, 20);
-        testCalendar.set(Calendar.MILLISECOND, 200);
-        date = testCalendar.getTime();
-        assertEquals("Second Round Down with 200 Milli Seconds Failed",
-                     dateTimeParser.parse("July 2, 2007 08:08:20.000"),
-                     DateUtils.round(date, Calendar.SECOND));
-
-        testCalendar.set(2007, 6, 2, 8, 8, 50);
-        date = testCalendar.getTime();
-        assertEquals("Hour Round Down Failed",
-                     dateTimeParser.parse("July 2, 2007 08:00:00.000"),
-                     DateUtils.round(date, Calendar.HOUR));
-
-        testCalendar.set(2007, 6, 2, 8, 31, 50);
-        date = testCalendar.getTime();
-        assertEquals("Hour Round Up Failed",
-                     dateTimeParser.parse("July 2, 2007 09:00:00.000"),
-                     DateUtils.round(date, Calendar.HOUR));
-    }
-
-    /**
      * Tests various values with the trunc method
      */
     public void testTruncate() throws Exception {
