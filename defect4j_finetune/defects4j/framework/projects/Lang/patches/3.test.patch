diff --git a/src/test/java/org/apache/commons/lang3/math/NumberUtilsTest.java b/src/test/java/org/apache/commons/lang3/math/NumberUtilsTest.java
index 56b0597..b41c030 100644
--- a/src/test/java/org/apache/commons/lang3/math/NumberUtilsTest.java
+++ b/src/test/java/org/apache/commons/lang3/math/NumberUtilsTest.java
@@ -115,20 +115,7 @@ public class NumberUtilsTest {
         assertTrue("toFloat(String,int) 1 failed", NumberUtils.toFloat("1.2345", 5.1f) == 1.2345f);
         assertTrue("toFloat(String,int) 2 failed", NumberUtils.toFloat("a", 5.0f) == 5.0f);
     }
-    
-    /**
-     * Test for {(@link NumberUtils#createNumber(String)}
-     */
-    @Test
-    public void testStringCreateNumberEnsureNoPrecisionLoss(){
-        String shouldBeFloat = "1.23";
-        String shouldBeDouble = "3.40282354e+38";
-        String shouldBeBigDecimal = "1.797693134862315759e+308";
-        
-        assertTrue(NumberUtils.createNumber(shouldBeFloat) instanceof Float);
-        assertTrue(NumberUtils.createNumber(shouldBeDouble) instanceof Double);
-        assertTrue(NumberUtils.createNumber(shouldBeBigDecimal) instanceof BigDecimal);
-    }
+
     /**
      * Test for {@link NumberUtils#toDouble(String)}.
      */
