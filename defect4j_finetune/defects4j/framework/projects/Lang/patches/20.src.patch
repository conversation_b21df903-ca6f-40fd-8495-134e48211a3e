diff --git a/src/main/java/org/apache/commons/lang3/StringUtils.java b/src/main/java/org/apache/commons/lang3/StringUtils.java
index 711852e..3c2cf3f 100644
--- a/src/main/java/org/apache/commons/lang3/StringUtils.java
+++ b/src/main/java/org/apache/commons/lang3/StringUtils.java
@@ -3295,7 +3295,7 @@ public class StringUtils {
             return EMPTY;
         }
         
-        StringBuilder buf = new StringBuilder(noOfItems * 16);
+        StringBuilder buf = new StringBuilder((array[startIndex] == null ? 16 : array[startIndex].toString().length()) + 1);
 
         for (int i = startIndex; i < endIndex; i++) {
             if (i > startIndex) {
@@ -3380,7 +3380,7 @@ public class StringUtils {
             return EMPTY;
         }
 
-        StringBuilder buf = new StringBuilder(noOfItems * 16);
+        StringBuilder buf = new StringBuilder((array[startIndex] == null ? 16 : array[startIndex].toString().length()) + separator.length());
 
         for (int i = startIndex; i < endIndex; i++) {
             if (i > startIndex) {
