diff --git a/src/java/org/apache/commons/lang/time/DateUtils.java b/src/java/org/apache/commons/lang/time/DateUtils.java
index e5138b5..0dd0ded 100644
--- a/src/java/org/apache/commons/lang/time/DateUtils.java
+++ b/src/java/org/apache/commons/lang/time/DateUtils.java
@@ -640,18 +640,18 @@ public class DateUtils {
         int millisecs = val.get(Calendar.MILLISECOND);
         if (!round || millisecs < 500) {
             time = time - millisecs;
-        }
         if (field == Calendar.SECOND) {
             done = true;
+            }
         }
 
         // truncate seconds
         int seconds = val.get(Calendar.SECOND);
         if (!done && (!round || seconds < 30)) {
             time = time - (seconds * 1000L);
-        }
         if (field == Calendar.MINUTE) {
             done = true;
+            }
         }
 
         // truncate minutes
