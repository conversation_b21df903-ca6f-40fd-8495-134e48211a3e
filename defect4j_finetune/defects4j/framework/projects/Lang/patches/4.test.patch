diff --git a/src/test/java/org/apache/commons/lang3/text/translate/LookupTranslatorTest.java b/src/test/java/org/apache/commons/lang3/text/translate/LookupTranslatorTest.java
index 67419f8..af4d684 100644
--- a/src/test/java/org/apache/commons/lang3/text/translate/LookupTranslatorTest.java
+++ b/src/test/java/org/apache/commons/lang3/text/translate/LookupTranslatorTest.java
@@ -39,14 +39,4 @@ public class LookupTranslatorTest  {
         assertEquals("Incorrect value", "two", out.toString());
     }
 
-    // Tests: https://issues.apache.org/jira/browse/LANG-882
-    @Test
-    public void testLang882() throws IOException {
-        final LookupTranslator lt = new LookupTranslator(new CharSequence[][] { { new StringBuffer("one"), new StringBuffer("two") } });
-        final StringWriter out = new StringWriter();
-        final int result = lt.translate(new StringBuffer("one"), 0, out);
-        assertEquals("Incorrect codepoint consumption", 3, result);
-        assertEquals("Incorrect value", "two", out.toString());
-    }
-
 }
