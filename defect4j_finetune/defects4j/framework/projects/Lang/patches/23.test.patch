diff --git a/src/test/java/org/apache/commons/lang3/text/ExtendedMessageFormatTest.java b/src/test/java/org/apache/commons/lang3/text/ExtendedMessageFormatTest.java
index e2bde90..845a2bd 100644
--- a/src/test/java/org/apache/commons/lang3/text/ExtendedMessageFormatTest.java
+++ b/src/test/java/org/apache/commons/lang3/text/ExtendedMessageFormatTest.java
@@ -265,48 +265,6 @@ public class ExtendedMessageFormatTest extends TestCase {
     }
 
     /**
-     * Test equals() and hashcode.
-     */
-    public void testEqualsHashcode() {
-        Map<String, ? extends FormatFactory> registry = Collections.singletonMap("testfmt", new LowerCaseFormatFactory());
-        Map<String, ? extends FormatFactory> otherRegitry = Collections.singletonMap("testfmt", new UpperCaseFormatFactory());
-
-        String pattern = "Pattern: {0,testfmt}";
-        ExtendedMessageFormat emf = new ExtendedMessageFormat(pattern, Locale.US, registry);
-
-        ExtendedMessageFormat other = null;
-
-        // Same object
-        assertTrue("same, equals()",   emf.equals(emf));
-        assertTrue("same, hashcode()", emf.hashCode() == emf.hashCode());
-
-        // Equal Object
-        other = new ExtendedMessageFormat(pattern, Locale.US, registry);
-        assertTrue("equal, equals()",   emf.equals(other));
-        assertTrue("equal, hashcode()", emf.hashCode() == other.hashCode());
-
-        // Different Class
-        other = new OtherExtendedMessageFormat(pattern, Locale.US, registry);
-        assertFalse("class, equals()",  emf.equals(other));
-        assertTrue("class, hashcode()", emf.hashCode() == other.hashCode()); // same hashcode
-        
-        // Different pattern
-        other = new ExtendedMessageFormat("X" + pattern, Locale.US, registry);
-        assertFalse("pattern, equals()",   emf.equals(other));
-        assertFalse("pattern, hashcode()", emf.hashCode() == other.hashCode());
-
-        // Different registry
-        other = new ExtendedMessageFormat(pattern, Locale.US, otherRegitry);
-        assertFalse("registry, equals()",   emf.equals(other));
-        assertFalse("registry, hashcode()", emf.hashCode() == other.hashCode());
-
-        // Different Locale
-        other = new ExtendedMessageFormat(pattern, Locale.FRANCE, registry);
-        assertFalse("locale, equals()",  emf.equals(other));
-        assertTrue("locale, hashcode()", emf.hashCode() == other.hashCode()); // same hashcode
-    }
-
-    /**
      * Test a built in format for the specified Locales, plus <code>null</code> Locale.
      * @param pattern MessageFormat pattern
      * @param args MessageFormat arguments
@@ -436,16 +394,4 @@ public class ExtendedMessageFormatTest extends TestCase {
                             .getDateInstance(DateFormat.DEFAULT, locale);
         }
     }
-
-    /**
-     * Alternative ExtendedMessageFormat impl.
-     */
-    private static class OtherExtendedMessageFormat extends ExtendedMessageFormat {
-        public OtherExtendedMessageFormat(String pattern, Locale locale,
-                Map<String, ? extends FormatFactory> registry) {
-            super(pattern, locale, registry);
-        }
-        
-    }
-
 }
