diff --git a/src/main/java/org/apache/commons/lang3/text/ExtendedMessageFormat.java b/src/main/java/org/apache/commons/lang3/text/ExtendedMessageFormat.java
index 55ee7d1..a2cc169 100644
--- a/src/main/java/org/apache/commons/lang3/text/ExtendedMessageFormat.java
+++ b/src/main/java/org/apache/commons/lang3/text/ExtendedMessageFormat.java
@@ -70,7 +70,6 @@ import org.apache.commons.lang3.Validate;
  */
 public class ExtendedMessageFormat extends MessageFormat {
     private static final long serialVersionUID = -2362048321261811743L;
-    private static final int HASH_SEED = 31;
 
     private static final String DUMMY_PATTERN = "";
     private static final String ESCAPED_QUOTE = "''";
@@ -261,42 +260,12 @@ public class ExtendedMessageFormat extends MessageFormat {
      * @param obj the object to compare to
      * @return true if this object equals the other, otherwise false
      */
-    @Override
-    public boolean equals(Object obj) {
-        if (obj == this) {
-            return true;
-        }
-        if (obj == null) {
-            return false;
-        }
-        if (!super.equals(obj)) {
-            return false;
-        }
-        if (ObjectUtils.notEqual(getClass(), obj.getClass())) {
-          return false;
-        }
-        ExtendedMessageFormat rhs = (ExtendedMessageFormat)obj;
-        if (ObjectUtils.notEqual(toPattern, rhs.toPattern)) {
-            return false;
-        }
-        if (ObjectUtils.notEqual(registry, rhs.registry)) {
-            return false;
-        }
-        return true;
-    }
 
     /**
      * Return the hashcode.
      *
      * @return the hashcode
      */
-    @Override
-    public int hashCode() {
-        int result = super.hashCode();
-        result = HASH_SEED * result + ObjectUtils.hashCode(registry);
-        result = HASH_SEED * result + ObjectUtils.hashCode(toPattern);
-        return result;
-    }
 
     /**
      * Get a custom format from a format description.
