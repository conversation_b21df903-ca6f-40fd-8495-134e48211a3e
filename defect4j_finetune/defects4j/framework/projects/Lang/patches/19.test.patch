diff --git a/src/test/java/org/apache/commons/lang3/text/translate/NumericEntityUnescaperTest.java b/src/test/java/org/apache/commons/lang3/text/translate/NumericEntityUnescaperTest.java
index 6cfa772..9e2d249 100644
--- a/src/test/java/org/apache/commons/lang3/text/translate/NumericEntityUnescaperTest.java
+++ b/src/test/java/org/apache/commons/lang3/text/translate/NumericEntityUnescaperTest.java
@@ -36,20 +36,11 @@ public class NumericEntityUnescaperTest extends TestCase {
 
     public void testOutOfBounds() {
         NumericEntityUnescaper neu = new NumericEntityUnescaper();
-
-        assertEquals("Failed to ignore when last character is &", "Test &", neu.translate("Test &"));
-        assertEquals("Failed to ignore when last character is &", "Test &#", neu.translate("Test &#"));
-        assertEquals("Failed to ignore when last character is &", "Test &#x", neu.translate("Test &#x"));
-        assertEquals("Failed to ignore when last character is &", "Test &#X", neu.translate("Test &#X"));
-    }
-
-    public void testUnfinishedEntity() {
-        NumericEntityUnescaper neu = new NumericEntityUnescaper();
-        String input = "Test &#x30 not test";
-        String expected = "Test \u0030 not test";
+        String input = "Test &";
+        String expected = input;
 
         String result = neu.translate(input);
-        assertEquals("Failed to support unfinished entities (i.e. missing semi-colon", expected, result);
+        assertEquals("Failed to ignore when last character is &", expected, result);
     }
 
 }
