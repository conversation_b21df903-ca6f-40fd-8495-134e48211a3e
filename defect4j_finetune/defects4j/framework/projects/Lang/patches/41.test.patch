diff --git a/src/test/org/apache/commons/lang/ClassUtilsTest.java b/src/test/org/apache/commons/lang/ClassUtilsTest.java
index 896d542..48986d6 100644
--- a/src/test/org/apache/commons/lang/ClassUtilsTest.java
+++ b/src/test/org/apache/commons/lang/ClassUtilsTest.java
@@ -92,35 +92,6 @@ public class ClassUtilsTest extends TestCase {
         assertEquals("ClassUtils", ClassUtils.getShortClassName(ClassUtils.class));
         assertEquals("Map.Entry", ClassUtils.getShortClassName(Map.Entry.class));
         assertEquals("", ClassUtils.getShortClassName((Class) null));
-
-        // LANG-535
-        assertEquals("String[]", ClassUtils.getShortClassName(String[].class));
-        assertEquals("Map.Entry[]", ClassUtils.getShortClassName(Map.Entry[].class));
-
-        // Primitives
-        assertEquals("boolean", ClassUtils.getShortClassName(boolean.class));
-        assertEquals("byte", ClassUtils.getShortClassName(byte.class));
-        assertEquals("char", ClassUtils.getShortClassName(char.class));
-        assertEquals("short", ClassUtils.getShortClassName(short.class));
-        assertEquals("int", ClassUtils.getShortClassName(int.class));
-        assertEquals("long", ClassUtils.getShortClassName(long.class));
-        assertEquals("float", ClassUtils.getShortClassName(float.class));
-        assertEquals("double", ClassUtils.getShortClassName(double.class));
-
-        // Primitive Arrays
-        assertEquals("boolean[]", ClassUtils.getShortClassName(boolean[].class));
-        assertEquals("byte[]", ClassUtils.getShortClassName(byte[].class));
-        assertEquals("char[]", ClassUtils.getShortClassName(char[].class));
-        assertEquals("short[]", ClassUtils.getShortClassName(short[].class));
-        assertEquals("int[]", ClassUtils.getShortClassName(int[].class));
-        assertEquals("long[]", ClassUtils.getShortClassName(long[].class));
-        assertEquals("float[]", ClassUtils.getShortClassName(float[].class));
-        assertEquals("double[]", ClassUtils.getShortClassName(double[].class));
-
-        // Arrays of arrays of ...
-        assertEquals("String[][]", ClassUtils.getShortClassName(String[][].class));
-        assertEquals("String[][][]", ClassUtils.getShortClassName(String[][][].class));
-        assertEquals("String[][][][]", ClassUtils.getShortClassName(String[][][][].class));
     }
     
     public void test_getShortClassName_String() {
@@ -129,7 +100,7 @@ public class ClassUtilsTest extends TestCase {
         assertEquals("", ClassUtils.getShortClassName((String) null));
         assertEquals("", ClassUtils.getShortClassName(""));
     }
-
+    
     // -------------------------------------------------------------------------
     public void test_getPackageName_Object() {
         assertEquals("org.apache.commons.lang", ClassUtils.getPackageName(new ClassUtils(), "<null>"));
@@ -141,24 +112,6 @@ public class ClassUtilsTest extends TestCase {
         assertEquals("java.lang", ClassUtils.getPackageName(String.class));
         assertEquals("java.util", ClassUtils.getPackageName(Map.Entry.class));
         assertEquals("", ClassUtils.getPackageName((Class)null));
-
-        // LANG-535
-        assertEquals("java.lang", ClassUtils.getPackageName(String[].class));
-
-        // Primitive Arrays
-        assertEquals("", ClassUtils.getPackageName(boolean[].class));
-        assertEquals("", ClassUtils.getPackageName(byte[].class));
-        assertEquals("", ClassUtils.getPackageName(char[].class));
-        assertEquals("", ClassUtils.getPackageName(short[].class));
-        assertEquals("", ClassUtils.getPackageName(int[].class));
-        assertEquals("", ClassUtils.getPackageName(long[].class));
-        assertEquals("", ClassUtils.getPackageName(float[].class));
-        assertEquals("", ClassUtils.getPackageName(double[].class));
-
-        // Arrays of arrays of ...
-        assertEquals("java.lang", ClassUtils.getPackageName(String[][].class));
-        assertEquals("java.lang", ClassUtils.getPackageName(String[][][].class));
-        assertEquals("java.lang", ClassUtils.getPackageName(String[][][][].class));
     }
     
     public void test_getPackageName_String() {
