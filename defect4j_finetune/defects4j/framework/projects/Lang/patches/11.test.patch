diff --git a/src/test/java/org/apache/commons/lang3/RandomStringUtilsTest.java b/src/test/java/org/apache/commons/lang3/RandomStringUtilsTest.java
index a7f57bf..6c7b0c1 100644
--- a/src/test/java/org/apache/commons/lang3/RandomStringUtilsTest.java
+++ b/src/test/java/org/apache/commons/lang3/RandomStringUtilsTest.java
@@ -130,17 +130,6 @@ public class RandomStringUtilsTest extends junit.framework.TestCase {
         assertEquals("aaa", RandomStringUtils.random(3,0,0,false,false,new char[]{'a'},new Random(seed)));
     }
 
-    public void testLANG807() {
-        try {
-            RandomStringUtils.random(3,5,5,false,false);
-            fail("Expected IllegalArgumentException");
-        } catch (IllegalArgumentException ex) { // distinguish from Random#nextInt message
-            final String msg = ex.getMessage();
-            assertTrue("Message (" + msg + ") must contain 'start'", msg.contains("start"));
-            assertTrue("Message (" + msg + ") must contain 'end'", msg.contains("end"));
-        }
-    }
-
     public void testExceptions() {
         final char[] DUMMY = new char[]{'a'}; // valid char array
         try {
