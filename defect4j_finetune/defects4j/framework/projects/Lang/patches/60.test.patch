diff --git a/src/test/org/apache/commons/lang/text/StrBuilderTest.java b/src/test/org/apache/commons/lang/text/StrBuilderTest.java
index 67e1ce1..900b922 100644
--- a/src/test/org/apache/commons/lang/text/StrBuilderTest.java
+++ b/src/test/org/apache/commons/lang/text/StrBuilderTest.java
@@ -1741,12 +1741,4 @@ public class StrBuilderTest extends TestCase {
         assertEquals(-1, sb.indexOf("three"));
     }
 
-    //-----------------------------------------------------------------------
-    public void testLang295() {
-        StrBuilder sb = new StrBuilder("onetwothree");
-        sb.deleteFirst("three");
-        assertFalse( "The contains(char) method is looking beyond the end of the string", sb.contains('h'));
-        assertEquals( "The indexOf(char) method is looking beyond the end of the string", -1, sb.indexOf('h'));
-    }
-
 }
