diff --git a/src/java/org/apache/commons/lang/NumberUtils.java b/src/java/org/apache/commons/lang/NumberUtils.java
index c5ca8cd..18a05ef 100644
--- a/src/java/org/apache/commons/lang/NumberUtils.java
+++ b/src/java/org/apache/commons/lang/NumberUtils.java
@@ -142,9 +142,6 @@ public final class NumberUtils {
         if (val.length() == 0) {
             throw new NumberFormatException("\"\" is not a valid number.");
         }
-        if (val.length() == 1 && !Character.isDigit(val.charAt(0))) {
-            throw new NumberFormatException(val + " is not a valid number.");
-        }
         if (val.startsWith("--")) {
             // this is protection for poorness in java.lang.BigDecimal.
             // it accepts this as a legal value, but it does not appear 
