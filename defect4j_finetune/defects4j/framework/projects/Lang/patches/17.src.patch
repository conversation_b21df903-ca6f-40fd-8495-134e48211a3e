diff --git a/src/main/java/org/apache/commons/lang3/text/translate/CharSequenceTranslator.java b/src/main/java/org/apache/commons/lang3/text/translate/CharSequenceTranslator.java
index 4d010ea..4cb581d 100644
--- a/src/main/java/org/apache/commons/lang3/text/translate/CharSequenceTranslator.java
+++ b/src/main/java/org/apache/commons/lang3/text/translate/CharSequenceTranslator.java
@@ -80,20 +80,26 @@ public abstract class CharSequenceTranslator {
             return;
         }
         int pos = 0;
-        int len = input.length();
+        int len = Character.codePointCount(input, 0, input.length());
         while (pos < len) {
             int consumed = translate(input, pos, out);
             if (consumed == 0) {
                 char[] c = Character.toChars(Character.codePointAt(input, pos));
                 out.write(c);
-                pos+= c.length;
-                continue;
             }
+            else {
 //          // contract with translators is that they have to understand codepoints 
 //          // and they just took care of a surrogate pair
             for (int pt = 0; pt < consumed; pt++) {
+                    if (pos < len - 2) {
                 pos += Character.charCount(Character.codePointAt(input, pos));
+                    } else {
+                        pos++;
+                    }
+                }
+                pos--;
             }
+            pos++;
         }
     }
 
