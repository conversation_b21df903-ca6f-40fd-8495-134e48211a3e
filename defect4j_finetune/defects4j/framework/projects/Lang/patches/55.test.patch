diff --git a/src/test/org/apache/commons/lang/time/StopWatchTest.java b/src/test/org/apache/commons/lang/time/StopWatchTest.java
index bb7b412..46f063a 100644
--- a/src/test/org/apache/commons/lang/time/StopWatchTest.java
+++ b/src/test/org/apache/commons/lang/time/StopWatchTest.java
@@ -108,18 +108,6 @@ public class StopWatchTest extends TestCase {
         assertTrue(totalTime < 1300);
     }
 
-    public void testLang315() {
-        StopWatch watch = new StopWatch();
-        watch.start();
-            try {Thread.sleep(200);} catch (InterruptedException ex) {}
-        watch.suspend();
-        long suspendTime = watch.getTime();
-            try {Thread.sleep(200);} catch (InterruptedException ex) {}
-        watch.stop();
-        long totalTime = watch.getTime();
-        assertTrue( suspendTime == totalTime );
-    }
-
     // test bad states
     public void testBadStates() {
         StopWatch watch = new StopWatch();
