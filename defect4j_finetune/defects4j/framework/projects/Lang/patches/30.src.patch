diff --git a/src/main/java/org/apache/commons/lang3/StringUtils.java b/src/main/java/org/apache/commons/lang3/StringUtils.java
index da86fdb..dd2f5cf 100644
--- a/src/main/java/org/apache/commons/lang3/StringUtils.java
+++ b/src/main/java/org/apache/commons/lang3/StringUtils.java
@@ -1373,21 +1373,13 @@ public class StringUtils {
             return INDEX_NOT_FOUND;
         }
         int csLen = cs.length();
-        int csLast = csLen - 1;
         int searchLen = searchChars.length;
-        int searchLast = searchLen - 1;
         for (int i = 0; i < csLen; i++) {
             char ch = cs.charAt(i);
             for (int j = 0; j < searchLen; j++) {
                 if (searchChars[j] == ch) {
-                    if (i < csLast && j < searchLast && Character.isHighSurrogate(ch)) {
                         // ch is a supplementary character
-                        if (searchChars[j + 1] == cs.charAt(i + 1)) {
-                            return i;
-                        }
-                    } else {
                         return i;
-                    }
                 }
             }
         }
@@ -1448,7 +1440,7 @@ public class StringUtils {
      * <code>false</code> if no match or null input
      * @since 2.4
      */
-    public static boolean containsAny(String cs, char[] searchChars) {
+    public static boolean containsAny(CharSequence cs, char[] searchChars) {
         if (isEmpty(cs) || ArrayUtils.isEmpty(searchChars)) {
             return false;
         }
@@ -1460,12 +1452,9 @@ public class StringUtils {
             char ch = cs.charAt(i);
             for (int j = 0; j < searchLength; j++) {
                 if (searchChars[j] == ch) {
-                    if (Character.isHighSurrogate(ch)) {
-                        if (j == searchLast) {
+                    if (i < csLast && j < searchLast && ch >= Character.MIN_HIGH_SURROGATE && ch <= Character.MAX_HIGH_SURROGATE) {
                             // missing low surrogate, fine, like String.indexOf(String)
-                            return true;
-                        }
-                        if (i < csLast && searchChars[j + 1] == cs.charAt(i + 1)) {
+                        if (searchChars[j + 1] == cs.charAt(i + 1)) {
                             return true;
                         }
                     } else {
@@ -1505,7 +1494,7 @@ public class StringUtils {
      * @return the <code>true</code> if any of the chars are found, <code>false</code> if no match or null input
      * @since 2.4
      */
-    public static boolean containsAny(String cs, String searchChars) {
+    public static boolean containsAny(CharSequence cs, String searchChars) {
         if (searchChars == null) {
             return false;
         }
@@ -1541,21 +1530,13 @@ public class StringUtils {
             return INDEX_NOT_FOUND;
         }
         int csLen = cs.length();
-        int csLast = csLen - 1;
         int searchLen = searchChars.length;
-        int searchLast = searchLen - 1;
         outer:
         for (int i = 0; i < csLen; i++) {
             char ch = cs.charAt(i);
             for (int j = 0; j < searchLen; j++) {
                 if (searchChars[j] == ch) {
-                    if (i < csLast && j < searchLast && Character.isHighSurrogate(ch)) {
-                        if (searchChars[j + 1] == cs.charAt(i + 1)) {
-                            continue outer;
-                        }
-                    } else {
                         continue outer;
-                    }
                 }
             }
             return i;
@@ -1592,16 +1573,8 @@ public class StringUtils {
         int strLen = str.length();
         for (int i = 0; i < strLen; i++) {
             char ch = str.charAt(i);
-            boolean chFound = searchChars.indexOf(ch) >= 0;
-            if (i + 1 < strLen && Character.isHighSurrogate(ch)) {
-                char ch2 = str.charAt(i + 1);
-                if (chFound && searchChars.indexOf(ch2) < 0) {
+            if (searchChars.indexOf(ch) < 0) {
                     return i;
-                }
-            } else {
-                if (!chFound) {
-                    return i;
-                }
             }
         }
         return INDEX_NOT_FOUND;
@@ -1702,25 +1675,14 @@ public class StringUtils {
             return true;
         }
         int csLen = cs.length();
-        int csLast = csLen - 1;
         int searchLen = searchChars.length;
-        int searchLast = searchLen - 1;
         for (int i = 0; i < csLen; i++) {
             char ch = cs.charAt(i);
             for (int j = 0; j < searchLen; j++) {
                 if (searchChars[j] == ch) {
-                    if (Character.isHighSurrogate(ch)) {
-                        if (j == searchLast) {
                             // missing low surrogate, fine, like String.indexOf(String)
-                            return false;
-                        }
-                        if (i < csLast && searchChars[j + 1] == cs.charAt(i + 1)) {
-                            return false;
-                        }
-                    } else {
                         // ch is in the Basic Multilingual Plane
                         return false;
-                    }
                 }
             }
         }
