<!--
Copyright (c) 2014-2024 <PERSON>, <PERSON><PERSON><PERSON>, and Defect<PERSON><PERSON><PERSON> contributors.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERC<PERSON>NTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

################################################################################
               This is the project-specific build file for Time.

It defines project-specific properties and targets, and imports the build file
of the checked-out project version.
#############################################################################-->
<project name="D4J-Time">
    <!-- Project-specific dependencies -->
    <property name="config.jar" value="${d4j.home}/major/lib/major-rt.jar" />
    <property name="jodaconvert.jar" value="${d4j.home}/framework/projects/Time/lib/joda-convert-1.2.jar" />

    <!-- Do not download maven dependencies -->
    <property name="maven.settings.offline" value="true" />
    <property name="maven.repo.local" value="${d4j.home}/framework/projects/Time/lib/" />

    <!-- Generate all debugging information -->
    <property name="compile.debuglevel" value="lines,source,vars" />

    <!-- Include existing project build file -->
    <import file="${d4j.workdir}/build.xml" />

    <!-- Values necessary for mutation scripting to work -->
    <if> <isset property="ant.refid:compile.classpath" />
          <then>
            <property name="test.dir" value="${test.home}" />
          </then>
          <else>
            <property name="oldversion" value="yes" />
            <path id="compile.classpath" refid="build.classpath" />
            <path id="test.classpath"    refid="build.test.classpath" />
            <property name="test.dir" value="${maven.build.testDir.0}" />
            <property name="source.home" value="${maven.build.srcDir.0}" />
          </else>
    </if>
    <property name="test.home" value="${maven.build.testOutputDir}" />
    <condition property="classes.dir" value="${maven.build.outputDir}" else="${build.classes}">
        <isset property="maven.build.outputDir" />
    </condition>
    <condition property="test.classes.dir" value="${maven.build.testOutputDir}" else="${build.tests}">
        <isset property="maven.build.testOutputDir" />
    </condition>

    <property name="build.home" value="${classes.dir}/.." />

    <target name="compile.tests" depends="joda-time.compile.tests"> </target>

    <property name="tz.build.notneeded" value="true" />
    <target name="compile" depends="joda-time.compile">
      <if> <isset property="oldversion" /> <then>
        <java classname="org.joda.time.tz.ZoneInfoCompiler" failonerror="yes">
          <classpath>
            <path refid="compile.classpath" />
            <pathelement path="${classes.dir}" />
            <pathelement location="${config.jar}" />
            <path refid="cobertura.classpath.include" />
          </classpath>
          <sysproperty key="org.joda.time.DateTimeZone.Provider" value ="org.joda.time.tz.UTCProvider" />
          <arg value="-src" />
          <arg value="${maven.build.srcDir.0}/org/joda/time/tz/src" />
          <arg value="-dst" />
          <arg value="${maven.build.outputDir}/org/joda/time/tz/data" />
          <arg value="africa" />
          <arg value="antarctica" />
          <arg value="asia" />
          <arg value="australasia" />
          <arg value="europe" />
          <arg value="northamerica" />
          <arg value="southamerica" />
          <arg value="pacificnew" />
          <arg value="etcetera" />
          <arg value="backward" />
          <arg value="systemv" />
        </java>
      </then> <else>
        <mkdir dir="${build.tz}" />
        <java classname="org.joda.time.tz.ZoneInfoCompiler" failonerror="yes">
          <classpath>
            <path refid="compile.classpath" />
            <pathelement path="${classes.dir}" />
            <pathelement location="${config.jar}" />
            <path refid="cobertura.classpath.include" />
          </classpath>
          <sysproperty key="org.joda.time.DateTimeZone.Provider" value ="org.joda.time.tz.UTCProvider" />
          <arg line="-src ${source.tz} -dst ${build.tz}" />
          <arg value="africa" />
          <arg value="antarctica" />
          <arg value="asia" />
          <arg value="australasia" />
          <arg value="europe" />
          <arg value="northamerica" />
          <arg value="southamerica" />
          <arg value="pacificnew" />
          <arg value="etcetera" />
          <arg value="backward" />
          <arg value="systemv" />
        </java>
      </else> </if>
    </target>

    <!-- List of all developer-written tests that reliably pass on the fixed version -->
    <fileset id="all.manual.tests" dir="${test.dir}" excludes="${d4j.tests.exclude}">
        <include name="**/Test*.java" />
        <exclude name="**/TestAll*.java" />
        <exclude name="**/TestParseISO.java" />
        <exclude name="**/*Abstract*Test.java" />
        <exclude name="**/gj/*.java" />
        <include name="**/gj/TestAll.java" />
    </fileset>

    <!-- Classpath to run developer-written tests -->
    <path id="d4j.test.classpath">
        <path refid="compile.classpath" />
        <path refid="test.classpath" />
        <pathelement path="${classes.dir}" />
        <pathelement path="${test.home}" />
    </path>
</project>
