#Generated by Maven Ant Plugin - DO NOT EDIT THIS FILE!
#Wed Jan 15 13:40:40 PST 2014
maven.settings.offline=false
maven.build.finalName=joda-time-2.3-SNAPSHOT
maven.build.resourceDir.2=src/main/java
maven.build.resourceDir.1=.
maven.build.resourceDir.0=src/main/resources
maven.build.testOutputDir=${maven.build.dir}/test-classes
maven.build.testResourceDir.0=src/test/resources
maven.reporting.outputDirectory=${maven.build.dir}/site
project.build.sourceEncoding=UTF-8
maven.build.srcDir.0=src/main/java
project.build.directory=${maven.build.dir}
maven.test.reports=${maven.build.dir}/test-reports
maven.build.dir=target
project.build.outputDirectory=${maven.build.outputDir}
maven.build.testDir.0=src/test/java
maven.settings.interactiveMode=true
maven.repo.local=${user.home}/.m2/repository
maven.build.outputDir=${maven.build.dir}/classes
