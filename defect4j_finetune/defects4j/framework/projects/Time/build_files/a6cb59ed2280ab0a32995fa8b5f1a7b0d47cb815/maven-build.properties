#Generated by Ma<PERSON> Ant Plugin - DO NOT EDIT THIS FILE!
#Wed Jan 15 13:38:30 PST 2014
maven-toolchains-plugin.version=1.0
maven.build.testDir.0=src/test/java
maven-surefire-report-plugin.version=2.15
maven-repository-plugin.version=2.3.1
maven.settings.offline=false
maven.compiler.target=1.5
maven-clean-plugin.version=2.5
maven-install-plugin.version=2.4
maven-site-plugin.version=3.3
maven.settings.interactiveMode=true
maven-pmd-plugin.version=3.0.1
maven-project-info-reports-plugin.version=2.7
author=false
maven.reporting.outputDirectory=${maven.build.dir}/site
maven-resources-plugin.version=2.6
maven.compiler.compilerVersion=1.5
notimestamp=true
project.build.sourceEncoding=UTF-8
maven.build.testOutputDir=${maven.build.dir}/test-classes
maven.build.finalName=joda-time-2.4-SNAPSHOT
maven-source-plugin.version=2.2.1
maven.test.reports=${maven.build.dir}/test-reports
maven-deploy-plugin.version=2.7
checkstyle.config.location=/tmp/check_build_file_1389821800/src/main/checkstyle/checkstyle.xml
maven.compiler.source=1.5
maven-gpg-plugin.version=1.4
maven.compiler.optimize=true
maven.repo.local=${user.home}/.m2/repository
maven.build.dir=target
maven-jar-plugin.version=2.4
maven.build.outputDir=${maven.build.dir}/classes
project.reporting.outputEncoding=UTF-8
project.build.directory=${maven.build.dir}
maven.build.resourceDir.1=src/main/java
maven.build.resourceDir.0=.
maven-dependency-plugin.version=2.8
maven-plugin-plugin.version=3.2
maven.compiler.debuglevel=lines,source
maven-jxr-plugin.version=2.3
maven-assembly-plugin.version=2.4
maven-checkstyle-plugin.version=2.10
maven.build.srcDir.0=src/main/java
maven.compiler.fork=true
maven-javadoc-plugin.version=2.9.1
maven-compiler-plugin.version=3.1
maven-changes-plugin.version=2.9
maven.build.testResourceDir.0=src/test/resources
maven.compiler.debug=true
project.build.outputDirectory=${maven.build.outputDir}
maven-surefire-plugin.version=2.15
maven.compiler.verbose=true
