diff --git a/src/main/java/org/joda/time/format/PeriodFormatterBuilder.java b/src/main/java/org/joda/time/format/PeriodFormatterBuilder.java
index f7c7738..1533228 100644
--- a/src/main/java/org/joda/time/format/PeriodFormatterBuilder.java
+++ b/src/main/java/org/joda/time/format/PeriodFormatterBuilder.java
@@ -798,11 +798,9 @@ public class PeriodFormatterBuilder {
         int size = elementPairs.size();
         if (size >= 2 && elementPairs.get(0) instanceof Separator) {
             Separator sep = (Separator) elementPairs.get(0);
-            if (sep.iAfterParser == null && sep.iAfterPrinter == null) {
                 PeriodFormatter f = toFormatter(elementPairs.subList(2, size), notPrinter, notParser);
                 sep = sep.finish(f.getPrinter(), f.getParser());
                 return new PeriodFormatter(sep, sep);
-            }
         }
         Object[] comp = createComposite(elementPairs);
         if (notPrinter) {
