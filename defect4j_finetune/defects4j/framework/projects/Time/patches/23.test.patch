diff --git a/src/test/java/org/joda/time/TestDateTimeZone.java b/src/test/java/org/joda/time/TestDateTimeZone.java
index a6eaac3..bc98acf 100644
--- a/src/test/java/org/joda/time/TestDateTimeZone.java
+++ b/src/test/java/org/joda/time/TestDateTimeZone.java
@@ -29,9 +29,7 @@ import java.security.Permissions;
 import java.security.Policy;
 import java.security.ProtectionDomain;
 import java.util.HashSet;
-import java.util.LinkedHashMap;
 import java.util.Locale;
-import java.util.Map;
 import java.util.Set;
 import java.util.TimeZone;
 
@@ -240,53 +238,6 @@ public class TestDateTimeZone extends TestCase {
         } catch (IllegalArgumentException ex) {}
     }
 
-    public void testForID_String_old() {
-        Map<String, String> map = new LinkedHashMap<String, String>();
-        map.put("GMT", "UTC");
-        map.put("WET", "WET");
-        map.put("CET", "CET");
-        map.put("MET", "CET");
-        map.put("ECT", "CET");
-        map.put("EET", "EET");
-        map.put("MIT", "Pacific/Apia");
-        map.put("HST", "Pacific/Honolulu");
-        map.put("AST", "America/Anchorage");
-        map.put("PST", "America/Los_Angeles");
-        map.put("MST", "America/Denver");
-        map.put("PNT", "America/Phoenix");
-        map.put("CST", "America/Chicago");
-        map.put("EST", "America/New_York");
-        map.put("IET", "America/Indiana/Indianapolis");
-        map.put("PRT", "America/Puerto_Rico");
-        map.put("CNT", "America/St_Johns");
-        map.put("AGT", "America/Argentina/Buenos_Aires");
-        map.put("BET", "America/Sao_Paulo");
-        map.put("ART", "Africa/Cairo");
-        map.put("CAT", "Africa/Harare");
-        map.put("EAT", "Africa/Addis_Ababa");
-        map.put("NET", "Asia/Yerevan");
-        map.put("PLT", "Asia/Karachi");
-        map.put("IST", "Asia/Kolkata");
-        map.put("BST", "Asia/Dhaka");
-        map.put("VST", "Asia/Ho_Chi_Minh");
-        map.put("CTT", "Asia/Shanghai");
-        map.put("JST", "Asia/Tokyo");
-        map.put("ACT", "Australia/Darwin");
-        map.put("AET", "Australia/Sydney");
-        map.put("SST", "Pacific/Guadalcanal");
-        map.put("NST", "Pacific/Auckland");
-        for (String key : map.keySet()) {
-            String value = map.get(key);
-            TimeZone juZone = TimeZone.getTimeZone(key);
-            DateTimeZone zone = DateTimeZone.forTimeZone(juZone);
-            assertEquals(value, zone.getID());
-//            System.out.println(juZone);
-//            System.out.println(juZone.getDisplayName());
-//            System.out.println(zone);
-//            System.out.println("------");
-        }
-    }
-
     //-----------------------------------------------------------------------
     public void testForOffsetHours_int() {
         assertEquals(DateTimeZone.UTC, DateTimeZone.forOffsetHours(0));
