diff --git a/src/test/java/org/joda/time/TestDuration_Basics.java b/src/test/java/org/joda/time/TestDuration_Basics.java
index 5850b56..4caa6e2 100644
--- a/src/test/java/org/joda/time/TestDuration_Basics.java
+++ b/src/test/java/org/joda/time/TestDuration_Basics.java
@@ -444,51 +444,15 @@ public class TestDuration_Basics extends TestCase {
 
     //-----------------------------------------------------------------------
     public void testToPeriod() {
-        DateTimeZone zone = DateTimeZone.getDefault();
-        try {
-            DateTimeZone.setDefault(DateTimeZone.forID("Europe/Paris"));
-            long length =
-                (4L + (3L * 7L) + (2L * 30L) + 365L) * DateTimeConstants.MILLIS_PER_DAY +
-                5L * DateTimeConstants.MILLIS_PER_HOUR +
-                6L * DateTimeConstants.MILLIS_PER_MINUTE +
-                7L * DateTimeConstants.MILLIS_PER_SECOND + 8L;
-            Duration dur = new Duration(length);
-            Period test = dur.toPeriod();
-            assertEquals(0, test.getYears());  // (4 + (3 * 7) + (2 * 30) + 365) == 450
-            assertEquals(0, test.getMonths());
-            assertEquals(0, test.getWeeks());
-            assertEquals(0, test.getDays());
-            assertEquals((450 * 24) + 5, test.getHours());
-            assertEquals(6, test.getMinutes());
-            assertEquals(7, test.getSeconds());
-            assertEquals(8, test.getMillis());
-        } finally {
-            DateTimeZone.setDefault(zone);
-        }
-    }
-
-    public void testToPeriod_fixedZone() throws Throwable {
-        DateTimeZone zone = DateTimeZone.getDefault();
-        try {
-            DateTimeZone.setDefault(DateTimeZone.forOffsetHours(2));
-            long length =
-                (4L + (3L * 7L) + (2L * 30L) + 365L) * DateTimeConstants.MILLIS_PER_DAY +
-                5L * DateTimeConstants.MILLIS_PER_HOUR +
-                6L * DateTimeConstants.MILLIS_PER_MINUTE +
-                7L * DateTimeConstants.MILLIS_PER_SECOND + 8L;
-            Duration dur = new Duration(length);
-            Period test = dur.toPeriod();
-            assertEquals(0, test.getYears());  // (4 + (3 * 7) + (2 * 30) + 365) == 450
-            assertEquals(0, test.getMonths());
-            assertEquals(0, test.getWeeks());
-            assertEquals(0, test.getDays());
-            assertEquals((450 * 24) + 5, test.getHours());
-            assertEquals(6, test.getMinutes());
-            assertEquals(7, test.getSeconds());
-            assertEquals(8, test.getMillis());
-        } finally {
-            DateTimeZone.setDefault(zone);
-        }
+        long length =
+            (4L + (3L * 7L) + (2L * 30L) + 365L) * DateTimeConstants.MILLIS_PER_DAY +
+            5L * DateTimeConstants.MILLIS_PER_HOUR +
+            6L * DateTimeConstants.MILLIS_PER_MINUTE +
+            7L * DateTimeConstants.MILLIS_PER_SECOND + 8L;
+        Duration test = new Duration(length);
+        Period result = test.toPeriod();
+        assertEquals(new Period(test), result);
+        assertEquals(new Period(test.getMillis()), result);
     }
 
     //-----------------------------------------------------------------------
diff --git a/src/test/java/org/joda/time/TestPeriod_Constructors.java b/src/test/java/org/joda/time/TestPeriod_Constructors.java
index 29f7bdf..d1369ee 100644
--- a/src/test/java/org/joda/time/TestPeriod_Constructors.java
+++ b/src/test/java/org/joda/time/TestPeriod_Constructors.java
@@ -171,31 +171,6 @@ public class TestPeriod_Constructors extends TestCase {
         assertEquals(8, test.getMillis());
     }
 
-    public void testConstructor_long_fixedZone() throws Throwable {
-        DateTimeZone zone = DateTimeZone.getDefault();
-        try {
-            DateTimeZone.setDefault(DateTimeZone.forOffsetHours(2));
-            long length =
-                (4L + (3L * 7L) + (2L * 30L) + 365L) * DateTimeConstants.MILLIS_PER_DAY +
-                5L * DateTimeConstants.MILLIS_PER_HOUR +
-                6L * DateTimeConstants.MILLIS_PER_MINUTE +
-                7L * DateTimeConstants.MILLIS_PER_SECOND + 8L;
-            Period test = new Period(length);
-            assertEquals(PeriodType.standard(), test.getPeriodType());
-            // only time fields are precise in AllType
-            assertEquals(0, test.getYears());  // (4 + (3 * 7) + (2 * 30) + 365) == 450
-            assertEquals(0, test.getMonths());
-            assertEquals(0, test.getWeeks());
-            assertEquals(0, test.getDays());
-            assertEquals((450 * 24) + 5, test.getHours());
-            assertEquals(6, test.getMinutes());
-            assertEquals(7, test.getSeconds());
-            assertEquals(8, test.getMillis());
-        } finally {
-            DateTimeZone.setDefault(zone);
-        }
-    }
-
     //-----------------------------------------------------------------------
     public void testConstructor_long_PeriodType1() throws Throwable {
         long length = 4 * DateTimeConstants.MILLIS_PER_DAY +
