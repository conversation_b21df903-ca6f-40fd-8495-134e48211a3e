diff --git a/src/test/java/org/joda/time/tz/TestCompiler.java b/src/test/java/org/joda/time/tz/TestCompiler.java
index fc6cc86..eec41d5 100644
--- a/src/test/java/org/joda/time/tz/TestCompiler.java
+++ b/src/test/java/org/joda/time/tz/TestCompiler.java
@@ -84,30 +84,6 @@ public class TestCompiler extends TestCase {
         DateTimeZone.setDefault(originalDateTimeZone);
     }
 
-    //-----------------------------------------------------------------------
-    public void testDateTimeZoneBuilder() throws Exception {
-        // test multithreading, issue #18
-        getTestDataTimeZoneBuilder().toDateTimeZone("TestDTZ1", true);
-        final DateTimeZone[] zone = new DateTimeZone[1];
-        Thread t = new Thread(new Runnable() {
-            public void run() {
-                zone[0] = getTestDataTimeZoneBuilder().toDateTimeZone("TestDTZ2", true);
-            }
-        });
-        t.start();
-        t.join();
-        assertNotNull(zone[0]);
-    }
-
-    private DateTimeZoneBuilder getTestDataTimeZoneBuilder() {
-         return new DateTimeZoneBuilder()
-             .addCutover(1601, 'w', 1, 1, 1, false, 7200000)
-             .setStandardOffset(3600000)
-             .addRecurringSavings("", 3600000, 1601, Integer.MAX_VALUE, 'w', 3, -1, 1, false, 7200000)
-             .addRecurringSavings("", 0, 1601, Integer.MAX_VALUE, 'w', 10, -1, 1, false, 10800000);
-    }    
-
-    //-----------------------------------------------------------------------
     public void testCompile() throws Exception {
         Provider provider = compileAndLoad(AMERICA_LOS_ANGELES_FILE);
         DateTimeZone tz = provider.getZone("America/Los_Angeles");
