diff --git a/src/test/java/org/joda/time/format/TestDateTimeFormatter.java b/src/test/java/org/joda/time/format/TestDateTimeFormatter.java
index 5326370..ca3c866 100644
--- a/src/test/java/org/joda/time/format/TestDateTimeFormatter.java
+++ b/src/test/java/org/joda/time/format/TestDateTimeFormatter.java
@@ -1,5 +1,5 @@
 /*
- *  Copyright 2001-2011 <PERSON>
+ *  Copyright 2001-2009 <PERSON>
  *
  *  Licensed under the Apache License, Version 2.0 (the "License");
  *  you may not use this file except in compliance with the License.
@@ -422,74 +422,6 @@ public class TestDateTimeFormatter extends TestCase {
         assertEquals(new LocalDate(2012, 2, 29, chrono), f.parseLocalDate("2 29"));
     }
 
-    public void testParseLocalDate_weekyear_month_week_2010() {
-        Chronology chrono = GJChronology.getInstanceUTC();
-        DateTimeFormatter f = DateTimeFormat.forPattern("xxxx-MM-ww").withChronology(chrono);
-        assertEquals(new LocalDate(2010, 1, 4, chrono), f.parseLocalDate("2010-01-01"));
-    }
-
-    public void testParseLocalDate_weekyear_month_week_2011() {
-        Chronology chrono = GJChronology.getInstanceUTC();
-        DateTimeFormatter f = DateTimeFormat.forPattern("xxxx-MM-ww").withChronology(chrono);
-        assertEquals(new LocalDate(2011, 1, 3, chrono), f.parseLocalDate("2011-01-01"));
-    }
-
-    public void testParseLocalDate_weekyear_month_week_2012() {
-        Chronology chrono = GJChronology.getInstanceUTC();
-        DateTimeFormatter f = DateTimeFormat.forPattern("xxxx-MM-ww").withChronology(chrono);
-        assertEquals(new LocalDate(2012, 1, 2, chrono), f.parseLocalDate("2012-01-01"));
-    }
-
-// This test fails, but since more related tests pass with the extra loop in DateTimeParserBucket
-// I'm going to leave the change in and ignore this test
-//    public void testParseLocalDate_weekyear_month_week_2013() {
-//        Chronology chrono = GJChronology.getInstanceUTC();
-//        DateTimeFormatter f = DateTimeFormat.forPattern("xxxx-MM-ww").withChronology(chrono);
-//        assertEquals(new LocalDate(2012, 12, 31, chrono), f.parseLocalDate("2013-01-01"));
-//    }
-
-    public void testParseLocalDate_year_month_week_2010() {
-        Chronology chrono = GJChronology.getInstanceUTC();
-        DateTimeFormatter f = DateTimeFormat.forPattern("yyyy-MM-ww").withChronology(chrono);
-        assertEquals(new LocalDate(2010, 1, 4, chrono), f.parseLocalDate("2010-01-01"));
-    }
-
-    public void testParseLocalDate_year_month_week_2011() {
-        Chronology chrono = GJChronology.getInstanceUTC();
-        DateTimeFormatter f = DateTimeFormat.forPattern("yyyy-MM-ww").withChronology(chrono);
-        assertEquals(new LocalDate(2011, 1, 3, chrono), f.parseLocalDate("2011-01-01"));
-    }
-
-    public void testParseLocalDate_year_month_week_2012() {
-        Chronology chrono = GJChronology.getInstanceUTC();
-        DateTimeFormatter f = DateTimeFormat.forPattern("yyyy-MM-ww").withChronology(chrono);
-        assertEquals(new LocalDate(2012, 1, 2, chrono), f.parseLocalDate("2012-01-01"));
-    }
-
-    public void testParseLocalDate_year_month_week_2013() {
-        Chronology chrono = GJChronology.getInstanceUTC();
-        DateTimeFormatter f = DateTimeFormat.forPattern("yyyy-MM-ww").withChronology(chrono);
-        assertEquals(new LocalDate(2012, 12, 31, chrono), f.parseLocalDate("2013-01-01"));  // 2013-01-01 would be better, but this is OK
-    }
-
-    public void testParseLocalDate_year_month_week_2014() {
-        Chronology chrono = GJChronology.getInstanceUTC();
-        DateTimeFormatter f = DateTimeFormat.forPattern("yyyy-MM-ww").withChronology(chrono);
-        assertEquals(new LocalDate(2013, 12, 30, chrono), f.parseLocalDate("2014-01-01"));  // 2014-01-01 would be better, but this is OK
-    }
-
-    public void testParseLocalDate_year_month_week_2015() {
-        Chronology chrono = GJChronology.getInstanceUTC();
-        DateTimeFormatter f = DateTimeFormat.forPattern("yyyy-MM-ww").withChronology(chrono);
-        assertEquals(new LocalDate(2014, 12, 29, chrono), f.parseLocalDate("2015-01-01"));  // 2015-01-01 would be better, but this is OK
-    }
-
-    public void testParseLocalDate_year_month_week_2016() {
-        Chronology chrono = GJChronology.getInstanceUTC();
-        DateTimeFormatter f = DateTimeFormat.forPattern("yyyy-MM-ww").withChronology(chrono);
-        assertEquals(new LocalDate(2016, 1, 4, chrono), f.parseLocalDate("2016-01-01"));
-    }
-
     //-----------------------------------------------------------------------
     public void testParseLocalTime_simple() {
         assertEquals(new LocalTime(10, 20, 30), g.parseLocalTime("2004-06-09T10:20:30Z"));
