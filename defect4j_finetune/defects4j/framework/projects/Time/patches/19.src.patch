diff --git a/src/main/java/org/joda/time/DateTimeZone.java b/src/main/java/org/joda/time/DateTimeZone.java
index ec05941..855cad0 100644
--- a/src/main/java/org/joda/time/DateTimeZone.java
+++ b/src/main/java/org/joda/time/DateTimeZone.java
@@ -897,7 +897,7 @@ public abstract class DateTimeZone implements Serializable {
                     return offsetLocal;
                 }
             }
-        } else if (offsetLocal >= 0) {
+        } else if (offsetLocal > 0) {
             long prev = previousTransition(instantAdjusted);
             if (prev < instantAdjusted) {
                 int offsetPrev = getOffset(prev);
