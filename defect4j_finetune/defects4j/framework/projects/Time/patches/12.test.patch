diff --git a/src/test/java/org/joda/time/TestLocalDateTime_Constructors.java b/src/test/java/org/joda/time/TestLocalDateTime_Constructors.java
index e00ce5d..1dbb381 100644
--- a/src/test/java/org/joda/time/TestLocalDateTime_Constructors.java
+++ b/src/test/java/org/joda/time/TestLocalDateTime_Constructors.java
@@ -102,30 +102,11 @@ public class TestLocalDateTime_Constructors extends TestCase {
     }
 
     //-----------------------------------------------------------------------
-    public void testFactory_fromCalendarFields() throws Exception {
+    public void testFactory_FromCalendarFields() throws Exception {
         GregorianCalendar cal = new GregorianCalendar(1970, 1, 3, 4, 5, 6);
         cal.set(Calendar.MILLISECOND, 7);
         LocalDateTime expected = new LocalDateTime(1970, 2, 3, 4, 5, 6, 7);
         assertEquals(expected, LocalDateTime.fromCalendarFields(cal));
-    }
-
-    public void testFactory_fromCalendarFields_beforeYearZero1() throws Exception {
-        GregorianCalendar cal = new GregorianCalendar(1, 1, 3, 4, 5, 6);
-        cal.set(Calendar.ERA, GregorianCalendar.BC);
-        cal.set(Calendar.MILLISECOND, 7);
-        LocalDateTime expected = new LocalDateTime(0, 2, 3, 4, 5, 6, 7);
-        assertEquals(expected, LocalDateTime.fromCalendarFields(cal));
-    }
-
-    public void testFactory_fromCalendarFields_beforeYearZero3() throws Exception {
-        GregorianCalendar cal = new GregorianCalendar(3, 1, 3, 4, 5, 6);
-        cal.set(Calendar.ERA, GregorianCalendar.BC);
-        cal.set(Calendar.MILLISECOND, 7);
-        LocalDateTime expected = new LocalDateTime(-2, 2, 3, 4, 5, 6, 7);
-        assertEquals(expected, LocalDateTime.fromCalendarFields(cal));
-    }
-
-    public void testFactory_fromCalendarFields_null() throws Exception {
         try {
             LocalDateTime.fromCalendarFields((Calendar) null);
             fail();
@@ -133,37 +114,22 @@ public class TestLocalDateTime_Constructors extends TestCase {
     }
 
     //-----------------------------------------------------------------------
-    public void testFactory_fromDateFields_after1970() throws Exception {
+    public void testFactory_FromDateFields_after1970() throws Exception {
         GregorianCalendar cal = new GregorianCalendar(1970, 1, 3, 4, 5, 6);
         cal.set(Calendar.MILLISECOND, 7);
         LocalDateTime expected = new LocalDateTime(1970, 2, 3, 4, 5 ,6, 7);
         assertEquals(expected, LocalDateTime.fromDateFields(cal.getTime()));
+        try {
+            LocalDateTime.fromDateFields((Date) null);
+            fail();
+        } catch (IllegalArgumentException ex) {}
     }
 
-    public void testFactory_fromDateFields_before1970() throws Exception {
+    public void testFactory_FromDateFields_before1970() throws Exception {
         GregorianCalendar cal = new GregorianCalendar(1969, 1, 3, 4, 5, 6);
         cal.set(Calendar.MILLISECOND, 7);
         LocalDateTime expected = new LocalDateTime(1969, 2, 3, 4, 5 ,6, 7);
         assertEquals(expected, LocalDateTime.fromDateFields(cal.getTime()));
-    }
-
-    public void testFactory_fromDateFields_beforeYearZero1() throws Exception {
-        GregorianCalendar cal = new GregorianCalendar(1, 1, 3, 4, 5, 6);
-        cal.set(Calendar.ERA, GregorianCalendar.BC);
-        cal.set(Calendar.MILLISECOND, 7);
-        LocalDateTime expected = new LocalDateTime(0, 2, 3, 4, 5, 6, 7);
-        assertEquals(expected, LocalDateTime.fromDateFields(cal.getTime()));
-    }
-
-    public void testFactory_fromDateFields_beforeYearZero3() throws Exception {
-        GregorianCalendar cal = new GregorianCalendar(3, 1, 3, 4, 5, 6);
-        cal.set(Calendar.ERA, GregorianCalendar.BC);
-        cal.set(Calendar.MILLISECOND, 7);
-        LocalDateTime expected = new LocalDateTime(-2, 2, 3, 4, 5, 6, 7);
-        assertEquals(expected, LocalDateTime.fromDateFields(cal.getTime()));
-    }
-
-    public void testFactory_fromDateFields_null() throws Exception {
         try {
             LocalDateTime.fromDateFields((Date) null);
             fail();
diff --git a/src/test/java/org/joda/time/TestLocalDate_Constructors.java b/src/test/java/org/joda/time/TestLocalDate_Constructors.java
index ee5fa9d..acd703c 100644
--- a/src/test/java/org/joda/time/TestLocalDate_Constructors.java
+++ b/src/test/java/org/joda/time/TestLocalDate_Constructors.java
@@ -94,30 +94,11 @@ public class TestLocalDate_Constructors extends TestCase {
     }
 
     //-----------------------------------------------------------------------
-    public void testFactory_fromCalendarFields() throws Exception {
+    public void testFactory_FromCalendarFields() throws Exception {
         GregorianCalendar cal = new GregorianCalendar(1970, 1, 3, 4, 5, 6);
         cal.set(Calendar.MILLISECOND, 7);
         LocalDate expected = new LocalDate(1970, 2, 3);
         assertEquals(expected, LocalDate.fromCalendarFields(cal));
-    }
-
-    public void testFactory_fromCalendarFields_beforeYearZero1() throws Exception {
-        GregorianCalendar cal = new GregorianCalendar(1, 1, 3, 4, 5, 6);
-        cal.set(Calendar.ERA, GregorianCalendar.BC);
-        cal.set(Calendar.MILLISECOND, 7);
-        LocalDate expected = new LocalDate(0, 2, 3);
-        assertEquals(expected, LocalDate.fromCalendarFields(cal));
-    }
-
-    public void testFactory_fromCalendarFields_beforeYearZero3() throws Exception {
-        GregorianCalendar cal = new GregorianCalendar(3, 1, 3, 4, 5, 6);
-        cal.set(Calendar.ERA, GregorianCalendar.BC);
-        cal.set(Calendar.MILLISECOND, 7);
-        LocalDate expected = new LocalDate(-2, 2, 3);
-        assertEquals(expected, LocalDate.fromCalendarFields(cal));
-    }
-
-    public void testFactory_fromCalendarFields_null() throws Exception {
         try {
             LocalDate.fromCalendarFields((Calendar) null);
             fail();
@@ -125,37 +106,11 @@ public class TestLocalDate_Constructors extends TestCase {
     }
 
     //-----------------------------------------------------------------------
-    public void testFactory_fromDateFields_after1970() throws Exception {
+    public void testFactory_FromDateFields() throws Exception {
         GregorianCalendar cal = new GregorianCalendar(1970, 1, 3, 4, 5, 6);
         cal.set(Calendar.MILLISECOND, 7);
         LocalDate expected = new LocalDate(1970, 2, 3);
         assertEquals(expected, LocalDate.fromDateFields(cal.getTime()));
-    }
-
-    public void testFactory_fromDateFields_before1970() throws Exception {
-        GregorianCalendar cal = new GregorianCalendar(1969, 1, 3, 4, 5, 6);
-        cal.set(Calendar.MILLISECOND, 7);
-        LocalDate expected = new LocalDate(1969, 2, 3);
-        assertEquals(expected, LocalDate.fromDateFields(cal.getTime()));
-    }
-
-    public void testFactory_fromDateFields_beforeYearZero1() throws Exception {
-        GregorianCalendar cal = new GregorianCalendar(1, 1, 3, 4, 5, 6);
-        cal.set(Calendar.ERA, GregorianCalendar.BC);
-        cal.set(Calendar.MILLISECOND, 7);
-        LocalDate expected = new LocalDate(0, 2, 3);
-        assertEquals(expected, LocalDate.fromDateFields(cal.getTime()));
-    }
-
-    public void testFactory_fromDateFields_beforeYearZero3() throws Exception {
-        GregorianCalendar cal = new GregorianCalendar(3, 1, 3, 4, 5, 6);
-        cal.set(Calendar.ERA, GregorianCalendar.BC);
-        cal.set(Calendar.MILLISECOND, 7);
-        LocalDate expected = new LocalDate(-2, 2, 3);
-        assertEquals(expected, LocalDate.fromDateFields(cal.getTime()));
-    }
-
-    public void testFactory_fromDateFields_null() throws Exception {
         try {
             LocalDate.fromDateFields((Date) null);
             fail();
@@ -428,7 +383,6 @@ public class TestLocalDate_Constructors extends TestCase {
         assertEquals(6, test.getDayOfMonth());
     }
 
-    @SuppressWarnings("deprecation")
     public void testConstructor_ObjectYearMonthDay() throws Throwable {
         YearMonthDay date = new YearMonthDay(1970, 4, 6, BUDDHIST_UTC);
         LocalDate test = new LocalDate(date);
