diff --git a/src/main/java/org/joda/time/format/PeriodFormatterBuilder.java b/src/main/java/org/joda/time/format/PeriodFormatterBuilder.java
index 29b0872..6c8e9c6 100644
--- a/src/main/java/org/joda/time/format/PeriodFormatterBuilder.java
+++ b/src/main/java/org/joda/time/format/PeriodFormatterBuilder.java
@@ -1095,7 +1095,7 @@ public class PeriodFormatterBuilder {
             if (iFieldType >= SECONDS_MILLIS) {
                 // valueLong contains the seconds and millis fields
                 // the minimum output is 0.000, which is 4 or 5 digits with a negative
-                sum = (valueLong < 0 ? Math.max(sum, 5) : Math.max(sum, 4));
+                sum = Math.max(sum, 4);
                 // plus one for the decimal point
                 sum++;
                 if (iFieldType == SECONDS_OPTIONAL_MILLIS &&
@@ -1130,7 +1130,6 @@ public class PeriodFormatterBuilder {
             if (iPrefix != null) {
                 iPrefix.printTo(buf, value);
             }
-            int bufLen = buf.length();
             int minDigits = iMinPrintedDigits;
             if (minDigits <= 1) {
                 FormatUtils.appendUnpaddedInteger(buf, value);
@@ -1140,9 +1139,6 @@ public class PeriodFormatterBuilder {
             if (iFieldType >= SECONDS_MILLIS) {
                 int dp = (int) (Math.abs(valueLong) % DateTimeConstants.MILLIS_PER_SECOND);
                 if (iFieldType == SECONDS_MILLIS || dp > 0) {
-                    if (valueLong < 0 && valueLong > -DateTimeConstants.MILLIS_PER_SECOND) {
-                        buf.insert(bufLen, '-');
-                    }
                     buf.append('.');
                     FormatUtils.appendPaddedInteger(buf, dp, 3);
                 }
