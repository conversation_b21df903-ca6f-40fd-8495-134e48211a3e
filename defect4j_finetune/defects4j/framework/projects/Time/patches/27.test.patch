diff --git a/src/test/java/org/joda/time/format/TestPeriodFormatterBuilder.java b/src/test/java/org/joda/time/format/TestPeriodFormatterBuilder.java
index 6684366..53d31b0 100644
--- a/src/test/java/org/joda/time/format/TestPeriodFormatterBuilder.java
+++ b/src/test/java/org/joda/time/format/TestPeriodFormatterBuilder.java
@@ -843,30 +843,4 @@ public class TestPeriodFormatterBuilder extends TestCase {
         assertNotNull(bld.toParser());
     }
 
-    public void testBug2495455() {
-        PeriodFormatter pfmt1 = new PeriodFormatterBuilder()
-            .appendLiteral("P")
-            .appendYears()
-            .appendSuffix("Y")
-            .appendMonths()
-            .appendSuffix("M")
-            .appendWeeks()
-            .appendSuffix("W")
-            .appendDays()
-            .appendSuffix("D")
-            .appendSeparatorIfFieldsAfter("T")
-            .appendHours()
-            .appendSuffix("H")
-            .appendMinutes()
-            .appendSuffix("M")
-            .appendSecondsWithOptionalMillis()
-            .appendSuffix("S")
-            .toFormatter();
-        PeriodFormatter pfmt2 = new PeriodFormatterBuilder()
-            .append(ISOPeriodFormat.standard())
-            .toFormatter();
-        pfmt1.parsePeriod("PT1003199059S");
-        pfmt2.parsePeriod("PT1003199059S");
-    }
-
 }
