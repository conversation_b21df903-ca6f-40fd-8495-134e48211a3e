diff --git a/src/test/java/org/joda/time/format/TestDateTimeFormatter.java b/src/test/java/org/joda/time/format/TestDateTimeFormatter.java
index 5d104df..0152ce7 100644
--- a/src/test/java/org/joda/time/format/TestDateTimeFormatter.java
+++ b/src/test/java/org/joda/time/format/TestDateTimeFormatter.java
@@ -905,62 +905,6 @@ public class TestDateTimeFormatter extends TestCase {
         assertEquals(new MutableDateTime(2004, 2, 29, 12, 20, 30, 0, LONDON), result);
     }
 
-    public void testParseInto_monthDay_feb29_startOfYear() {
-        DateTimeFormatter f = DateTimeFormat.forPattern("M d").withLocale(Locale.UK);
-        MutableDateTime result = new MutableDateTime(2004, 1, 1, 0, 0, 0, 0, LONDON);
-        assertEquals(4, f.parseInto(result, "2 29", 0));
-        assertEquals(new MutableDateTime(2004, 2, 29, 0, 0, 0, 0, LONDON), result);
-    }
-
-    public void testParseInto_monthDay_feb29_OfYear() {
-        DateTimeFormatter f = DateTimeFormat.forPattern("M d").withLocale(Locale.UK);
-        MutableDateTime result = new MutableDateTime(2004, 12, 31, 23, 59, 59, 999, LONDON);
-        assertEquals(4, f.parseInto(result, "2 29", 0));
-        assertEquals(new MutableDateTime(2004, 2, 29, 23, 59, 59, 999, LONDON), result);
-    }
-
-    public void testParseInto_monthDay_feb29_newYork() {
-        DateTimeFormatter f = DateTimeFormat.forPattern("M d").withLocale(Locale.UK);
-        MutableDateTime result = new MutableDateTime(2004, 1, 9, 12, 20, 30, 0, NEWYORK);
-        assertEquals(4, f.parseInto(result, "2 29", 0));
-        assertEquals(new MutableDateTime(2004, 2, 29, 12, 20, 30, 0, NEWYORK), result);
-    }
-
-    public void testParseInto_monthDay_feb29_newYork_startOfYear() {
-        DateTimeFormatter f = DateTimeFormat.forPattern("M d").withLocale(Locale.UK);
-        MutableDateTime result = new MutableDateTime(2004, 1, 1, 0, 0, 0, 0, NEWYORK);
-        assertEquals(4, f.parseInto(result, "2 29", 0));
-        assertEquals(new MutableDateTime(2004, 2, 29, 0, 0, 0, 0, NEWYORK), result);
-    }
-
-    public void testParseInto_monthDay_feb29_newYork_endOfYear() {
-        DateTimeFormatter f = DateTimeFormat.forPattern("M d").withLocale(Locale.UK);
-        MutableDateTime result = new MutableDateTime(2004, 12, 31, 23, 59, 59, 999, NEWYORK);
-        assertEquals(4, f.parseInto(result, "2 29", 0));
-        assertEquals(new MutableDateTime(2004, 2, 29, 23, 59, 59, 999, NEWYORK), result);
-    }
-
-    public void testParseInto_monthDay_feb29_tokyo() {
-        DateTimeFormatter f = DateTimeFormat.forPattern("M d").withLocale(Locale.UK);
-        MutableDateTime result = new MutableDateTime(2004, 1, 9, 12, 20, 30, 0, TOKYO);
-        assertEquals(4, f.parseInto(result, "2 29", 0));
-        assertEquals(new MutableDateTime(2004, 2, 29, 12, 20, 30, 0, TOKYO), result);
-    }
-
-    public void testParseInto_monthDay_feb29_tokyo_startOfYear() {
-        DateTimeFormatter f = DateTimeFormat.forPattern("M d").withLocale(Locale.UK);
-        MutableDateTime result = new MutableDateTime(2004, 1, 1, 0, 0, 0, 0, TOKYO);
-        assertEquals(4, f.parseInto(result, "2 29", 0));
-        assertEquals(new MutableDateTime(2004, 2, 29, 0, 0, 0, 0, TOKYO), result);
-    }
-
-    public void testParseInto_monthDay_feb29_tokyo_endOfYear() {
-        DateTimeFormatter f = DateTimeFormat.forPattern("M d").withLocale(Locale.UK);
-        MutableDateTime result = new MutableDateTime(2004, 12, 31, 23, 59, 59, 999, TOKYO);
-        assertEquals(4, f.parseInto(result, "2 29", 0));
-        assertEquals(new MutableDateTime(2004, 2, 29, 23, 59, 59, 999, TOKYO), result);
-    }
-
     public void testParseInto_monthDay_withDefaultYear_feb29() {
         DateTimeFormatter f = DateTimeFormat.forPattern("M d").withDefaultYear(2012);
         MutableDateTime result = new MutableDateTime(2004, 1, 9, 12, 20, 30, 0, LONDON);
@@ -968,20 +912,6 @@ public class TestDateTimeFormatter extends TestCase {
         assertEquals(new MutableDateTime(2004, 2, 29, 12, 20, 30, 0, LONDON), result);
     }
 
-    public void testParseInto_monthDay_withDefaultYear_feb29_newYork() {
-        DateTimeFormatter f = DateTimeFormat.forPattern("M d").withDefaultYear(2012);
-        MutableDateTime result = new MutableDateTime(2004, 1, 9, 12, 20, 30, 0, NEWYORK);
-        assertEquals(4, f.parseInto(result, "2 29", 0));
-        assertEquals(new MutableDateTime(2004, 2, 29, 12, 20, 30, 0, NEWYORK), result);
-    }
-
-    public void testParseInto_monthDay_withDefaultYear_feb29_newYork_endOfYear() {
-        DateTimeFormatter f = DateTimeFormat.forPattern("M d").withDefaultYear(2012);
-        MutableDateTime result = new MutableDateTime(2004, 12, 9, 12, 20, 30, 0, NEWYORK);
-        assertEquals(4, f.parseInto(result, "2 29", 0));
-        assertEquals(new MutableDateTime(2004, 2, 29, 12, 20, 30, 0, NEWYORK), result);
-    }
-
     public void testParseMillis_fractionOfSecondLong() {
         DateTimeFormatter f = new DateTimeFormatterBuilder()
             .appendSecondOfDay(2).appendLiteral('.').appendFractionOfSecond(1, 9)
