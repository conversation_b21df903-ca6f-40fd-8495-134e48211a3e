diff --git a/src/test/java/org/joda/time/format/TestDateTimeFormatterBuilder.java b/src/test/java/org/joda/time/format/TestDateTimeFormatterBuilder.java
index 54033bb..31aa53c 100644
--- a/src/test/java/org/joda/time/format/TestDateTimeFormatterBuilder.java
+++ b/src/test/java/org/joda/time/format/TestDateTimeFormatterBuilder.java
@@ -252,16 +252,6 @@ public class TestDateTimeFormatterBuilder extends TestCase {
         assertEquals(dt, f.withOffsetParsed().parseDateTime("2007-03-04 12:30 Europe/Paris"));
     }
 
-    public void test_printParseZoneDawsonCreek() {  // clashes with shorter Dawson
-        DateTimeFormatterBuilder bld = new DateTimeFormatterBuilder()
-            .appendPattern("yyyy-MM-dd HH:mm ").appendTimeZoneId();
-        DateTimeFormatter f = bld.toFormatter();
-        
-        DateTime dt = new DateTime(2007, 3, 4, 12, 30, 0, DateTimeZone.forID("America/Dawson_Creek"));
-        assertEquals("2007-03-04 12:30 America/Dawson_Creek", f.print(dt));
-        assertEquals(dt, f.parseDateTime("2007-03-04 12:30 America/Dawson_Creek"));
-    }
-
     public void test_printParseOffset() {
         DateTimeFormatterBuilder bld = new DateTimeFormatterBuilder()
             .appendPattern("yyyy-MM-dd HH:mm ").appendTimeZoneOffset("Z", true, 2, 2);
