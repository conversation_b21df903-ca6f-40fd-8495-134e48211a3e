diff --git a/src/test/java/org/joda/time/field/TestFieldUtils.java b/src/test/java/org/joda/time/field/TestFieldUtils.java
index e60d192..2933d41 100644
--- a/src/test/java/org/joda/time/field/TestFieldUtils.java
+++ b/src/test/java/org/joda/time/field/TestFieldUtils.java
@@ -191,94 +191,50 @@ public class TestFieldUtils extends TestCase {
 
     //-----------------------------------------------------------------------
     public void testSafeMultiplyLongLong() {
-        assertEquals(0L, FieldUtils.safeMultiply(0L, 0L));
-        
-        assertEquals(1L, FieldUtils.safeMultiply(1L, 1L));
-        assertEquals(3L, FieldUtils.safeMultiply(1L, 3L));
-        assertEquals(3L, FieldUtils.safeMultiply(3L, 1L));
-        
-        assertEquals(6L, FieldUtils.safeMultiply(2L, 3L));
-        assertEquals(-6L, FieldUtils.safeMultiply(2L, -3L));
-        assertEquals(-6L, FieldUtils.safeMultiply(-2L, 3L));
-        assertEquals(6L, FieldUtils.safeMultiply(-2L, -3L));
-        
-        assertEquals(Long.MAX_VALUE, FieldUtils.safeMultiply(Long.MAX_VALUE, 1L));
-        assertEquals(Long.MIN_VALUE, FieldUtils.safeMultiply(Long.MIN_VALUE, 1L));
-        assertEquals(-Long.MAX_VALUE, FieldUtils.safeMultiply(Long.MAX_VALUE, -1L));
-        
-        try {
-            FieldUtils.safeMultiply(Long.MIN_VALUE, -1L);
-            fail();
-        } catch (ArithmeticException e) {
-        }
-        
-        try {
-            FieldUtils.safeMultiply(-1L, Long.MIN_VALUE);
-            fail();
-        } catch (ArithmeticException e) {
-        }
+      assertEquals(0L, FieldUtils.safeMultiply(0L, 0L));
       
-        try {
-            FieldUtils.safeMultiply(Long.MIN_VALUE, 100L);
-            fail();
-        } catch (ArithmeticException e) {
-        }
-        
-        try {
-            FieldUtils.safeMultiply(Long.MIN_VALUE, Long.MAX_VALUE);
-            fail();
-        } catch (ArithmeticException e) {
-        }
-        
-        try {
-            FieldUtils.safeMultiply(Long.MAX_VALUE, Long.MIN_VALUE);
-            fail();
-        } catch (ArithmeticException e) {
-        }
-    }
-
-    //-----------------------------------------------------------------------
-    public void testSafeMultiplyLongInt() {
-        assertEquals(0L, FieldUtils.safeMultiply(0L, 0));
-        
-        assertEquals(1L, FieldUtils.safeMultiply(1L, 1));
-        assertEquals(3L, FieldUtils.safeMultiply(1L, 3));
-        assertEquals(3L, FieldUtils.safeMultiply(3L, 1));
-        
-        assertEquals(6L, FieldUtils.safeMultiply(2L, 3));
-        assertEquals(-6L, FieldUtils.safeMultiply(2L, -3));
-        assertEquals(-6L, FieldUtils.safeMultiply(-2L, 3));
-        assertEquals(6L, FieldUtils.safeMultiply(-2L, -3));
-        
-        assertEquals(-1L * Integer.MIN_VALUE, FieldUtils.safeMultiply(-1L, Integer.MIN_VALUE));
-        
-        assertEquals(Long.MAX_VALUE, FieldUtils.safeMultiply(Long.MAX_VALUE, 1));
-        assertEquals(Long.MIN_VALUE, FieldUtils.safeMultiply(Long.MIN_VALUE, 1));
-        assertEquals(-Long.MAX_VALUE, FieldUtils.safeMultiply(Long.MAX_VALUE, -1));
-        
-        try {
-            FieldUtils.safeMultiply(Long.MIN_VALUE, -1);
-            fail();
-        } catch (ArithmeticException e) {
-        }
-        
-        try {
-            FieldUtils.safeMultiply(Long.MIN_VALUE, 100);
-            fail();
-        } catch (ArithmeticException e) {
-        }
-        
-        try {
-            FieldUtils.safeMultiply(Long.MIN_VALUE, Integer.MAX_VALUE);
-            fail();
-        } catch (ArithmeticException e) {
-        }
-        
-        try {
-            FieldUtils.safeMultiply(Long.MAX_VALUE, Integer.MIN_VALUE);
-            fail();
-        } catch (ArithmeticException e) {
-        }
-    }
+      assertEquals(1L, FieldUtils.safeMultiply(1L, 1L));
+      assertEquals(3L, FieldUtils.safeMultiply(1L, 3L));
+      assertEquals(3L, FieldUtils.safeMultiply(3L, 1L));
+      
+      assertEquals(6L, FieldUtils.safeMultiply(2L, 3L));
+      assertEquals(-6L, FieldUtils.safeMultiply(2L, -3L));
+      assertEquals(-6L, FieldUtils.safeMultiply(-2L, 3L));
+      assertEquals(6L, FieldUtils.safeMultiply(-2L, -3L));
+      
+      assertEquals(Long.MAX_VALUE, FieldUtils.safeMultiply(Long.MAX_VALUE, 1L));
+      assertEquals(Long.MIN_VALUE, FieldUtils.safeMultiply(Long.MIN_VALUE, 1L));
+      assertEquals(-Long.MAX_VALUE, FieldUtils.safeMultiply(Long.MAX_VALUE, -1L));
+      
+      try {
+          FieldUtils.safeMultiply(Long.MIN_VALUE, -1L);
+          fail();
+      } catch (ArithmeticException e) {
+      }
+      
+      try {
+          FieldUtils.safeMultiply(-1L, Long.MIN_VALUE);
+          fail();
+      } catch (ArithmeticException e) {
+      }
+    
+      try {
+          FieldUtils.safeMultiply(Long.MIN_VALUE, 100L);
+          fail();
+      } catch (ArithmeticException e) {
+      }
+      
+      try {
+          FieldUtils.safeMultiply(Long.MIN_VALUE, Long.MAX_VALUE);
+          fail();
+      } catch (ArithmeticException e) {
+      }
+      
+      try {
+          FieldUtils.safeMultiply(Long.MAX_VALUE, Long.MIN_VALUE);
+          fail();
+      } catch (ArithmeticException e) {
+      }
+  }
 }
 
