diff --git a/src/main/java/org/joda/time/LocalDate.java b/src/main/java/org/joda/time/LocalDate.java
index c86b9d7..3868531 100644
--- a/src/main/java/org/joda/time/LocalDate.java
+++ b/src/main/java/org/joda/time/LocalDate.java
@@ -207,10 +207,9 @@ public final class LocalDate
         if (calendar == null) {
             throw new IllegalArgumentException("The calendar must not be null");
         }
-        int era = calendar.get(Calendar.ERA);
         int yearOfEra = calendar.get(Calendar.YEAR);
         return new LocalDate(
-            (era == GregorianCalendar.AD ? yearOfEra : 1 - yearOfEra),
+            yearOfEra,
             calendar.get(Calendar.MONTH) + 1,
             calendar.get(Calendar.DAY_OF_MONTH)
         );
@@ -241,12 +240,7 @@ public final class LocalDate
         if (date == null) {
             throw new IllegalArgumentException("The date must not be null");
         }
-        if (date.getTime() < 0) {
             // handle years in era BC
-            GregorianCalendar cal = new GregorianCalendar();
-            cal.setTime(date);
-            return fromCalendarFields(cal);
-        }
         return new LocalDate(
             date.getYear() + 1900,
             date.getMonth() + 1,
diff --git a/src/main/java/org/joda/time/LocalDateTime.java b/src/main/java/org/joda/time/LocalDateTime.java
index e75deca..f37db3e 100644
--- a/src/main/java/org/joda/time/LocalDateTime.java
+++ b/src/main/java/org/joda/time/LocalDateTime.java
@@ -196,10 +196,9 @@ public final class LocalDateTime
         if (calendar == null) {
             throw new IllegalArgumentException("The calendar must not be null");
         }
-        int era = calendar.get(Calendar.ERA);
         int yearOfEra = calendar.get(Calendar.YEAR);
         return new LocalDateTime(
-            (era == GregorianCalendar.AD ? yearOfEra : 1 - yearOfEra),
+            yearOfEra,
             calendar.get(Calendar.MONTH) + 1,
             calendar.get(Calendar.DAY_OF_MONTH),
             calendar.get(Calendar.HOUR_OF_DAY),
@@ -234,12 +233,7 @@ public final class LocalDateTime
         if (date == null) {
             throw new IllegalArgumentException("The date must not be null");
         }
-        if (date.getTime() < 0) {
             // handle years in era BC
-            GregorianCalendar cal = new GregorianCalendar();
-            cal.setTime(date);
-            return fromCalendarFields(cal);
-        }
         return new LocalDateTime(
             date.getYear() + 1900,
             date.getMonth() + 1,
