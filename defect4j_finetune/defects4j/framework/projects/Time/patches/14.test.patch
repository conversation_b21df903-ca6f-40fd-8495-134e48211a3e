diff --git a/src/test/java/org/joda/time/TestMonthDay_Basics.java b/src/test/java/org/joda/time/TestMonthDay_Basics.java
index 38f97a7..65f30b8 100644
--- a/src/test/java/org/joda/time/TestMonthDay_Basics.java
+++ b/src/test/java/org/joda/time/TestMonthDay_Basics.java
@@ -448,34 +448,6 @@ public class TestMonthDay_Basics extends TestCase {
         assertEquals(expected, result);
     }
 
-    public void testPlusMonths_int_fromLeap() {
-        MonthDay test = new MonthDay(2, 29, ISOChronology.getInstanceUTC());
-        MonthDay result = test.plusMonths(1);
-        MonthDay expected = new MonthDay(3, 29, ISOChronology.getInstance());
-        assertEquals(expected, result);
-    }
-
-    public void testPlusMonths_int_negativeFromLeap() {
-        MonthDay test = new MonthDay(2, 29, ISOChronology.getInstanceUTC());
-        MonthDay result = test.plusMonths(-1);
-        MonthDay expected = new MonthDay(1, 29, ISOChronology.getInstance());
-        assertEquals(expected, result);
-    }
-
-    public void testPlusMonths_int_endOfMonthAdjust() {
-        MonthDay test = new MonthDay(3, 31, ISOChronology.getInstanceUTC());
-        MonthDay result = test.plusMonths(1);
-        MonthDay expected = new MonthDay(4, 30, ISOChronology.getInstance());
-        assertEquals(expected, result);
-    }
-
-    public void testPlusMonths_int_negativeEndOfMonthAdjust() {
-        MonthDay test = new MonthDay(3, 31, ISOChronology.getInstanceUTC());
-        MonthDay result = test.plusMonths(-1);
-        MonthDay expected = new MonthDay(2, 29, ISOChronology.getInstance());
-        assertEquals(expected, result);
-    }
-
     public void testPlusMonths_int_same() {
         MonthDay test = new MonthDay(6, 5, ISO_UTC);
         MonthDay result = test.plusMonths(0);
@@ -496,7 +468,6 @@ public class TestMonthDay_Basics extends TestCase {
         assertEquals(expected, result);
     }
 
-    //-------------------------------------------------------------------------
     public void testPlusDays_int() {
         MonthDay test = new MonthDay(5, 10, BuddhistChronology.getInstance());
         MonthDay result = test.plusDays(1);
@@ -504,20 +475,6 @@ public class TestMonthDay_Basics extends TestCase {
         assertEquals(expected, result);
     }
 
-    public void testPlusDays_int_fromLeap() {
-        MonthDay test = new MonthDay(2, 29, ISOChronology.getInstanceUTC());
-        MonthDay result = test.plusDays(1);
-        MonthDay expected = new MonthDay(3, 1, ISOChronology.getInstance());
-        assertEquals(expected, result);
-    }
-
-    public void testPlusDays_int_negativeFromLeap() {
-        MonthDay test = new MonthDay(2, 29, ISOChronology.getInstanceUTC());
-        MonthDay result = test.plusDays(-1);
-        MonthDay expected = new MonthDay(2, 28, ISOChronology.getInstance());
-        assertEquals(expected, result);
-    }
-
     public void testPlusDays_same() {
         MonthDay test = new MonthDay(5, 10, BuddhistChronology.getInstance());
         MonthDay result = test.plusDays(0);
@@ -542,34 +499,6 @@ public class TestMonthDay_Basics extends TestCase {
         assertEquals(expected, result);
     }
 
-    public void testMinusMonths_int_fromLeap() {
-        MonthDay test = new MonthDay(2, 29, ISOChronology.getInstanceUTC());
-        MonthDay result = test.minusMonths(1);
-        MonthDay expected = new MonthDay(1, 29, ISOChronology.getInstance());
-        assertEquals(expected, result);
-    }
-
-    public void testMinusMonths_int_negativeFromLeap() {
-        MonthDay test = new MonthDay(2, 29, ISOChronology.getInstanceUTC());
-        MonthDay result = test.minusMonths(-1);
-        MonthDay expected = new MonthDay(3, 29, ISOChronology.getInstance());
-        assertEquals(expected, result);
-    }
-
-    public void testMinusMonths_int_endOfMonthAdjust() {
-        MonthDay test = new MonthDay(3, 31, ISOChronology.getInstanceUTC());
-        MonthDay result = test.minusMonths(1);
-        MonthDay expected = new MonthDay(2, 29, ISOChronology.getInstance());
-        assertEquals(expected, result);
-    }
-
-    public void testMinusMonths_int_negativeEndOfMonthAdjust() {
-        MonthDay test = new MonthDay(3, 31, ISOChronology.getInstanceUTC());
-        MonthDay result = test.minusMonths(-1);
-        MonthDay expected = new MonthDay(4, 30, ISOChronology.getInstance());
-        assertEquals(expected, result);
-    }
-
     public void testMinusMonths_int_same() {
         MonthDay test = new MonthDay(6, 5, ISO_UTC);
         MonthDay result = test.minusMonths(0);
@@ -590,7 +519,6 @@ public class TestMonthDay_Basics extends TestCase {
         assertEquals(expected, result);
     }
 
-    //-------------------------------------------------------------------------
     public void testMinusDays_int() {
         MonthDay test = new MonthDay(5, 11, BuddhistChronology.getInstance());
         MonthDay result = test.minusDays(1);
@@ -598,20 +526,6 @@ public class TestMonthDay_Basics extends TestCase {
         assertEquals(expected, result);
     }
 
-    public void testMinusDays_int_fromLeap() {
-        MonthDay test = new MonthDay(2, 29, ISOChronology.getInstanceUTC());
-        MonthDay result = test.minusDays(1);
-        MonthDay expected = new MonthDay(2, 28, ISOChronology.getInstance());
-        assertEquals(expected, result);
-    }
-
-    public void testMinusDays_int_negativeFromLeap() {
-        MonthDay test = new MonthDay(2, 29, ISOChronology.getInstanceUTC());
-        MonthDay result = test.minusDays(-1);
-        MonthDay expected = new MonthDay(3, 1, ISOChronology.getInstance());
-        assertEquals(expected, result);
-    }
-
     public void testMinusDays_same() {
         MonthDay test = new MonthDay(5, 11, BuddhistChronology.getInstance());
         MonthDay result = test.minusDays(0);
