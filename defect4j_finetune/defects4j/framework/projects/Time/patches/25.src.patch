diff --git a/src/main/java/org/joda/time/DateTimeZone.java b/src/main/java/org/joda/time/DateTimeZone.java
index 81f5872..a320022 100644
--- a/src/main/java/org/joda/time/DateTimeZone.java
+++ b/src/main/java/org/joda/time/DateTimeZone.java
@@ -896,15 +896,6 @@ public abstract class DateTimeZone implements Serializable {
                     return offsetLocal;
                 }
             }
-        } else if (offsetLocal > 0) {
-            long prev = previousTransition(instantAdjusted);
-            if (prev < instantAdjusted) {
-                int offsetPrev = getOffset(prev);
-                int diff = offsetPrev - offsetLocal;
-                if (instantAdjusted - prev <= diff) {
-                    return offsetPrev;
-                }
-            }
         }
         return offsetAdjusted;
     }
