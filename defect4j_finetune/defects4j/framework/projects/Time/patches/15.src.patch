diff --git a/src/main/java/org/joda/time/field/FieldUtils.java b/src/main/java/org/joda/time/field/FieldUtils.java
index 1e045b2..a0fe7e2 100644
--- a/src/main/java/org/joda/time/field/FieldUtils.java
+++ b/src/main/java/org/joda/time/field/FieldUtils.java
@@ -135,9 +135,6 @@ public class FieldUtils {
     public static long safeMultiply(long val1, int val2) {
         switch (val2) {
             case -1:
-                if (val1 == Long.MIN_VALUE) {
-                    throw new ArithmeticException("Multiplication overflows a long: " + val1 + " * " + val2);
-                }
                 return -val1;
             case 0:
                 return 0L;
