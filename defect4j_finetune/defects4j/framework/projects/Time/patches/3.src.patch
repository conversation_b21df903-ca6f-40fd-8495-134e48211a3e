diff --git a/src/main/java/org/joda/time/MutableDateTime.java b/src/main/java/org/joda/time/MutableDateTime.java
index 9aa8602..310f55d 100644
--- a/src/main/java/org/joda/time/MutableDateTime.java
+++ b/src/main/java/org/joda/time/MutableDateTime.java
@@ -636,9 +636,7 @@ public class MutableDateTime
         if (type == null) {
             throw new IllegalArgumentException("Field must not be null");
         }
-        if (amount != 0) {
             setMillis(type.getField(getChronology()).add(getMillis(), amount));
-        }
     }
 
     //-----------------------------------------------------------------------
@@ -659,9 +657,7 @@ public class MutableDateTime
      * @throws IllegalArgumentException if the value is invalid
      */
     public void addYears(final int years) {
-        if (years != 0) {
             setMillis(getChronology().years().add(getMillis(), years));
-        }
     }
 
     //-----------------------------------------------------------------------
@@ -682,9 +678,7 @@ public class MutableDateTime
      * @throws IllegalArgumentException if the value is invalid
      */
     public void addWeekyears(final int weekyears) {
-        if (weekyears != 0) {
             setMillis(getChronology().weekyears().add(getMillis(), weekyears));
-        }
     }
 
     //-----------------------------------------------------------------------
@@ -705,9 +699,7 @@ public class MutableDateTime
      * @throws IllegalArgumentException if the value is invalid
      */
     public void addMonths(final int months) {
-        if (months != 0) {
             setMillis(getChronology().months().add(getMillis(), months));
-        }
     }
 
     //-----------------------------------------------------------------------
@@ -728,9 +720,7 @@ public class MutableDateTime
      * @throws IllegalArgumentException if the value is invalid
      */
     public void addWeeks(final int weeks) {
-        if (weeks != 0) {
             setMillis(getChronology().weeks().add(getMillis(), weeks));
-        }
     }
 
     //-----------------------------------------------------------------------
@@ -771,9 +761,7 @@ public class MutableDateTime
      * @throws IllegalArgumentException if the value is invalid
      */
     public void addDays(final int days) {
-        if (days != 0) {
             setMillis(getChronology().days().add(getMillis(), days));
-        }
     }
 
     //-----------------------------------------------------------------------
@@ -794,9 +782,7 @@ public class MutableDateTime
      * @throws IllegalArgumentException if the value is invalid
      */
     public void addHours(final int hours) {
-        if (hours != 0) {
             setMillis(getChronology().hours().add(getMillis(), hours));
-        }
     }
     
     //-----------------------------------------------------------------------
@@ -827,9 +813,7 @@ public class MutableDateTime
      * @throws IllegalArgumentException if the value is invalid
      */
     public void addMinutes(final int minutes) {
-        if (minutes != 0) {
             setMillis(getChronology().minutes().add(getMillis(), minutes));
-        }
     }
 
     //-----------------------------------------------------------------------
@@ -860,9 +844,7 @@ public class MutableDateTime
      * @throws IllegalArgumentException if the value is invalid
      */
     public void addSeconds(final int seconds) {
-        if (seconds != 0) {
             setMillis(getChronology().seconds().add(getMillis(), seconds));
-        }
     }
 
     //-----------------------------------------------------------------------
@@ -895,9 +877,7 @@ public class MutableDateTime
      * @throws IllegalArgumentException if the value is invalid
      */
     public void addMillis(final int millis) {
-        if (millis != 0) {
             setMillis(getChronology().millis().add(getMillis(), millis));
-        }
     }
 
     //-----------------------------------------------------------------------
