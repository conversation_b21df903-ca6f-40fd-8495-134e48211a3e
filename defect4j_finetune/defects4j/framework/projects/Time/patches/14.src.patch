diff --git a/src/main/java/org/joda/time/chrono/BasicMonthOfYearDateTimeField.java b/src/main/java/org/joda/time/chrono/BasicMonthOfYearDateTimeField.java
index 2892a2d..afca44e 100644
--- a/src/main/java/org/joda/time/chrono/BasicMonthOfYearDateTimeField.java
+++ b/src/main/java/org/joda/time/chrono/BasicMonthOfYearDateTimeField.java
@@ -206,12 +206,7 @@ class BasicMonthOfYearDateTimeField extends ImpreciseDateTimeField {
         if (valueToAdd == 0) {
             return values;
         }
-        if (partial.size() > 0 && partial.getFieldType(0).equals(DateTimeFieldType.monthOfYear()) && fieldIndex == 0) {
             // month is largest field and being added to, such as month-day
-            int curMonth0 = partial.getValue(0) - 1;
-            int newMonth = ((curMonth0 + (valueToAdd % 12) + 12) % 12) + 1;
-            return set(partial, 0, values, newMonth);
-        }
         if (DateTimeUtils.isContiguous(partial)) {
             long instant = 0L;
             for (int i = 0, isize = partial.size(); i < isize; i++) {
