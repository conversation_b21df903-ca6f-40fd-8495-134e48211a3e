diff --git a/src/main/java/org/joda/time/DateTimeZone.java b/src/main/java/org/joda/time/DateTimeZone.java
index 23f70a5c..9edb1888 100644
--- a/src/main/java/org/joda/time/DateTimeZone.java
+++ b/src/main/java/org/joda/time/DateTimeZone.java
@@ -933,15 +933,6 @@ public abstract class DateTimeZone implements Serializable {
      * @throws IllegalArgumentException if the zone has no equivalent local time
      * @since 2.0
      */
-    public long convertLocalToUTC(long instantLocal, boolean strict, long originalInstantUTC) {
-        int offsetOriginal = getOffset(originalInstantUTC);
-        long instantUTC = instantLocal - offsetOriginal;
-        int offsetLocalFromOriginal = getOffset(instantUTC);
-        if (offsetLocalFromOriginal == offsetOriginal) {
-            return instantUTC;
-        }
-        return convertLocalToUTC(instantLocal, strict);
-    }
 
     /**
      * Converts a local instant to a standard UTC instant with the same
diff --git a/src/main/java/org/joda/time/chrono/ZonedChronology.java b/src/main/java/org/joda/time/chrono/ZonedChronology.java
index 819f64e..f165f3d 100644
--- a/src/main/java/org/joda/time/chrono/ZonedChronology.java
+++ b/src/main/java/org/joda/time/chrono/ZonedChronology.java
@@ -433,7 +433,7 @@ public final class ZonedChronology extends AssembledChronology {
             } else {
                long localInstant = iZone.convertUTCToLocal(instant);
                localInstant = iField.add(localInstant, value);
-               return iZone.convertLocalToUTC(localInstant, false, instant);
+               return iZone.convertLocalToUTC(localInstant, false);
             }
         }
 
@@ -445,7 +445,7 @@ public final class ZonedChronology extends AssembledChronology {
             } else {
                long localInstant = iZone.convertUTCToLocal(instant);
                localInstant = iField.add(localInstant, value);
-               return iZone.convertLocalToUTC(localInstant, false, instant);
+               return iZone.convertLocalToUTC(localInstant, false);
             }
         }
 
@@ -457,14 +457,14 @@ public final class ZonedChronology extends AssembledChronology {
             } else {
                 long localInstant = iZone.convertUTCToLocal(instant);
                 localInstant = iField.addWrapField(localInstant, value);
-                return iZone.convertLocalToUTC(localInstant, false, instant);
+                return iZone.convertLocalToUTC(localInstant, false);
             }
         }
 
         public long set(long instant, int value) {
             long localInstant = iZone.convertUTCToLocal(instant);
             localInstant = iField.set(localInstant, value);
-            long result = iZone.convertLocalToUTC(localInstant, false, instant);
+            long result = iZone.convertLocalToUTC(localInstant, false);
             if (get(result) != value) {
                 throw new IllegalFieldValueException(iField.getType(), new Integer(value),
                     "Illegal instant due to time zone offset transition: " +
@@ -478,7 +478,7 @@ public final class ZonedChronology extends AssembledChronology {
             // cannot verify that new value stuck because set may be lenient
             long localInstant = iZone.convertUTCToLocal(instant);
             localInstant = iField.set(localInstant, text, locale);
-            return iZone.convertLocalToUTC(localInstant, false, instant);
+            return iZone.convertLocalToUTC(localInstant, false);
         }
 
         public int getDifference(long minuendInstant, long subtrahendInstant) {
@@ -525,7 +525,7 @@ public final class ZonedChronology extends AssembledChronology {
             } else {
                 long localInstant = iZone.convertUTCToLocal(instant);
                 localInstant = iField.roundFloor(localInstant);
-                return iZone.convertLocalToUTC(localInstant, false, instant);
+                return iZone.convertLocalToUTC(localInstant, false);
             }
         }
 
@@ -537,7 +537,7 @@ public final class ZonedChronology extends AssembledChronology {
             } else {
                 long localInstant = iZone.convertUTCToLocal(instant);
                 localInstant = iField.roundCeiling(localInstant);
-                return iZone.convertLocalToUTC(localInstant, false, instant);
+                return iZone.convertLocalToUTC(localInstant, false);
             }
         }
 
diff --git a/src/main/java/org/joda/time/field/LenientDateTimeField.java b/src/main/java/org/joda/time/field/LenientDateTimeField.java
index 6cf4c718..450a4eff 100644
--- a/src/main/java/org/joda/time/field/LenientDateTimeField.java
+++ b/src/main/java/org/joda/time/field/LenientDateTimeField.java
@@ -72,6 +72,6 @@ public class LenientDateTimeField extends DelegatedDateTimeField {
         long localInstant = iBase.getZone().convertUTCToLocal(instant);
         long difference = FieldUtils.safeSubtract(value, get(instant));
         localInstant = getType().getField(iBase.withUTC()).add(localInstant, difference);
-        return iBase.getZone().convertLocalToUTC(localInstant, false, instant);
+        return iBase.getZone().convertLocalToUTC(localInstant, false);
     }
 }
