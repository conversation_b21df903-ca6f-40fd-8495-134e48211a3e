diff --git a/src/test/java/org/joda/time/TestDateTimeZone.java b/src/test/java/org/joda/time/TestDateTimeZone.java
index cec86cd..3b4221e 100644
--- a/src/test/java/org/joda/time/TestDateTimeZone.java
+++ b/src/test/java/org/joda/time/TestDateTimeZone.java
@@ -303,22 +303,9 @@ public class TestDateTimeZone extends TestCase {
     public void testForOffsetHoursMinutes_int_int() {
         assertEquals(DateTimeZone.UTC, DateTimeZone.forOffsetHoursMinutes(0, 0));
         assertEquals(DateTimeZone.forID("+23:59"), DateTimeZone.forOffsetHoursMinutes(23, 59));
-        
-        assertEquals(DateTimeZone.forID("+02:15"), DateTimeZone.forOffsetHoursMinutes(2, 15));
-        assertEquals(DateTimeZone.forID("+02:00"), DateTimeZone.forOffsetHoursMinutes(2, 0));
-        try {
-            DateTimeZone.forOffsetHoursMinutes(2, -15);
-            fail();
-        } catch (IllegalArgumentException ex) {}
-        
-        assertEquals(DateTimeZone.forID("+00:15"), DateTimeZone.forOffsetHoursMinutes(0, 15));
-        assertEquals(DateTimeZone.forID("+00:00"), DateTimeZone.forOffsetHoursMinutes(0, 0));
-        assertEquals(DateTimeZone.forID("-00:15"), DateTimeZone.forOffsetHoursMinutes(0, -15));
-        
+        assertEquals(DateTimeZone.forID("+03:15"), DateTimeZone.forOffsetHoursMinutes(3, 15));
         assertEquals(DateTimeZone.forID("-02:00"), DateTimeZone.forOffsetHoursMinutes(-2, 0));
-        assertEquals(DateTimeZone.forID("-02:15"), DateTimeZone.forOffsetHoursMinutes(-2, -15));
-        assertEquals(DateTimeZone.forID("-02:15"), DateTimeZone.forOffsetHoursMinutes(-2, 15));
-        
+        assertEquals(DateTimeZone.forID("-02:30"), DateTimeZone.forOffsetHoursMinutes(-2, 30));
         assertEquals(DateTimeZone.forID("-23:59"), DateTimeZone.forOffsetHoursMinutes(-23, 59));
         try {
             DateTimeZone.forOffsetHoursMinutes(2, 60);
@@ -329,6 +316,14 @@ public class TestDateTimeZone extends TestCase {
             fail();
         } catch (IllegalArgumentException ex) {}
         try {
+            DateTimeZone.forOffsetHoursMinutes(2, -1);
+            fail();
+        } catch (IllegalArgumentException ex) {}
+        try {
+            DateTimeZone.forOffsetHoursMinutes(-2, -1);
+            fail();
+        } catch (IllegalArgumentException ex) {}
+        try {
             DateTimeZone.forOffsetHoursMinutes(24, 0);
             fail();
         } catch (IllegalArgumentException ex) {}
