diff --git a/src/test/java/org/joda/time/TestDateTimeZoneCutover.java b/src/test/java/org/joda/time/TestDateTimeZoneCutover.java
index d86b68e..a7a5175 100644
--- a/src/test/java/org/joda/time/TestDateTimeZoneCutover.java
+++ b/src/test/java/org/joda/time/TestDateTimeZoneCutover.java
@@ -1,5 +1,5 @@
 /*
- *  Copyright 2001-2012 <PERSON>
+ *  Copyright 2001-2007 <PERSON>
  *
  *  Licensed under the Apache License, Version 2.0 (the "License");
  *  you may not use this file except in compliance with the License.
@@ -1229,7 +1229,6 @@ public class TestDateTimeZoneCutover extends TestCase {
         assertEquals(baseAfter.toString(), testAfter.toString());
     }
 
-    //-------------------------------------------------------------------------
     public void testBug3192457_adjustOffset() {
         final DateTimeZone zone = DateTimeZone.forID("Europe/Paris");
         DateTime base = new DateTime(2007, 10, 28, 3, 15, zone);
@@ -1238,46 +1237,13 @@ public class TestDateTimeZoneCutover extends TestCase {
         
         assertSame(base, base.withEarlierOffsetAtOverlap());
         assertSame(base, base.withLaterOffsetAtOverlap());
-        
         assertSame(baseBefore, baseBefore.withEarlierOffsetAtOverlap());
-        assertEquals(baseAfter, baseBefore.withLaterOffsetAtOverlap());
-        
         assertSame(baseAfter, baseAfter.withLaterOffsetAtOverlap());
-        assertEquals(baseBefore, baseAfter.withEarlierOffsetAtOverlap());
-    }
-
-    public void testBug3476684_adjustOffset() {
-        final DateTimeZone zone = DateTimeZone.forID("America/Sao_Paulo");
-        DateTime base = new DateTime(2012, 2, 25, 22, 15, zone);
-        DateTime baseBefore = base.plusHours(1);  // 23:15 (first)
-        DateTime baseAfter = base.plusHours(2);  // 23:15 (second)
-        
-        assertSame(base, base.withEarlierOffsetAtOverlap());
-        assertSame(base, base.withLaterOffsetAtOverlap());
-        
-        assertSame(baseBefore, baseBefore.withEarlierOffsetAtOverlap());
-        assertEquals(baseAfter, baseBefore.withLaterOffsetAtOverlap());
         
-        assertSame(baseAfter, baseAfter.withLaterOffsetAtOverlap());
         assertEquals(baseBefore, baseAfter.withEarlierOffsetAtOverlap());
+        assertEquals(baseAfter, baseAfter.withLaterOffsetAtOverlap());
     }
 
-    public void testBug3476684_adjustOffset_springGap() {
-      final DateTimeZone zone = DateTimeZone.forID("America/Sao_Paulo");
-      DateTime base = new DateTime(2011, 10, 15, 22, 15, zone);
-      DateTime baseBefore = base.plusHours(1);  // 23:15
-      DateTime baseAfter = base.plusHours(2);  // 01:15
-      
-      assertSame(base, base.withEarlierOffsetAtOverlap());
-      assertSame(base, base.withLaterOffsetAtOverlap());
-      
-      assertSame(baseBefore, baseBefore.withEarlierOffsetAtOverlap());
-      assertEquals(baseBefore, baseBefore.withLaterOffsetAtOverlap());
-      
-      assertSame(baseAfter, baseAfter.withLaterOffsetAtOverlap());
-      assertEquals(baseAfter, baseAfter.withEarlierOffsetAtOverlap());
-  }
-
     // ensure Summer time picked
     //-----------------------------------------------------------------------
     public void testDateTimeCreation_athens() {
