diff --git a/src/main/java/org/joda/time/format/DateTimeFormatterBuilder.java b/src/main/java/org/joda/time/format/DateTimeFormatterBuilder.java
index ebb4b08..558a586 100644
--- a/src/main/java/org/joda/time/format/DateTimeFormatterBuilder.java
+++ b/src/main/java/org/joda/time/format/DateTimeFormatterBuilder.java
@@ -2539,18 +2539,12 @@ public class DateTimeFormatterBuilder {
 
         public int parseInto(DateTimeParserBucket bucket, String text, int position) {
             String str = text.substring(position);
-            String best = null;
             for (String id : ALL_IDS) {
                 if (str.startsWith(id)) {
-                	if (best == null || id.length() > best.length()) {
-                		best = id;
-                	}
+                    bucket.setZone(DateTimeZone.forID(id));
+                    return position + id.length();
                 }
             }
-            if (best != null) {
-                bucket.setZone(DateTimeZone.forID(best));
-                return position + best.length();
-            }
             return ~position;
         }
     }
