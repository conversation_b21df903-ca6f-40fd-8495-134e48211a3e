diff --git a/src/main/java/org/joda/time/DateTimeZone.java b/src/main/java/org/joda/time/DateTimeZone.java
index 855cad0..5d89e34 100644
--- a/src/main/java/org/joda/time/DateTimeZone.java
+++ b/src/main/java/org/joda/time/DateTimeZone.java
@@ -561,11 +561,6 @@ public abstract class DateTimeZone implements Serializable {
             // Backwards compatibility with TimeZone.
             map = new HashMap<String, String>();
             map.put("GMT", "UTC");
-            map.put("WET", "WET");
-            map.put("CET", "CET");
-            map.put("MET", "CET");
-            map.put("ECT", "CET");
-            map.put("EET", "EET");
             map.put("MIT", "Pacific/Apia");
             map.put("HST", "Pacific/Honolulu");  // JDK 1.1 compatible
             map.put("AST", "America/Anchorage");
@@ -574,19 +569,23 @@ public abstract class DateTimeZone implements Serializable {
             map.put("PNT", "America/Phoenix");
             map.put("CST", "America/Chicago");
             map.put("EST", "America/New_York");  // JDK 1.1 compatible
-            map.put("IET", "America/Indiana/Indianapolis");
+            map.put("IET", "America/Indianapolis");
             map.put("PRT", "America/Puerto_Rico");
             map.put("CNT", "America/St_Johns");
-            map.put("AGT", "America/Argentina/Buenos_Aires");
+            map.put("AGT", "America/Buenos_Aires");
             map.put("BET", "America/Sao_Paulo");
+            map.put("WET", "Europe/London");
+            map.put("ECT", "Europe/Paris");
             map.put("ART", "Africa/Cairo");
             map.put("CAT", "Africa/Harare");
+            map.put("EET", "Europe/Bucharest");
             map.put("EAT", "Africa/Addis_Ababa");
+            map.put("MET", "Asia/Tehran");
             map.put("NET", "Asia/Yerevan");
             map.put("PLT", "Asia/Karachi");
-            map.put("IST", "Asia/Kolkata");
+            map.put("IST", "Asia/Calcutta");
             map.put("BST", "Asia/Dhaka");
-            map.put("VST", "Asia/Ho_Chi_Minh");
+            map.put("VST", "Asia/Saigon");
             map.put("CTT", "Asia/Shanghai");
             map.put("JST", "Asia/Tokyo");
             map.put("ACT", "Australia/Darwin");
