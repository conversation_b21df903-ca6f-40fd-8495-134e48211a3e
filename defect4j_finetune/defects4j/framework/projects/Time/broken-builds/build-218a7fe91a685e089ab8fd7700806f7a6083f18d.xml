<project name="joda-time" default="jar" basedir=".">

<!-- Joda-time ANT script -->
<!-- Based on scripts from Apache Jakarta Commons and elsewhere -->

<!-- This is the recommended way to build Joda-Time. -->
<!-- Maven is only intended for building the website. -->

<!-- This ant file will download junit-3.8.1.jar to the lib subdirectory -->
<!-- automatically if it does not find it there already. To change this -->
<!-- behaviour, override the junit.jar property in build.properties. -->

<!-- ========== Properties ================================================ -->

  <property file="build.properties"/>

<!-- ========== Component Declarations ==================================== -->


  <!-- The name of this component -->
  <property name="component.name"          value="joda-time"/>

  <!-- The primary package name of this component -->
  <property name="component.package"       value="org.joda.time"/>

  <!-- The title of this component -->
  <property name="component.title"         value="Joda date and time"/>

  <!-- The current version number of this component -->
  <property name="component.version"       value="2.0"/>
  <property name="previous.version"        value="1.6"/>

  <!-- The current version number of this component -->
  <property name="component.fullname"      value="${component.name}-${component.version}"/>
  
  <!-- The directory of source files -->
  <property name="xdocs"                   value="xdocs"/>

  <!-- Dependencies -->
  <property name="lib"                     value="lib"/>
  <property name="junit.jar"               value="${lib}/junit-3.8.2.jar"/>

  <!-- The directory of source files -->
  <property name="source"                  value="src"/>
  <property name="source.home"             value="${source}/main/java"/>
  <property name="source.tz"               value="${source.home}/org/joda/time/tz/src"/>
  <property name="conf.home"               value="${source}/conf"/>
  <property name="test.home"               value="${source}/test/java"/>

  <!-- The base directory for example sources -->
  <property name="example.home"            value="src/example"/>

  <!-- The base directory for compilation targets -->
  <property name="build"                   value="build"/>
  <property name="build.conf"              value="${build}/conf"/>
  <property name="build.classes"           value="${build}/classes"/>
  <property name="build.tz"                value="${build.classes}/org/joda/time/tz/data"/>
  <property name="build.tests"             value="${build}/tests"/>
  <property name="build.docs"              value="${build}/docs"/>
  <property name="build.sources"           value="${build}/sources"/>
  <property name="build.javadoc"           value="${build}/javadoc"/>
  <property name="build.dist"              value="${build}/dist"/>
  <property name="build.fullname"          value="${build}/${component.fullname}"/>
  <property name="build.dist.fullname"     value="${build.dist}/${component.fullname}"/>
  <property name="build.dist.src.fullname" value="${build.dist.fullname}-src"/>
  <property name="build.dist.bundle"       value="${build.dist.fullname}-bundle"/>

  <property name="repo" value="${user.home}/.maven/repository" />

<!-- ========== Compiler Defaults ========================================= -->

  <!-- Should Java compilations set the 'debug' compiler option? -->
  <property name="compile.debug"           value="true"/>
  <property name="compile.debuglevel"      value="lines,source"/>

  <!-- Should Java compilations set the 'deprecation' compiler option? -->
  <property name="compile.deprecation"     value="false"/>

  <!-- Should Java compilations set the 'optimize' compiler option? -->
  <property name="compile.optimize"        value="true"/>

  <!-- Construct compile classpath -->
  <path id="compile.classpath">
    <pathelement location="${jodaconvert.jar}" />
    <pathelement location="${build.classes}"/>
  </path>


<!-- ========== Test Execution Defaults =================================== -->

  <!-- Construct unit test classpath -->
  <path id="test.classpath">
    <pathelement location="${jodaconvert.jar}" />
    <pathelement location="${build.classes}"/>
    <pathelement location="${build.tests}"/>
    <pathelement location="${junit.jar}"/>
  </path>

  <!-- Should all tests fail if one does? -->
  <property name="test.failonerror"        value="true"/>

  <!-- The test runner to execute -->
  <property name="test.runner"             value="junit.textui.TestRunner"/>


<!-- ====================================================================== -->
<!-- ========== Executable Targets ======================================== -->
<!-- ====================================================================== -->

  <target name="clean"
          description="Clean build and distribution directories">
    <delete dir="${build}"/>
  </target>

<!-- ====================================================================== -->

  <target name="init"
          description="Initialize and evaluate conditionals">
    <echo message="-------- ${component.name} ${component.version} --------"/>
    <filter token="name"                  value="${component.name}"/>
    <filter token="package"               value="${component.package}"/>
    <filter token="version"               value="${component.version}"/>
    <available property="junit.ant" classname="junit.framework.Test"/>
    <available property="junit.present" file="${junit.jar}"/>
    <uptodate property="tz.build.notneeded" targetfile="${build.tz}/ZoneInfoMap" >
      <srcfiles dir= "${source.tz}" includes="**/*.*"/>
    </uptodate>
  </target>

<!-- ====================================================================== -->

  <target name="getjunit" unless="junit.present">
    <echo message="Getting junit from http://www.ibiblio.org/maven/junit"/>
    <setproxy />
    <mkdir dir="${lib}"/>
    <get dest="${junit.jar}" usetimestamp="true" ignoreerrors="true" src="http://www.ibiblio.org/maven/junit/jars/junit-3.8.1.jar" />
  </target>

<!-- ====================================================================== -->

  <target name="installjunit" unless="junit.ant">
    <echo message="Installing junit in ${ant.home}/lib"/>
  	<copy file="${junit.jar}" todir="${ant.home}/lib" />
    <echo message="***************************************************************"/>
    <echo message="*  A copy of junit has been installed in your ant directory   *"/>
    <echo message="*                                                             *"/>
    <echo message="* You will need to restart the ant build to pickup the change *"/>
    <echo message="***************************************************************"/>
  	<fail message="Please restart ant"/>
  </target>

<!-- ====================================================================== -->

  <target name="prepare" depends="init,getjunit,installjunit"
          description="Prepare build directory">
    <mkdir dir="${build}"/>
    <mkdir dir="${build.classes}"/>
    <mkdir dir="${build.conf}"/>
  </target>

<!-- ====================================================================== -->

  <target name="static" depends="prepare"
          description="Copy static files to build directory">
    <tstamp/>
    <copy todir="${build.conf}" filtering="on">
      <fileset dir="${conf.home}" includes="*.MF"/>
    </copy>
  </target>

<!-- ====================================================================== -->

  <target name="compile" depends="compile.main,compile.zoneinfo"
          description="Compile shareable components">
  </target>
          
          
  <target name="compile.main" depends="static"
          description="Compile main datetime classes">
    <javac  srcdir="${source.home}"
           destdir="${build.classes}"
             debug="${compile.debug}"
        debuglevel="${compile.debuglevel}"
       deprecation="${compile.deprecation}"
          optimize="${compile.optimize}"
    	    source="1.5" target="1.5" includeantruntime="false">
      <classpath refid="compile.classpath"/>
    </javac>
    <copy todir="${build.classes}">
      <fileset dir="${source.home}" includes="**/*.properties"/>
    </copy>
  </target>

<!-- ====================================================================== -->

  <target name="compile.zoneinfo"
          depends="compile.main"
          description="Compile timezone data files"
          unless="tz.build.notneeded">
    <!-- Invoke the newly built ZoneInfoCompiler to compile the zoneinfo data files -->
    <mkdir dir="${build.tz}" />
    <java classname="org.joda.time.tz.ZoneInfoCompiler"
          fork="true"
          failonerror="true">
      <classpath path="${build.classes}" />
      <!-- Override default provider since data directory doesn't exist yet -->
      <sysproperty key="org.joda.time.DateTimeZone.Provider"
                   value="org.joda.time.tz.UTCProvider" />
      <!-- Specify source and destination directories -->
      <arg line="-src ${source.tz} -dst ${build.tz}" />
      <!-- Specify all the data files to compile -->
      <arg value="africa" />
      <arg value="antarctica" />
      <arg value="asia" />
      <arg value="australasia" />
      <arg value="europe" />
      <arg value="northamerica" />
      <arg value="southamerica" />
      <arg value="pacificnew" />
      <arg value="etcetera" />
      <arg value="backward" />
      <arg value="systemv" />
    </java>
  </target>
  
<!-- ====================================================================== -->

  <target name="compile.tests" depends="compile"
          description="Compile unit test cases">
    <mkdir dir="${build.tests}"/>
    <javac  srcdir="${test.home}"
           destdir="${build.tests}"
             debug="${compile.debug}"
       deprecation="${compile.deprecation}"
          optimize="${compile.optimize}" includeantruntime="false">
      <classpath refid="test.classpath"/>
    </javac>
    <copy    todir="${build.tests}" filtering="on">
      <fileset dir="${test.home}" excludes="**/*.java"/>
    </copy>
  </target>

<!-- ====================================================================== -->

  <target name="all" depends="clean,compile"
          description="Clean and compile all components"/>

<!-- ====================================================================== -->

  <target name="javadoc" depends="compile"
          description="Create component Javadoc documentation">
    <mkdir      dir="${build.docs}"/>
    <javadoc sourcepath="${source.home}"
                destdir="${build.docs}"
           packagenames="org.joda.time.*"
                 author="true"
                private="false"
                package="false"
                version="true"
                    use="yes"
             splitindex="yes"
               doctitle="&lt;h1&gt;${component.title}&lt;/h1&gt;"
            windowtitle="${component.title} (Version ${component.version})"
                 bottom="Copyright (c) 2001-2006 - Joda.org"
               Overview="${source.home}/org/joda/time/overview.html">
      <classpath refid="compile.classpath"/>
      <group title="User Packages" packages="org.joda.time:org.joda.time.format:org.joda.time.chrono">
      </group>
      <group title="Implementation Packages" packages="org.joda.time.base:org.joda.time.convert:org.joda.time.field:org.joda.time.tz">
      </group>
    </javadoc>
  </target>

<!-- ====================================================================== -->

  <target name="jar" depends="compile"
          description="Create jar">
    <mkdir      dir="${build.classes}/META-INF"/>
    <copy      file="LICENSE.txt"
             tofile="${build.classes}/META-INF/LICENSE.txt"/>
    <copy      file="NOTICE.txt"
             tofile="${build.classes}/META-INF/NOTICE.txt"/>
    <jar    jarfile="${build.fullname}.jar"
            basedir="${build.classes}"
           manifest="${build.conf}/MANIFEST.MF"/>
  </target>

<!-- ====================================================================== -->

  <target name="javadoc.jar">
    <mkdir      dir="${build.javadoc}"/>
    <copy     todir="${build.javadoc}">
  	  <fileset dir="${build.docs}" includes="**/*" />
  	</copy>
    <mkdir      dir="${build.javadoc}/META-INF"/>
    <copy      file="LICENSE.txt"
             tofile="${build.javadoc}/META-INF/LICENSE.txt"/>
    <copy      file="NOTICE.txt"
             tofile="${build.javadoc}/META-INF/NOTICE.txt"/>
    <jar    jarfile="${build.fullname}-javadoc.jar"
            basedir="${build.javadoc}" />
  </target>

<!-- ====================================================================== -->

  <target name="sources.jar">
    <mkdir      dir="${build.sources}"/>
    <copy     todir="${build.sources}">
  	  <fileset dir="${source.home}" includes="**/*.java" />
  	</copy>
    <mkdir      dir="${build.sources}/META-INF"/>
    <copy      file="LICENSE.txt"
             tofile="${build.sources}/META-INF/LICENSE.txt"/>
    <copy      file="NOTICE.txt"
             tofile="${build.sources}/META-INF/NOTICE.txt"/>
    <jar    jarfile="${build.fullname}-sources.jar"
            basedir="${build.sources}" />
  </target>

<!-- ====================================================================== -->

  <target name="dist" depends="compile,jar,test.jar,javadoc,javadoc.jar,sources.jar"
          description="Create binary distribution">
    
	<!-- binary -->
    <delete     dir="${build.dist.fullname}"/>
    <mkdir      dir="${build.dist.fullname}"/>
    <copy      file="LICENSE.txt" todir="${build.dist.fullname}"/>
    <copy      file="NOTICE.txt" todir="${build.dist.fullname}"/>
    <copy      file="RELEASE-NOTES.txt" todir="${build.dist.fullname}"/>
    <copy      file="${build.fullname}.jar"
              todir="${build.dist.fullname}"/>
    <copy      file="${build.fullname}-sources.jar"
              todir="${build.dist.fullname}"/>
    <copy      file="${build.fullname}-javadoc.jar"
              todir="${build.dist.fullname}"/>
    
	<fixcrlf srcdir="${build.dist.fullname}" eol="lf" includes="*.txt"/>
	<tar   destfile="${build.fullname}.tar" basedir="${build.dist}"/>
	<gzip   zipfile="${build.fullname}.tar.gz" src="${build.fullname}.tar"/>
	<delete    file="${build.fullname}.tar"/>
	<fixcrlf srcdir="${build.dist.fullname}" eol="crlf" includes="*.txt"/>
	<zip   destfile="${build.fullname}.zip" basedir="${build.dist}"/>
    <delete     dir="${build.dist.fullname}"/>
	
	<!-- source -->
    <delete     dir="${build.dist.src.fullname}"/>
    <mkdir      dir="${build.dist.src.fullname}"/>
    <copy      file="LICENSE.txt" todir="${build.dist.src.fullname}"/>
    <copy      file="NOTICE.txt" todir="${build.dist.src.fullname}"/>
    <copy      file="RELEASE-NOTES.txt" todir="${build.dist.src.fullname}"/>
    <copy      file="${build.fullname}.jar"
              todir="${build.dist.src.fullname}"/>
    <copy     todir="${build.dist.src.fullname}">
      <fileset  dir="." includes="${source}/**/*" excludes="CVS/**/*"/>
    </copy>
    <copy     todir="${build.dist.src.fullname}">
      <fileset  dir="." includes="${xdocs}/**/*" excludes="CVS/**/*"/>
    </copy>
	<delete     dir="${build.dist.src.fullname}/src/tzdata"/>
    <copy      file="build.xml" todir="${build.dist.src.fullname}"/>
    <copy      file="pom.xml" todir="${build.dist.src.fullname}"/>
    <copy      file="checkstyle.xml" todir="${build.dist.src.fullname}"/>
    <copy      file="ToDo.txt" todir="${build.dist.src.fullname}"/>
	
	<fixcrlf srcdir="${build.dist.src.fullname}" eol="lf" includes="*.txt,*.properties,*.xml"/>
	<tar   destfile="${build.fullname}-src.tar" basedir="${build.dist}"/>
	<gzip   zipfile="${build.fullname}-src.tar.gz" src="${build.fullname}-src.tar"/>
	<delete    file="${build.fullname}-src.tar"/>
	<fixcrlf srcdir="${build.dist.src.fullname}" eol="crlf" includes="*.txt,*.properties,*.xml"/>
	<zip   destfile="${build.fullname}-src.zip" basedir="${build.dist}"/>
    <delete     dir="${build.dist.src.fullname}"/>
  	
	<!-- bundle -->
    <delete     dir="${build.dist.bundle}"/>
    <mkdir      dir="${build.dist.bundle}"/>
    <copy      file="LICENSE.txt" todir="${build.dist.bundle}"/>
    <copy      file="NOTICE.txt" todir="${build.dist.bundle}"/>
    <copy      file="pom.xml" todir="${build.dist.bundle}"/>
    <copy      file="${build.fullname}.jar"
              todir="${build.dist.bundle}"/>
    <copy      file="${build.fullname}-sources.jar"
              todir="${build.dist.bundle}"/>
	<copy      file="${build.fullname}-javadoc.jar"
	          todir="${build.dist.bundle}"/>
    
	<fixcrlf srcdir="${build.dist.bundle}" eol="crlf" includes="*.txt"/>
	<jar    jarfile="${build.fullname}-bundle.jar" basedir="${build.dist.bundle}"/>
    <delete     dir="${build.dist.bundle}"/>
	
  </target>

<!-- ====================================================================== -->

  <target name="test"  depends="compile.tests, test.time"
          description="Run all unit test cases">
  </target>

  <target name="test.time" depends="compile.tests,compile.zoneinfo">
    <echo message="Running time tests ..."/>
    <junit printsummary="yes" haltonfailure="yes">
      <formatter type="plain" usefile="false" />
      <classpath>
        <pathelement location="${build.classes}"/>
        <pathelement location="${build.tests}"/>
        <pathelement location="${build.tz}"/>
        <pathelement path="${java.class.path}"/>
      </classpath>

      <batchtest fork="yes">
        <fileset dir="${test.home}">
          <include name="**/TestAll.java"/>
        </fileset>
      </batchtest>
    </junit>
  </target>

  <!-- don't depend on jar, so we can test jar built on another JDK version -->
  <target name="test.jar" depends="compile.tests">
    <echo message="Running time tests from jar ..."/>
    <junit printsummary="yes" haltonfailure="yes">
      <formatter type="plain" usefile="false" />
      <classpath>
        <pathelement location="${build.fullname}.jar"/>
        <pathelement location="${build.tests}"/>
        <pathelement location="${junit.jar}"/>
        <pathelement path="${java.class.path}"/>
      </classpath>

      <batchtest fork="yes">
        <fileset dir="${test.home}">
          <include name="**/TestAll.java"/>
        </fileset>
      </batchtest>
    </junit>
  </target>

  <target name="clirr" depends="jar" description="clirr binary compatibility">
  	<echo message="${repo}/clirr/jars/clirr-core-0.6-uber.jar"></echo>
    <taskdef classpath="${repo}/clirr/jars/clirr-core-0.6-uber.jar"
        resource="clirrtask.properties"/>

    <clirr>
      <origfiles dir="${repo}/${component.name}/jars" includes="${component.name}-${previous.version}.jar"/>
      <newfiles dir="." includes="${build.fullname}.jar"/>
      <formatter type="xml" outfile="build/clirr.xml" />
    </clirr>
  </target>

  <!--property name="emma.dir" value="${lib}" />
  <path id="emma.lib" >
    <pathelement location="${emma.dir}/emma.jar" />
    <pathelement location="${emma.dir}/emma_ant.jar" />
  </path>
  <target name="emma" description="turns on EMMA's on-the-fly instrumentation mode" >
  	<taskdef resource="emma_ant.properties" classpathref="emma.lib" />
    <property name="emma.enabled" value="true" />
  </target-->
</project>
