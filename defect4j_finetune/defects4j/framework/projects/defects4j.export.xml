<!--
Copyright (c) 2014-2024 <PERSON>, <PERSON><PERSON><PERSON>, and Defects<PERSON>J contributors.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERC<PERSON>NTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

################################################################################
           This build file defines all export targets for Defects4J.

It is imported by the main build file (defects4j.build.xml) and not intended to
be used stand-alone.
#############################################################################-->
<project name="D4J-export">
<!-- Source directory of classes -->
    <target name="export.dir.src.classes"
            description="Source directory of classes"
            depends="sanity.check">
        <fail message="Property file.export not set!" unless="file.export" />
        <property name="dir.rel.src.classes" value="${d4j.dir.src.classes}" relative="yes" />

        <echo message="${dir.rel.src.classes}" file="${file.export}"/>
    </target>

<!-- Target directory of classes -->
    <target name="export.dir.bin.classes"
            description="Target directory of classes"
            depends="sanity.check">
        <fail message="Property file.export not set!" unless="file.export" />
        <property name="dir.rel.bin.classes" value="${classes.dir}" relative="yes" />

        <echo message="${dir.rel.bin.classes}" file="${file.export}"/>
    </target>

<!-- Source directory of tests -->
    <target name="export.dir.src.tests"
            description="Source directory of tests"
            depends="sanity.check">
        <fail message="Property file.export not set!" unless="file.export" />
        <property name="dir.rel.src.tests" value="${d4j.dir.src.tests}" relative="yes" />

        <echo message="${dir.rel.src.tests}" file="${file.export}"/>
    </target>

<!-- Target directory of test classes -->
    <target name="export.dir.bin.tests"
            description="Target directory of test classes"
            depends="sanity.check">
        <fail message="Property file.export not set!" unless="file.export" />
        <property name="dir.rel.bin.tests" value="${test.classes.dir}" relative="yes" />

        <echo message="${dir.rel.bin.tests}" file="${file.export}"/>
    </target>

<!-- Compile classpath -->
    <target name="export.cp.compile"
            description="Classpath to compile the project"
            depends="sanity.check,compile">
        <fail message="Property file.export not set!" unless="file.export" />

        <path id="project.classpath">
            <pathelement path="${classes.dir}" />
            <path refid="compile.classpath" />
        </path>

        <pathconvert property="cp.compile" refid="project.classpath" />
        <echo message="${cp.compile}" file="${file.export}"/>
    </target>

<!-- Test classpath -->
    <target name="export.cp.test"
            description="Classpath to compile and run the developer-written tests"
            depends="sanity.check,compile">
        <fail message="Property file.export not set!" unless="file.export" />

        <pathconvert property="cp.test" refid="d4j.test.classpath" />
        <echo message="${cp.test}" file="${file.export}"/>
    </target>

<!-- Relevant classes -->
    <target name="export.classes.relevant"
            description="Classes loaded by the triggering tests"
            depends="sanity.check">
        <fail message="Property file.export not set!" unless="file.export" />

        <echo message="${d4j.classes.relevant}" file="${file.export}"/>

        <loadfile property="classes.rel" srcFile="${file.export}">
        <filterchain>
            <tokenfilter>
                <replaceregex pattern="[,;]" replace="${line.separator}" flags="g"/>
            </tokenfilter>
        </filterchain>
        </loadfile>
        <echo message="${classes.rel}" file="${file.export}"/>
    </target>

<!-- Modified classes -->
    <target name="export.classes.modified"
            description="Classes modified by the bug fix"
            depends="sanity.check">
        <fail message="Property file.export not set!" unless="file.export" />

        <echo message="${d4j.classes.modified}" file="${file.export}"/>

        <loadfile property="classes.mod" srcFile="${file.export}">
        <filterchain>
            <tokenfilter>
                <replaceregex pattern="[,;]" replace="${line.separator}" flags="g"/>
            </tokenfilter>
        </filterchain>
        </loadfile>
        <echo message="${classes.mod}" file="${file.export}"/>
    </target>

<!-- Trigger tests -->
    <target name="export.tests.trigger"
            description="Trigger tests that expose the bug"
            depends="sanity.check">
        <fail message="Property file.export not set!" unless="file.export" />

        <echo message="${d4j.tests.trigger}" file="${file.export}"/>

        <loadfile property="trigger.tests" srcFile="${file.export}">
        <filterchain>
            <tokenfilter>
                <replaceregex pattern="[,;]" replace="${line.separator}" flags="g"/>
            </tokenfilter>
        </filterchain>
        </loadfile>
        <echo message="${trigger.tests}" file="${file.export}"/>
    </target>

<!-- Relevant tests -->
    <target name="export.tests.relevant"
            description="Relevant tests that touch at least one of the modified sources"
            depends="sanity.check">
        <fail message="Property file.export not set!" unless="file.export" />

        <loadfile property="relevant.tests"
            srcFile="${d4j.home}/framework/projects/${d4j.project.id}/relevant_tests/${d4j.bug.id}" />
        <echo message="${relevant.tests}" file="${file.export}" />
    </target>


<!-- TODO: Closure defines the file set of tests on the target directory, which
requires the dependency to the compile targets. Can we get rid of this special
case? -->
<!-- List of all manual test classes -->
    <target name="export.tests.all"
            description="List of all developer-written tests"
            depends="sanity.check,compile,compile.tests">
        <fail message="Property file.export not set!" unless="file.export" />
        <fail message="File set all.manual.tests not set!" unless="ant.refid:all.manual.tests" />

        <!-- Get all developer-written tests, separated by newline -->
        <pathconvert pathsep="${line.separator}" property="tmp.tests" refid="all.manual.tests" />
        <echo message="${tmp.tests}" file="${file.export}"/>
        <!-- Get fully qualified class names, i.e., remove path prefix,
             replace file separator by '.' and remove ".java" or ".class" -->
        <property name="src.prefix" location="${d4j.workdir}${file.separator}${d4j.dir.src.tests}"/>
        <property name="bin.prefix" location="${test.home}"/>
        <loadfile property="all.tests" srcFile="${file.export}">
        <filterchain>
            <tokenfilter>
                <replaceregex pattern="${src.prefix}${file.separator}" replace="" />
                <replaceregex pattern="${bin.prefix}${file.separator}" replace="" />
                <replaceregex pattern="${file.separator}" replace="." flags="g" />
                <replaceregex pattern=".java$" replace="" />
                <replaceregex pattern=".class$" replace="" />
            </tokenfilter>
        </filterchain>
        </loadfile>
        <echo message="${all.tests}" file="${file.export}"/>
    </target>
</project>
