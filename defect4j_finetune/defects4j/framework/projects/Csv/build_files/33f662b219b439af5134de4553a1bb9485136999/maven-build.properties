#Generated by <PERSON><PERSON> Ant Plugin - DO NOT EDIT THIS FILE!
#Mon Apr 15 08:38:20 PDT 2019
commons.compiler.compilerVersion=
commons.release.2.name=commons-csv-${commons.release.2.version}
checkstyle.version=2.17
commons.release.desc=(Java 7+)
maven.build.srcDir.0=src/main/java
commons.binary.suffix=-bin
commons.manifestfile=/tmp/initialize-revisions.pl_10900_1555341828/67b/target/osgi/MANIFEST.MF
maven.build.timestamp.format=yyyy-MM-dd HH\:mm\:ssZ
commons.project-info.version=2.9
maven.build.resourceDir.1=.
maven.build.resourceDir.0=src/main/resources
project.build.sourceEncoding=UTF-8
commons.changes.onlyCurrentVersion=false
sourceReleaseAssemblyDescriptor=source-release
commons.release.3.name=commons-csv-${commons.release.3.version}
maven.build.outputDir=${maven.build.dir}/classes
commons.componentid=csv
commons.site.path=commons-csv
maven.repo.local=${user.home}/.m2/repository
maven.build.finalName=commons-csv-1.6-SNAPSHOT
commons.osgi.private=
maven.build.testDir.0=src/test/java
commons.javadoc.javaee.link=http\://docs.oracle.com/javaee/6/api/
commons.osgi.export=org.apache.commons.*;version\=1.6-SNAPSHOT;-noimport\:\=true
commons.release.2.binary.suffix=-bin
maven.reporting.outputDirectory=${maven.build.dir}/site
commons.compiler.javac=
commons.encoding=UTF-8
organization.logo=https\://www.apache.org/images/asf_logo_wide.gif
commons.scmPubUrl=https\://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-csv
sonar.host.url=https\://analysis.apache.org/
commons.rc.version=RC1
commons.cobertura.version=2.7
commons.build-helper.version=3.0.0
commons.release.name=commons-csv-1.6
commons.jacoco.haltOnFailure=false
minSeverity=info
commons.jacoco.methodRatio=0.95
commons.animal-sniffer.version=1.16
commons.findbugs.version=3.0.5
maven.build.testOutputDir=${maven.build.dir}/test-classes
maven.compiler.target=1.7
project.reporting.outputEncoding=UTF-8
commons.scmPubServer=commons.site
animal-sniffer.signature=java${javaTarget.majorVersion}${javaTarget.minorVersion}
maven.build.dir=target
maven.test.reports=${maven.build.dir}/test-reports
gpg.useagent=true
commons.jacoco.complexityRatio=0.85
commons.javadoc.version=3.0.0
commons.surefire.version=2.20.1
commons.changes.version=2.12.1
commons.failsafe.version=2.20.1
surefire.version=2.19.1
commons.coveralls.timestampFormat=EpochMillis
commons.release.version=1.6
commons.japicmp.breakBuildOnBinaryIncompatibleModifications=true
commons.osgi.excludeDependencies=true
commons.jxr.version=2.5
commons.surefire-report.version=2.20.1
commons.rat.version=0.12
commons.site.cache=/home/<USER>/commons-sites
project.build.outputDirectory=${maven.build.outputDir}
commons.docEncoding=UTF-8
commons.jacoco.branchRatio=0.85
commons.javadoc.java.link=http\://docs.oracle.com/javase/7/docs/api/
commons.japicmp.version=0.11.0
commons.changes.runOnlyAtExecutionRoot=false
commons.deployment.protocol=scp
maven.settings.offline=false
distMgmtSnapshotsName=Apache Development Snapshot Repository
commons.scm-publish.version=1.1
commons.animal-sniffer.signature.version=1.0
commons.jacoco.instructionRatio=0.90
maven.settings.interactiveMode=true
commons.compiler.version=3.7.0
commons.changes.maxEntries=100
commons.jacoco.lineRatio=0.90
commons.release.2.desc=
commons.jira.pid=12313222
project.build.directory=${maven.build.dir}
commons.module.name=org.apache.commons.csv
arguments=
commons.surefire-report.aggregate=false
commons.coveralls.version=4.3.0
commons.wagon-ssh.version=3.0.0
commons.release.3.binary.suffix=-bin
commons.build-plugin.version=1.7
distMgmtSnapshotsUrl=https\://repository.apache.org/content/repositories/snapshots
maven.compiler.source=1.7
commons.jacoco.classRatio=1.00
maven.build.testResourceDir.1=.
maven.build.testResourceDir.0=src/test/resources
commons.osgi.dynamicImport=
commons.compiler.fork=false
commons.jdepend.version=2.0
checkstyle.resourceExcludes=LICENSE.txt, NOTICE.txt
commons.release.3.desc=
commons.felix.version=3.4.0
implementation.build=${scmBranch}@r${buildNumber}; 2019-04-15 15\:38\:19+0000
commons.site-plugin.version=3.7
commons.osgi.symbolicName=org.apache.commons.csv
commons.surefire.java=
commons.jacoco.version=0.7.9
commons.osgi.import=*
checkstyle.header.file=/tmp/initialize-revisions.pl_10900_1555341828/67b/LICENSE-header.txt
commons.jira.id=CSV
commons.clirr.version=2.8
commons.scmPubCheckoutDirectory=/home/<USER>/commons-sites/commons-csv
