#Generated by <PERSON><PERSON> Ant Plugin - DO NOT EDIT THIS FILE!
#Mon Apr 15 08:25:36 PDT 2019
maven.build.testDir.0=src/test/java
implementation.build=${scmBranch}@r${buildNumber}; 2019-04-15 15\:25\:35+0000
commons.changes.version=2.6
maven.settings.offline=false
commons.clirr.version=2.3
commons.encoding=iso-8859-1
commons.compiler.javac=
commons.javadoc.java.link=http\://download.oracle.com/javase/6/docs/api/
commons.osgi.dynamicImport=
commons.docEncoding=iso-8859-1
commons.release.2.name=commons-csv-${commons.release.2.version}
maven.settings.interactiveMode=true
commons.osgi.private=
maven.build.timestamp.format=yyyy-MM-dd HH\:mm\:ssZ
commons.jxr.version=2.3
commons.release.version=23
commons.componentid=csv
maven.reporting.outputDirectory=${maven.build.dir}/site
commons.osgi.symbolicName=org.apache.commons.csv
commons.javadoc.javaee.link=http\://download.oracle.com/javaee/6/api/
project.build.sourceEncoding=iso-8859-1
maven.build.testOutputDir=${maven.build.dir}/test-classes
commons.compiler.fork=false
commons.binary.suffix=-bin
maven.build.finalName=commons-csv-1.0-SNAPSHOT
maven.test.reports=${maven.build.dir}/test-reports
commons.release.name=commons-csv-23
commons.osgi.export=org.apache.commons.*;version\=1.0-SNAPSHOT;-noimport\:\=true
maven.compile.target=1.5
distMgmtSnapshotsName=Apache Development Snapshot Repository
commons.project-info.version=2.4
maven.repo.local=${user.home}/.m2/repository
maven.build.dir=target
distMgmtSnapshotsUrl=https\://repository.apache.org/content/repositories/snapshots
commons.surefire.java=
maven.build.outputDir=${maven.build.dir}/classes
commons.osgi.import=*
project.reporting.outputEncoding=iso-8859-1
commons.surefire.version=2.9
project.build.directory=${maven.build.dir}
maven.build.resourceDir.1=.
maven.build.resourceDir.0=src/main/resources
commons.release.2.desc=
commons.manifestfile=target/osgi/MANIFEST.MF
organization.logo=http\://www.apache.org/images/asf_logo_wide.gif
commons.jira.pid=12313222
maven.build.srcDir.0=src/main/java
commons.release.2.binary.suffix=-bin
commons.rc.version=RC1
commons.rat.version=0.7
commons.compiler.compilerVersion=
minSeverity=info
maven.build.testResourceDir.1=.
maven.build.testResourceDir.0=src/test/resources
commons.javadoc.version=2.8
maven.compile.source=1.5
commons.deployment.protocol=scp
project.build.outputDirectory=${maven.build.outputDir}
sourceReleaseAssemblyDescriptor=source-release
commons.surefire-report.version=2.9
commons.release.desc=
commons.jira.id=CSV
