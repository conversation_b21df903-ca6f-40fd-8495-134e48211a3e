#Generated by <PERSON><PERSON> Ant Plugin - DO NOT EDIT THIS FILE!
#Mon Apr 15 08:29:03 PDT 2019
commons.compiler.compilerVersion=
commons.release.2.name=commons-csv-${commons.release.2.version}
checkstyle.version=2.12
commons.release.desc=
maven.build.srcDir.0=src/main/java
commons.binary.suffix=-bin
commons.manifestfile=/tmp/initialize-revisions.pl_10900_1555341828/28b/target/osgi/MANIFEST.MF
maven.build.timestamp.format=yyyy-MM-dd HH\:mm\:ssZ
commons.project-info.version=2.7
maven.build.resourceDir.1=.
maven.build.resourceDir.0=src/main/resources
project.build.sourceEncoding=UTF-8
sourceReleaseAssemblyDescriptor=source-release
commons.release.3.name=commons-csv-${commons.release.3.version}
maven.build.outputDir=${maven.build.dir}/classes
commons.componentid=csv
commons.site.path=commons-csv
maven.repo.local=${user.home}/.m2/repository
maven.build.finalName=commons-csv-1.0-SNAPSHOT
commons.osgi.private=
maven.build.testDir.0=src/test/java
commons.javadoc.javaee.link=http\://docs.oracle.com/javaee/6/api/
commons.osgi.export=org.apache.commons.*;version\=1.0-SNAPSHOT;-noimport\:\=true
commons.release.2.binary.suffix=-bin
maven.reporting.outputDirectory=${maven.build.dir}/site
commons.compiler.javac=
commons.encoding=UTF-8
organization.logo=http\://www.apache.org/images/asf_logo_wide.gif
commons.scmPubUrl=https\://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-csv
sonar.host.url=https\://analysis.apache.org/
commons.rc.version=RC1
commons.cobertura.version=2.6
commons.release.name=commons-csv-1.0
commons.jacoco.haltOnFailure=false
minSeverity=info
commons.jacoco.methodRatio=0.95
maven.build.testOutputDir=${maven.build.dir}/test-classes
maven.compiler.target=1.5
project.reporting.outputEncoding=UTF-8
maven.build.dir=target
maven.test.reports=${maven.build.dir}/test-reports
gpg.useagent=true
commons.jacoco.complexityRatio=0.85
commons.javadoc.version=2.9.1
commons.surefire.version=2.16
commons.changes.version=2.9
commons.release.version=1.0
commons.jxr.version=2.4
commons.surefire-report.version=2.16
commons.rat.version=0.10
commons.site.cache=/home/<USER>/commons-sites
project.build.outputDirectory=${maven.build.outputDir}
commons.docEncoding=UTF-8
commons.jacoco.branchRatio=0.85
commons.javadoc.java.link=http\://docs.oracle.com/javase/6/docs/api/
commons.deployment.protocol=scp
maven.settings.offline=false
distMgmtSnapshotsName=Apache Development Snapshot Repository
commons.scm-publish.version=1.0-beta-2
commons.jacoco.instructionRatio=0.90
maven.settings.interactiveMode=true
commons.compiler.version=3.1
commons.jacoco.lineRatio=0.90
commons.release.2.desc=
commons.jira.pid=12313222
project.build.directory=${maven.build.dir}
arguments=
commons.surefire-report.aggregate=false
commons.wagon-ssh.version=2.3
commons.release.3.binary.suffix=-bin
distMgmtSnapshotsUrl=https\://repository.apache.org/content/repositories/snapshots
maven.compiler.source=1.5
commons.jacoco.classRatio=1.00
maven.build.testResourceDir.1=.
maven.build.testResourceDir.0=src/test/resources
commons.osgi.dynamicImport=
commons.compiler.fork=false
commons.jdepend.version=2.0-beta-2
commons.release.3.desc=
implementation.build=${scmBranch}@r${buildNumber}; 2019-04-15 15\:29\:02+0000
commons.osgi.symbolicName=org.apache.commons.csv
commons.site-plugin.version=3.3
commons.surefire.java=
commons.jacoco.version=0.6.4.201312101107
commons.osgi.import=*
checkstyle.header.file=/tmp/initialize-revisions.pl_10900_1555341828/28b/LICENSE-header.txt
commons.jira.id=CSV
commons.clirr.version=2.6.1
commons.scmPubCheckoutDirectory=/home/<USER>/commons-sites/commons-csv
