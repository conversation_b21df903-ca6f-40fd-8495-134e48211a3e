<?xml version="1.0" encoding="UTF-8"?>

<!-- ====================================================================== -->
<!-- Ant build file (http://ant.apache.org/) for Ant 1.6.2 or above.        -->
<!-- ====================================================================== -->

<!-- ====================================================================== -->
<!-- ===================== - DO NOT EDIT THIS FILE! - ===================== -->
<!-- ====================================================================== -->
<!--                                                                        -->
<!-- Any modifications will be overwritten.                                 -->
<!--                                                                        -->
<!-- Generated by Maven Ant Plugin on 4/15/19 8:26 AM                       -->
<!-- See: http://maven.apache.org/plugins/maven-ant-plugin/                 -->
<!--                                                                        -->
<!-- ====================================================================== -->

<project name="commons-csv-from-maven" default="package" basedir=".">

  <!-- ====================================================================== -->
  <!-- Build environment properties                                           -->
  <!-- ====================================================================== -->

  <property file="${user.home}/.m2/maven.properties"/>
  <property file="maven-build.properties"/>

  <property name="maven.build.finalName" value="commons-csv-1.0-SNAPSHOT"/>
  <property name="maven.build.dir" value="target"/>
  <property name="maven.build.outputDir" value="${maven.build.dir}/classes"/>
  <property name="maven.build.srcDir.0" value="src/main/java"/>
  <property name="maven.build.resourceDir.0" value="src/main/resources"/>
  <property name="maven.build.resourceDir.1" value="."/>
  <property name="maven.build.testOutputDir" value="${maven.build.dir}/test-classes"/>
  <property name="maven.build.testDir.0" value="src/test/java"/>
  <property name="maven.build.testResourceDir.0" value="src/test/resources"/>
  <property name="maven.build.testResourceDir.1" value="."/>
  <property name="maven.test.reports" value="${maven.build.dir}/test-reports"/>
  <property name="maven.reporting.outputDirectory" value="${maven.build.dir}/site"/>

  <property name="maven.repo.local" value="${user.home}/.m2/repository"/>
  <property name="maven.settings.offline" value="false"/>
  <property name="maven.settings.interactiveMode" value="true"/>

  <!-- ====================================================================== -->
  <!-- Defining classpaths                                                    -->
  <!-- ====================================================================== -->

  <path id="build.classpath"/>
  <path id="build.test.classpath">
    <pathelement location="${maven.repo.local}/junit/junit/4.11/junit-4.11.jar"/>
    <pathelement location="${maven.repo.local}/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar"/>
    <pathelement location="${maven.repo.local}/commons-io/commons-io/2.2/commons-io-2.2.jar"/>
    <pathelement location="${maven.repo.local}/com/h2database/h2/1.3.168/h2-1.3.168.jar"/>
  </path>

  <!-- ====================================================================== -->
  <!-- Cleaning up target                                                     -->
  <!-- ====================================================================== -->

  <target name="clean" description="Clean the output directory">
    <delete dir="${maven.build.dir}"/>
  </target>

  <!-- ====================================================================== -->
  <!-- Compilation target                                                     -->
  <!-- ====================================================================== -->

  <target name="compile" depends="get-deps" description="Compile the code">
    <mkdir dir="${maven.build.outputDir}"/>
    <javac destdir="${maven.build.outputDir}" 
           encoding="UTF-8" 
           nowarn="false" 
           debug="true" 
           optimize="false" 
           deprecation="true" 
           target="1.5" 
           verbose="false" 
           fork="false" 
           source="1.5">
      <src>
        <pathelement location="${maven.build.srcDir.0}"/>
      </src>
      <classpath refid="build.classpath"/>
    </javac>
    <mkdir dir="${maven.build.outputDir}/META-INF"/>
    <copy todir="${maven.build.outputDir}/META-INF">
      <fileset dir="${maven.build.resourceDir.1}">
        <include name="NOTICE.txt"/>
        <include name="LICENSE.txt"/>
      </fileset>
    </copy>
  </target>

  <!-- ====================================================================== -->
  <!-- Test-compilation target                                                -->
  <!-- ====================================================================== -->

  <target name="compile-tests" 
          depends="compile" 
          description="Compile the test code" 
          unless="maven.test.skip">
    <mkdir dir="${maven.build.testOutputDir}"/>
    <javac destdir="${maven.build.testOutputDir}" 
           encoding="UTF-8" 
           nowarn="false" 
           debug="true" 
           optimize="false" 
           deprecation="true" 
           target="1.5" 
           verbose="false" 
           fork="false" 
           source="1.5">
      <src>
        <pathelement location="${maven.build.testDir.0}"/>
      </src>
      <classpath>
        <path refid="build.test.classpath"/>
        <pathelement location="${maven.build.outputDir}"/>
      </classpath>
    </javac>
    <copy todir="${maven.build.testOutputDir}">
      <fileset dir="${maven.build.testResourceDir.0}"/>
    </copy>
    <mkdir dir="${maven.build.testOutputDir}/META-INF"/>
    <copy todir="${maven.build.testOutputDir}/META-INF">
      <fileset dir="${maven.build.testResourceDir.1}">
        <include name="NOTICE.txt"/>
        <include name="LICENSE.txt"/>
      </fileset>
    </copy>
  </target>

  <!-- ====================================================================== -->
  <!-- Run all tests                                                          -->
  <!-- ====================================================================== -->

  <target name="test" 
          depends="compile-tests, junit-missing" 
          unless="junit.skipped" 
          description="Run the test cases">
    <mkdir dir="${maven.test.reports}"/>
    <junit printSummary="yes" haltonerror="true" haltonfailure="true" fork="true" dir=".">
      <sysproperty key="basedir" value="."/>
      <formatter type="xml"/>
      <formatter type="plain" usefile="false"/>
      <classpath>
        <path refid="build.test.classpath"/>
        <pathelement location="${maven.build.outputDir}"/>
        <pathelement location="${maven.build.testOutputDir}"/>
      </classpath>
      <batchtest todir="${maven.test.reports}" unless="test">
        <fileset dir="${maven.build.testDir.0}">
          <include name="**/Test*.java"/>
          <include name="**/*Test.java"/>
          <include name="**/*TestCase.java"/>
          <exclude name="**/perf/PerformanceTest.java"/>
        </fileset>
      </batchtest>
      <batchtest todir="${maven.test.reports}" if="test">
        <fileset dir="${maven.build.testDir.0}">
          <include name="**/${test}.java"/>
          <exclude name="**/perf/PerformanceTest.java"/>
        </fileset>
      </batchtest>
    </junit>
  </target>

  <target name="test-junit-present">
    <available classname="junit.framework.Test" property="junit.present" classpathref="build.test.classpath"/>
  </target>

  <target name="test-junit-status" 
          depends="test-junit-present">
    <condition property="junit.missing">
      <and>
        <isfalse value="${junit.present}"/>
        <isfalse value="${maven.test.skip}"/>
      </and>
    </condition>
    <condition property="junit.skipped">
      <or>
        <isfalse value="${junit.present}"/>
        <istrue value="${maven.test.skip}"/>
      </or>
    </condition>
  </target>

  <target name="junit-missing" 
          depends="test-junit-status" 
          if="junit.missing">
    <echo>=================================== WARNING ===================================</echo>
    <echo> JUnit is not present in the test classpath or your $ANT_HOME/lib directory. Tests not executed.</echo>
    <echo>===============================================================================</echo>
  </target>

  <!-- ====================================================================== -->
  <!-- Javadoc target                                                         -->
  <!-- ====================================================================== -->

  <target name="javadoc" description="Generates the Javadoc of the application">
    <javadoc sourcepath="${maven.build.srcDir.0}" 
             packagenames="*" 
             destdir="${maven.reporting.outputDirectory}/apidocs" 
             access="protected" 
             old="false" 
             verbose="false" 
             encoding="UTF-8" 
             version="true" 
             use="true" 
             author="true" 
             splitindex="false" 
             nodeprecated="false" 
             nodeprecatedlist="false" 
             notree="false" 
             noindex="false" 
             nohelp="false" 
             nonavbar="false" 
             serialwarn="false" 
             charset="ISO-8859-1" 
             docencoding="UTF-8" 
             source="1.5" 
             linksource="true" 
             breakiterator="false">
      <link href="http://download.oracle.com/javase/6/docs/api/"/>
      <link href="http://download.oracle.com/javaee/6/api/"/>
    </javadoc>
  </target>

  <!-- ====================================================================== -->
  <!-- Package target                                                         -->
  <!-- ====================================================================== -->

  <target name="package" depends="compile,test" description="Package the application">
    <jar jarfile="${maven.build.dir}/${maven.build.finalName}.jar" 
         compress="true" 
         index="false" 
         manifest="/tmp/initialize-revisions.pl_10900_1555341828/18b/target/osgi/MANIFEST.MF" 
         basedir="${maven.build.outputDir}" 
         excludes="**/package.html">
      <manifest>
        <attribute name="Main-Class"/>
      </manifest>
    </jar>
  </target>

  <!-- ====================================================================== -->
  <!-- A dummy target for the package named after the type it creates         -->
  <!-- ====================================================================== -->

  <target name="jar" depends="package" description="Builds the jar for the application"/>

  <!-- ====================================================================== -->
  <!-- Download dependencies target                                           -->
  <!-- ====================================================================== -->

  <target name="test-offline">
    <condition property="maven.mode.offline">
      <equals arg1="${maven.settings.offline}" arg2="true"/>
    </condition>
  </target>

  <target name="get-deps" 
          depends="test-offline" 
          description="Download all dependencies" 
          unless="maven.mode.offline">
    <mkdir dir="${maven.repo.local}"/>
    <mkdir dir="${maven.repo.local}/junit/junit/4.11"/>
    <get src="http://repository.apache.org/snapshots/junit/junit/4.11/junit-4.11.jar" 
         dest="${maven.repo.local}/junit/junit/4.11/junit-4.11.jar" 
         usetimestamp="false" 
         ignoreerrors="true"/>
    <get src="https://repo.maven.apache.org/maven2/junit/junit/4.11/junit-4.11.jar" 
         dest="${maven.repo.local}/junit/junit/4.11/junit-4.11.jar" 
         usetimestamp="false" 
         ignoreerrors="true"/>
    <mkdir dir="${maven.repo.local}/org/hamcrest/hamcrest-core/1.3"/>
    <get src="http://repository.apache.org/snapshots/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar" 
         dest="${maven.repo.local}/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar" 
         usetimestamp="false" 
         ignoreerrors="true"/>
    <get src="https://repo.maven.apache.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar" 
         dest="${maven.repo.local}/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar" 
         usetimestamp="false" 
         ignoreerrors="true"/>
    <mkdir dir="${maven.repo.local}/commons-io/commons-io/2.2"/>
    <get src="http://repository.apache.org/snapshots/commons-io/commons-io/2.2/commons-io-2.2.jar" 
         dest="${maven.repo.local}/commons-io/commons-io/2.2/commons-io-2.2.jar" 
         usetimestamp="false" 
         ignoreerrors="true"/>
    <get src="https://repo.maven.apache.org/maven2/commons-io/commons-io/2.2/commons-io-2.2.jar" 
         dest="${maven.repo.local}/commons-io/commons-io/2.2/commons-io-2.2.jar" 
         usetimestamp="false" 
         ignoreerrors="true"/>
    <mkdir dir="${maven.repo.local}/com/h2database/h2/1.3.168"/>
    <get src="http://repository.apache.org/snapshots/com/h2database/h2/1.3.168/h2-1.3.168.jar" 
         dest="${maven.repo.local}/com/h2database/h2/1.3.168/h2-1.3.168.jar" 
         usetimestamp="false" 
         ignoreerrors="true"/>
    <get src="https://repo.maven.apache.org/maven2/com/h2database/h2/1.3.168/h2-1.3.168.jar" 
         dest="${maven.repo.local}/com/h2database/h2/1.3.168/h2-1.3.168.jar" 
         usetimestamp="false" 
         ignoreerrors="true"/>
  </target>

</project>
