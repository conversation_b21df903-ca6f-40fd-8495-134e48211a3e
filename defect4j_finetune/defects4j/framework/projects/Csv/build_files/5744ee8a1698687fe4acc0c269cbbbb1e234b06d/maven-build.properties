#Generated by <PERSON><PERSON> Ant Plugin - DO NOT EDIT THIS FILE!
#Mon Apr 15 08:26:55 PDT 2019
commons.compiler.compilerVersion=
maven.compile.source=1.5
commons.release.2.name=commons-csv-${commons.release.2.version}
commons.release.desc=
maven.build.srcDir.0=src/main/java
commons.binary.suffix=-bin
commons.manifestfile=/tmp/initialize-revisions.pl_10900_1555341828/18b/target/osgi/MANIFEST.MF
maven.build.timestamp.format=yyyy-MM-dd HH\:mm\:ssZ
commons.project-info.version=2.6
maven.build.resourceDir.1=.
maven.build.resourceDir.0=src/main/resources
project.build.sourceEncoding=UTF-8
sourceReleaseAssemblyDescriptor=source-release
commons.release.3.name=commons-csv-${commons.release.3.version}
maven.build.outputDir=${maven.build.dir}/classes
commons.componentid=csv
commons.site.path=commons-csv
maven.repo.local=${user.home}/.m2/repository
maven.build.finalName=commons-csv-1.0-SNAPSHOT
commons.osgi.private=
maven.build.testDir.0=src/test/java
commons.javadoc.javaee.link=http\://download.oracle.com/javaee/6/api/
commons.osgi.export=org.apache.commons.*;version\=1.0-SNAPSHOT;-noimport\:\=true
commons.release.2.binary.suffix=-bin
maven.reporting.outputDirectory=${maven.build.dir}/site
commons.compiler.javac=
commons.encoding=UTF-8
organization.logo=http\://www.apache.org/images/asf_logo_wide.gif
commons.scmPubUrl=https\://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-csv
sonar.host.url=https\://analysis.apache.org/
commons.rc.version=RC1
commons.cobertura.version=2.5.2
commons.release.name=commons-csv-1.0
minSeverity=info
maven.build.testOutputDir=${maven.build.dir}/test-classes
project.reporting.outputEncoding=UTF-8
maven.build.dir=target
maven.test.reports=${maven.build.dir}/test-reports
gpg.useagent=true
commons.javadoc.version=2.9
commons.surefire.version=2.13
commons.changes.version=2.8
commons.release.version=1.0
commons.jxr.version=2.3
commons.surefire-report.version=2.13
commons.rat.version=0.8
commons.site.cache=/home/<USER>/commons-sites
project.build.outputDirectory=${maven.build.outputDir}
commons.docEncoding=UTF-8
commons.javadoc.java.link=http\://download.oracle.com/javase/6/docs/api/
commons.deployment.protocol=scp
maven.settings.offline=false
distMgmtSnapshotsName=Apache Development Snapshot Repository
maven.settings.interactiveMode=true
maven.compile.target=1.5
commons.release.2.desc=
commons.jira.pid=12313222
project.build.directory=${maven.build.dir}
arguments=
commons.surefire-report.aggregate=false
commons.release.3.binary.suffix=-bin
commons.wagon-ssh.version=2.3
distMgmtSnapshotsUrl=https\://repository.apache.org/content/repositories/snapshots
maven.build.testResourceDir.1=.
maven.build.testResourceDir.0=src/test/resources
commons.osgi.dynamicImport=
commons.compiler.fork=false
commons.jdepend.version=2.0-beta-2
commons.release.3.desc=
implementation.build=${scmBranch}@r${buildNumber}; 2019-04-15 15\:26\:54+0000
commons.site-plugin.version=3.2
commons.osgi.symbolicName=org.apache.commons.csv
commons.surefire.java=
commons.osgi.import=*
checkstyle.header.file=/tmp/initialize-revisions.pl_10900_1555341828/18b/LICENSE-header.txt
commons.jira.id=CSV
commons.clirr.version=2.5
commons.scmPubCheckoutDirectory=/home/<USER>/commons-sites/commons-csv
