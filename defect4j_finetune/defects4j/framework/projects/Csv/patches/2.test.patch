diff --git a/src/test/java/org/apache/commons/csv/CSVRecordTest.java b/src/test/java/org/apache/commons/csv/CSVRecordTest.java
deleted file mode 100644
index bcb33db..0000000
--- a/src/test/java/org/apache/commons/csv/CSVRecordTest.java
+++ /dev/null
@@ -1,105 +0,0 @@
-/*
- * Licensed to the Apache Software Foundation (ASF) under one or more
- * contributor license agreements.  See the NOTICE file distributed with
- * this work for additional information regarding copyright ownership.
- * The ASF licenses this file to You under the Apache License, Version 2.0
- * (the "License"); you may not use this file except in compliance with
- * the License.  You may obtain a copy of the License at
- *
- *      http://www.apache.org/licenses/LICENSE-2.0
- *
- * Unless required by applicable law or agreed to in writing, software
- * distributed under the License is distributed on an "AS IS" BASIS,
- * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- * See the License for the specific language governing permissions and
- * limitations under the License.
- */
-package org.apache.commons.csv;
-
-import static org.junit.Assert.assertEquals;
-import static org.junit.Assert.assertFalse;
-import static org.junit.Assert.assertTrue;
-
-import java.util.HashMap;
-import java.util.Iterator;
-import java.util.Map;
-
-import org.junit.Before;
-import org.junit.Test;
-
-public class CSVRecordTest {
-
-    private String[] values;
-    private CSVRecord record, recordWithHeader;
-    private Map<String, Integer> header;
-
-    @Before
-    public void setUp() throws Exception {
-        values = new String[] { "first", "second", "third" };
-        record = new CSVRecord(values, null, null, 0);
-        header = new HashMap<String, Integer>();
-        header.put("first", Integer.valueOf(0));
-        header.put("second", Integer.valueOf(1));
-        header.put("third", Integer.valueOf(2));
-        recordWithHeader = new CSVRecord(values, header, null, 0);
-    }
-
-    @Test
-    public void testGetInt() {
-        assertEquals(values[0], record.get(0));
-        assertEquals(values[1], record.get(1));
-        assertEquals(values[2], record.get(2));
-    }
-
-    @Test
-    public void testGetString() {
-        assertEquals(values[0], recordWithHeader.get("first"));
-        assertEquals(values[1], recordWithHeader.get("second"));
-        assertEquals(values[2], recordWithHeader.get("third"));
-    }
-
-    @Test(expected = IllegalStateException.class)
-    public void testGetStringNoHeader() {
-        record.get("first");
-    }
-
-    @Test(expected = IllegalArgumentException.class)
-    public void testGetStringInconsistentRecord() {
-        header.put("fourth", Integer.valueOf(4));
-        recordWithHeader.get("fourth");
-    }
-
-    @Test
-    public void testIsConsistent() {
-        assertTrue(record.isConsistent());
-        assertTrue(recordWithHeader.isConsistent());
-
-        header.put("fourth", Integer.valueOf(4));
-        assertFalse(recordWithHeader.isConsistent());
-    }
-
-    @Test
-    public void testIsMapped() {
-        assertFalse(record.isMapped("first"));
-        assertTrue(recordWithHeader.isMapped("first"));
-        assertFalse(recordWithHeader.isMapped("fourth"));
-    }
-
-    @Test
-    public void testIsSet() {
-        assertFalse(record.isSet("first"));
-        assertTrue(recordWithHeader.isSet("first"));
-        assertFalse(recordWithHeader.isSet("fourth"));
-    }
-
-    @Test
-    public void testIterator() {
-        int i = 0;
-        for (Iterator<String> itr = record.iterator(); itr.hasNext();) {
-            String value = itr.next();
-            assertEquals(values[i], value);
-            i++;
-        }
-    }
-
-}
