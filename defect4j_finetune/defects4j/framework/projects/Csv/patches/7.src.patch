diff --git a/src/main/java/org/apache/commons/csv/CSVParser.java b/src/main/java/org/apache/commons/csv/CSVParser.java
index 315a11c..a6a6d49 100644
--- a/src/main/java/org/apache/commons/csv/CSVParser.java
+++ b/src/main/java/org/apache/commons/csv/CSVParser.java
@@ -29,7 +29,6 @@ import java.io.StringReader;
 import java.net.URL;
 import java.nio.charset.Charset;
 import java.util.ArrayList;
-import java.util.Arrays;
 import java.util.Collection;
 import java.util.Iterator;
 import java.util.LinkedHashMap;
@@ -369,9 +368,6 @@ public final class CSVParser implements Iterable<CSVRecord>, Closeable {
             // build the name to index mappings
             if (header != null) {
                 for (int i = 0; i < header.length; i++) {
-                    if (hdrMap.containsKey(header[i])) {
-                        throw new IllegalStateException("The header contains duplicate names: " + Arrays.toString(header));
-                    }
                     hdrMap.put(header[i], Integer.valueOf(i));
                 }
             }
