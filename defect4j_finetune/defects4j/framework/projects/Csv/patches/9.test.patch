diff --git a/src/test/java/org/apache/commons/csv/CSVRecordTest.java b/src/test/java/org/apache/commons/csv/CSVRecordTest.java
index 1ef6e28..0e40ae4 100644
--- a/src/test/java/org/apache/commons/csv/CSVRecordTest.java
+++ b/src/test/java/org/apache/commons/csv/CSVRecordTest.java
@@ -18,7 +18,6 @@ package org.apache.commons.csv;
 
 import static org.junit.Assert.assertEquals;
 import static org.junit.Assert.assertFalse;
-import static org.junit.Assert.assertNotNull;
 import static org.junit.Assert.assertNull;
 import static org.junit.Assert.assertTrue;
 
@@ -167,15 +166,6 @@ public class CSVRecordTest {
        final CSVRecord shortRec = parser.iterator().next();
        shortRec.toMap();
     }
-
-    @Test
-    public void testToMapWithNoHeader() throws Exception {
-       final CSVParser parser =  CSVParser.parse("a,b", CSVFormat.newFormat(','));
-       final CSVRecord shortRec = parser.iterator().next();
-       Map<String, String> map = shortRec.toMap();
-       assertNotNull("Map is not null.", map);
-       assertTrue("Map is empty.", map.isEmpty());
-    }
     
     private void validateMap(final Map<String, String> map, final boolean allowsNulls) {
         assertTrue(map.containsKey("first"));
