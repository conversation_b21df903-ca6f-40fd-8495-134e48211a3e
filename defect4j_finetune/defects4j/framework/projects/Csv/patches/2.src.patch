diff --git a/src/main/java/org/apache/commons/csv/CSVRecord.java b/src/main/java/org/apache/commons/csv/CSVRecord.java
index fd633c2..dc52017 100644
--- a/src/main/java/org/apache/commons/csv/CSVRecord.java
+++ b/src/main/java/org/apache/commons/csv/CSVRecord.java
@@ -82,14 +82,7 @@ public class CSVRecord implements Serializable, Iterable<String> {
                     "No header mapping was specified, the record values can't be accessed by name");
         }
         final Integer index = mapping.get(name);
-        try {
             return index != null ? values[index.intValue()] : null;
-        } catch (ArrayIndexOutOfBoundsException e) {
-            throw new IllegalArgumentException(
-                    String.format(
-                            "Index for header '%s' is %d but CSVRecord only has %d values!",
-                            name, index.intValue(), values.length));
-        }
     }
 
     /**
