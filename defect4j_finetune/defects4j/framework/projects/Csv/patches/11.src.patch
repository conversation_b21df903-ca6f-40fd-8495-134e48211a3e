diff --git a/src/main/java/org/apache/commons/csv/CSVParser.java b/src/main/java/org/apache/commons/csv/CSVParser.java
index b7ca3fa..b6867a4 100644
--- a/src/main/java/org/apache/commons/csv/CSVParser.java
+++ b/src/main/java/org/apache/commons/csv/CSVParser.java
@@ -381,7 +381,7 @@ public final class CSVParser implements Iterable<CSVRecord>, Closeable {
                 for (int i = 0; i < headerRecord.length; i++) {
                     final String header = headerRecord[i];
                     final boolean containsHeader = hdrMap.containsKey(header);
-                    final boolean emptyHeader = header == null || header.trim().isEmpty();
+                    final boolean emptyHeader = header.trim().isEmpty();
                     if (containsHeader && (!emptyHeader || (emptyHeader && !this.format.getIgnoreEmptyHeaders()))) {
                         throw new IllegalArgumentException("The header contains a duplicate name: \"" + header +
                                 "\" in " + Arrays.toString(headerRecord));
