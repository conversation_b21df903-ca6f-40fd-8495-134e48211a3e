/*
 * This file was automatically generated by EvoSuite
 * Fri Mar 31 20:20:01 GMT 2023
 */

package com.google.gson.internal;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.lang.reflect.GenericArrayType;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.TypeVariable;
import java.lang.reflect.WildcardType;
import java.util.Collection;
import java.util.NoSuchElementException;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.PrivateAccess;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class $Gson$Types_ESTest extends $Gson$Types_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      Class class1 = (Class).Gson.Types.getSupertype(wildcardType0, class0, class0);
      assertNotNull(class1);
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertEquals("class java.lang.Object", class1.toString());
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertFalse(class1.isAnnotation());
      assertFalse(class1.isPrimitive());
      
      boolean boolean0 = .Gson.Types.equals((Type) class1, (Type) genericArrayType0);
      assertFalse(boolean0);
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertEquals("class java.lang.Object", class1.toString());
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertFalse(class1.isAnnotation());
      assertFalse(class1.isPrimitive());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      try { 
        PrivateAccess.callDefaultConstructorOfTheClassUnderTest();
        fail("Expecting exception: UnsupportedOperationException");
      
      } catch(UnsupportedOperationException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.gson.internal.$Gson$Types", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      WildcardType wildcardType1 = .Gson.Types.supertypeOf(wildcardType0);
      assertNotNull(wildcardType1);
      assertTrue(wildcardType1.equals((Object)wildcardType0));
      assertNotSame(wildcardType0, wildcardType1);
      assertNotSame(wildcardType1, wildcardType0);
      
      boolean boolean0 = .Gson.Types.equal(wildcardType0, wildcardType1);
      assertTrue(wildcardType0.equals((Object)wildcardType1));
      assertTrue(wildcardType1.equals((Object)wildcardType0));
      assertNotSame(wildcardType0, wildcardType1);
      assertNotSame(wildcardType1, wildcardType0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf(class0);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.subtypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      Class<?> class1 = .Gson.Types.getRawType(wildcardType0);
      assertNotNull(class1);
      assertFalse(class1.isAnnotation());
      assertEquals("class [Ljava.lang.reflect.WildcardType;", class1.toString());
      assertFalse(class1.isInterface());
      assertEquals(1041, class1.getModifiers());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertTrue(class1.isArray());
      
      Type[] typeArray0 = new Type[0];
      ParameterizedType parameterizedType0 = .Gson.Types.newParameterizedTypeWithOwner(class1, genericArrayType0, typeArray0);
      assertNotNull(parameterizedType0);
      assertEquals(0, typeArray0.length);
      assertFalse(class1.isAnnotation());
      assertEquals("class [Ljava.lang.reflect.WildcardType;", class1.toString());
      assertFalse(class1.isInterface());
      assertEquals(1041, class1.getModifiers());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertTrue(class1.isArray());
      
      Type type0 = .Gson.Types.canonicalize(parameterizedType0);
      assertNotNull(type0);
      assertEquals(0, typeArray0.length);
      assertFalse(class1.isAnnotation());
      assertEquals("class [Ljava.lang.reflect.WildcardType;", class1.toString());
      assertFalse(class1.isInterface());
      assertEquals(1041, class1.getModifiers());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertTrue(class1.isArray());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      Class class1 = (Class).Gson.Types.resolve((Type) null, class0, class0);
      assertNotNull(class1);
      assertTrue(class1.isInterface());
      assertEquals(1537, class1.getModifiers());
      assertEquals("interface java.lang.reflect.WildcardType", class1.toString());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isArray());
      assertFalse(class1.isAnnotation());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      
      Type[] typeArray0 = new Type[7];
      typeArray0[0] = (Type) class0;
      typeArray0[1] = (Type) class1;
      typeArray0[2] = (Type) class0;
      typeArray0[3] = (Type) class1;
      typeArray0[4] = (Type) class1;
      typeArray0[5] = (Type) class0;
      typeArray0[6] = (Type) class1;
      ParameterizedType parameterizedType0 = .Gson.Types.newParameterizedTypeWithOwner(class1, class0, typeArray0);
      assertNotNull(parameterizedType0);
      assertEquals(7, typeArray0.length);
      assertTrue(class1.isInterface());
      assertEquals(1537, class1.getModifiers());
      assertEquals("interface java.lang.reflect.WildcardType", class1.toString());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isArray());
      assertFalse(class1.isAnnotation());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf(parameterizedType0);
      assertNotNull(genericArrayType0);
      assertEquals(7, typeArray0.length);
      assertTrue(class1.isInterface());
      assertEquals(1537, class1.getModifiers());
      assertEquals("interface java.lang.reflect.WildcardType", class1.toString());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isArray());
      assertFalse(class1.isAnnotation());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      
      Class<?> class2 = .Gson.Types.getRawType(genericArrayType0);
      assertNotNull(class2);
      assertEquals(7, typeArray0.length);
      assertTrue(class1.isInterface());
      assertEquals(1537, class1.getModifiers());
      assertEquals("interface java.lang.reflect.WildcardType", class1.toString());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isArray());
      assertFalse(class1.isAnnotation());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertFalse(class2.isInterface());
      assertFalse(class2.isAnnotation());
      assertFalse(class2.isPrimitive());
      assertEquals(1041, class2.getModifiers());
      assertEquals("class [Ljava.lang.reflect.WildcardType;", class2.toString());
      assertTrue(class2.isArray());
      assertFalse(class2.isEnum());
      assertFalse(class2.isSynthetic());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      // Undeclared exception!
      try { 
        .Gson.Types.getRawType(genericArrayType0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Expected a Class, ParameterizedType, or GenericArrayType, but <null> is of type null
         //
         verifyException("com.google.gson.internal.$Gson$Types", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Type type0 = mock(Type.class, new ViolatedAssumptionAnswer());
      doReturn("[e;[<77P,?8)\"3").when(type0).toString();
      // Undeclared exception!
      try { 
        .Gson.Types.getRawType(type0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Expected a Class, ParameterizedType, or GenericArrayType, but <[e;[<77P,?8)\"3> is of type org.evosuite.shaded.org.mockito.codegen.Type$MockitoMock$1680078041
         //
         verifyException("com.google.gson.internal.$Gson$Types", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      WildcardType wildcardType1 = .Gson.Types.subtypeOf(class0);
      assertNotNull(wildcardType1);
      assertFalse(wildcardType1.equals((Object)wildcardType0));
      assertNotSame(wildcardType1, wildcardType0);
      
      boolean boolean0 = .Gson.Types.equal(wildcardType0, wildcardType1);
      assertFalse(wildcardType0.equals((Object)wildcardType1));
      assertFalse(wildcardType1.equals((Object)wildcardType0));
      assertNotSame(wildcardType0, wildcardType1);
      assertNotSame(wildcardType1, wildcardType0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      boolean boolean0 = .Gson.Types.equal((Object) null, wildcardType0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      Type type0 = .Gson.Types.getSupertype((Type) null, class0, class0);
      assertNull(type0);
      
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      Type[] typeArray0 = new Type[0];
      ParameterizedType parameterizedType0 = .Gson.Types.newParameterizedTypeWithOwner(genericArrayType0, genericArrayType0, typeArray0);
      assertNotNull(parameterizedType0);
      assertEquals(0, typeArray0.length);
      
      boolean boolean0 = .Gson.Types.equals((Type) parameterizedType0, type0);
      assertEquals(0, typeArray0.length);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      Type type0 = .Gson.Types.getSupertype((Type) null, class0, class0);
      assertNull(type0);
      
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      Type[] typeArray0 = new Type[0];
      ParameterizedType parameterizedType0 = .Gson.Types.newParameterizedTypeWithOwner(type0, genericArrayType0, typeArray0);
      assertNotNull(parameterizedType0);
      assertEquals(0, typeArray0.length);
      
      Type type1 = .Gson.Types.canonicalize(parameterizedType0);
      assertNotNull(type1);
      assertEquals(0, typeArray0.length);
      
      boolean boolean0 = .Gson.Types.equals((Type) parameterizedType0, type1);
      assertEquals(0, typeArray0.length);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      Class<?> class1 = .Gson.Types.getRawType(wildcardType0);
      assertNotNull(class1);
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertEquals("class java.lang.Object", class1.toString());
      assertFalse(class1.isPrimitive());
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isAnnotation());
      
      Type[] typeArray0 = new Type[0];
      ParameterizedType parameterizedType0 = .Gson.Types.newParameterizedTypeWithOwner((Type) null, (Type) null, typeArray0);
      assertNotNull(parameterizedType0);
      assertEquals(0, typeArray0.length);
      
      Type[] typeArray1 = new Type[3];
      assertFalse(typeArray1.equals((Object)typeArray0));
      
      typeArray1[0] = (Type) class0;
      typeArray1[1] = (Type) wildcardType0;
      typeArray1[2] = (Type) wildcardType0;
      ParameterizedType parameterizedType1 = .Gson.Types.newParameterizedTypeWithOwner(wildcardType0, class1, typeArray1);
      assertNotNull(parameterizedType1);
      assertFalse(typeArray1.equals((Object)typeArray0));
      assertFalse(parameterizedType1.equals((Object)parameterizedType0));
      assertEquals(3, typeArray1.length);
      assertNotSame(typeArray1, typeArray0);
      assertNotSame(parameterizedType1, parameterizedType0);
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertEquals("class java.lang.Object", class1.toString());
      assertFalse(class1.isPrimitive());
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isAnnotation());
      
      boolean boolean0 = .Gson.Types.equals((Type) parameterizedType1, (Type) parameterizedType0);
      assertFalse(typeArray0.equals((Object)typeArray1));
      assertFalse(parameterizedType0.equals((Object)parameterizedType1));
      assertFalse(typeArray1.equals((Object)typeArray0));
      assertFalse(parameterizedType1.equals((Object)parameterizedType0));
      assertEquals(0, typeArray0.length);
      assertEquals(3, typeArray1.length);
      assertNotSame(typeArray0, typeArray1);
      assertNotSame(parameterizedType0, parameterizedType1);
      assertNotSame(typeArray1, typeArray0);
      assertNotSame(parameterizedType1, parameterizedType0);
      assertFalse(boolean0);
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertEquals("class java.lang.Object", class1.toString());
      assertFalse(class1.isPrimitive());
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isAnnotation());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      boolean boolean0 = .Gson.Types.equals((Type) genericArrayType0, (Type) wildcardType0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      Class<?> class0 = .Gson.Types.getRawType(wildcardType0);
      assertNotNull(class0);
      assertFalse(class0.isPrimitive());
      assertFalse(class0.isInterface());
      assertEquals("class java.lang.Object", class0.toString());
      assertFalse(class0.isEnum());
      assertFalse(class0.isSynthetic());
      assertEquals(1, class0.getModifiers());
      assertFalse(class0.isArray());
      assertFalse(class0.isAnnotation());
      
      GenericArrayType genericArrayType1 = .Gson.Types.arrayOf(class0);
      assertNotNull(genericArrayType1);
      assertFalse(genericArrayType1.equals((Object)genericArrayType0));
      assertNotSame(genericArrayType0, genericArrayType1);
      assertNotSame(genericArrayType1, genericArrayType0);
      assertFalse(class0.isPrimitive());
      assertFalse(class0.isInterface());
      assertEquals("class java.lang.Object", class0.toString());
      assertFalse(class0.isEnum());
      assertFalse(class0.isSynthetic());
      assertEquals(1, class0.getModifiers());
      assertFalse(class0.isArray());
      assertFalse(class0.isAnnotation());
      
      WildcardType wildcardType1 = .Gson.Types.supertypeOf(genericArrayType1);
      assertNotNull(wildcardType1);
      assertFalse(genericArrayType0.equals((Object)genericArrayType1));
      assertFalse(genericArrayType1.equals((Object)genericArrayType0));
      assertFalse(wildcardType1.equals((Object)wildcardType0));
      assertNotSame(genericArrayType0, genericArrayType1);
      assertNotSame(wildcardType0, wildcardType1);
      assertNotSame(genericArrayType1, genericArrayType0);
      assertNotSame(wildcardType1, wildcardType0);
      assertFalse(class0.isPrimitive());
      assertFalse(class0.isInterface());
      assertEquals("class java.lang.Object", class0.toString());
      assertFalse(class0.isEnum());
      assertFalse(class0.isSynthetic());
      assertEquals(1, class0.getModifiers());
      assertFalse(class0.isArray());
      assertFalse(class0.isAnnotation());
      
      boolean boolean0 = .Gson.Types.equal(wildcardType0, wildcardType1);
      assertFalse(genericArrayType0.equals((Object)genericArrayType1));
      assertFalse(wildcardType0.equals((Object)wildcardType1));
      assertFalse(genericArrayType1.equals((Object)genericArrayType0));
      assertFalse(wildcardType1.equals((Object)wildcardType0));
      assertNotSame(genericArrayType0, genericArrayType1);
      assertNotSame(wildcardType0, wildcardType1);
      assertNotSame(genericArrayType1, genericArrayType0);
      assertNotSame(wildcardType1, wildcardType0);
      assertFalse(boolean0);
      assertFalse(class0.isPrimitive());
      assertFalse(class0.isInterface());
      assertEquals("class java.lang.Object", class0.toString());
      assertFalse(class0.isEnum());
      assertFalse(class0.isSynthetic());
      assertEquals(1, class0.getModifiers());
      assertFalse(class0.isArray());
      assertFalse(class0.isAnnotation());
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      Class class1 = (Class).Gson.Types.getSupertype(wildcardType0, class0, class0);
      assertNotNull(class1);
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertFalse(class1.isAnnotation());
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertEquals("class java.lang.Object", class1.toString());
      assertFalse(class1.isPrimitive());
      
      boolean boolean0 = .Gson.Types.equals((Type) wildcardType0, (Type) class1);
      assertFalse(boolean0);
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertFalse(class1.isAnnotation());
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertEquals("class java.lang.Object", class1.toString());
      assertFalse(class1.isPrimitive());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      int int0 = .Gson.Types.hashCodeOrZero((Object) null);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      int int0 = .Gson.Types.hashCodeOrZero(wildcardType0);
      assertEquals(3, int0);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      // Undeclared exception!
      try { 
        .Gson.Types.typeToString((Type) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.evosuite.runtime.System", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      Class<?> class0 = .Gson.Types.getRawType(wildcardType0);
      assertNotNull(class0);
      assertEquals(1, class0.getModifiers());
      assertFalse(class0.isAnnotation());
      assertEquals("class java.lang.Object", class0.toString());
      assertFalse(class0.isPrimitive());
      assertFalse(class0.isInterface());
      assertFalse(class0.isArray());
      assertFalse(class0.isEnum());
      assertFalse(class0.isSynthetic());
      
      String string0 = .Gson.Types.typeToString(class0);
      assertNotNull(string0);
      assertEquals("java.lang.Object", string0);
      assertEquals(1, class0.getModifiers());
      assertFalse(class0.isAnnotation());
      assertEquals("class java.lang.Object", class0.toString());
      assertFalse(class0.isPrimitive());
      assertFalse(class0.isInterface());
      assertFalse(class0.isArray());
      assertFalse(class0.isEnum());
      assertFalse(class0.isSynthetic());
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      Class class1 = (Class).Gson.Types.getSupertype(wildcardType0, class0, class0);
      assertNotNull(class1);
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isAnnotation());
      assertEquals("class java.lang.Object", class1.toString());
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertEquals(1, class1.getModifiers());
      
      Class class2 = (Class).Gson.Types.getGenericSupertype(wildcardType0, class1, class0);
      assertNotNull(class2);
      assertFalse(class2.equals((Object)class1));
      assertNotSame(class1, class2);
      assertNotSame(class2, class1);
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isAnnotation());
      assertEquals("class java.lang.Object", class1.toString());
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertEquals(1, class1.getModifiers());
      assertEquals("interface java.lang.reflect.WildcardType", class2.toString());
      assertEquals(1537, class2.getModifiers());
      assertFalse(class2.isEnum());
      assertFalse(class2.isSynthetic());
      assertFalse(class2.isArray());
      assertTrue(class2.isInterface());
      assertFalse(class2.isPrimitive());
      assertFalse(class2.isAnnotation());
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      Class<?> class1 = .Gson.Types.getRawType(wildcardType0);
      assertNotNull(class1);
      assertFalse(class1.isAnnotation());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertEquals("class java.lang.Object", class1.toString());
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      
      Class class2 = (Class).Gson.Types.resolve((Type) null, class1, class1);
      assertNotNull(class2);
      assertFalse(class1.isAnnotation());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertEquals("class java.lang.Object", class1.toString());
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertFalse(class2.isEnum());
      assertFalse(class2.isSynthetic());
      assertEquals(1, class2.getModifiers());
      assertEquals("class java.lang.Object", class2.toString());
      assertFalse(class2.isInterface());
      assertFalse(class2.isArray());
      assertFalse(class2.isPrimitive());
      assertFalse(class2.isAnnotation());
      
      Class class3 = (Class).Gson.Types.getSupertype(wildcardType0, class0, class2);
      assertNotNull(class3);
      assertSame(class2, class3);
      assertSame(class3, class2);
      assertFalse(class1.isAnnotation());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertEquals("class java.lang.Object", class1.toString());
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertFalse(class2.isEnum());
      assertFalse(class2.isSynthetic());
      assertEquals(1, class2.getModifiers());
      assertEquals("class java.lang.Object", class2.toString());
      assertFalse(class2.isInterface());
      assertFalse(class2.isArray());
      assertFalse(class2.isPrimitive());
      assertFalse(class2.isAnnotation());
      assertFalse(class3.isAnnotation());
      assertFalse(class3.isInterface());
      assertFalse(class3.isArray());
      assertFalse(class3.isEnum());
      assertEquals("class java.lang.Object", class3.toString());
      assertFalse(class3.isSynthetic());
      assertFalse(class3.isPrimitive());
      assertEquals(1, class3.getModifiers());
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      Type type0 = .Gson.Types.getArrayComponentType(class0);
      assertNull(type0);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      Type type0 = .Gson.Types.getArrayComponentType(genericArrayType0);
      assertNull(type0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      // Undeclared exception!
      try { 
        .Gson.Types.getMapKeyAndValueTypes((Type) null, class0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.gson.internal.$Gson$Preconditions", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      Class<?> class1 = .Gson.Types.getRawType(wildcardType0);
      assertNotNull(class1);
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isSynthetic());
      assertFalse(class1.isEnum());
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertEquals("class java.lang.Object", class1.toString());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isAnnotation());
      
      GenericArrayType genericArrayType1 = .Gson.Types.arrayOf(class1);
      assertNotNull(genericArrayType1);
      assertFalse(genericArrayType1.equals((Object)genericArrayType0));
      assertNotSame(genericArrayType0, genericArrayType1);
      assertNotSame(genericArrayType1, genericArrayType0);
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isSynthetic());
      assertFalse(class1.isEnum());
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertEquals("class java.lang.Object", class1.toString());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isAnnotation());
      
      Class<?> class2 = .Gson.Types.getRawType(genericArrayType1);
      assertNotNull(class2);
      assertFalse(genericArrayType0.equals((Object)genericArrayType1));
      assertFalse(genericArrayType1.equals((Object)genericArrayType0));
      assertFalse(class2.equals((Object)class1));
      assertNotSame(genericArrayType0, genericArrayType1);
      assertNotSame(class1, class2);
      assertNotSame(genericArrayType1, genericArrayType0);
      assertNotSame(class2, class1);
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isSynthetic());
      assertFalse(class1.isEnum());
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertEquals("class java.lang.Object", class1.toString());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isAnnotation());
      assertFalse(class2.isAnnotation());
      assertFalse(class2.isPrimitive());
      assertEquals("class [Ljava.lang.Object;", class2.toString());
      assertFalse(class2.isInterface());
      assertEquals(1041, class2.getModifiers());
      assertTrue(class2.isArray());
      assertFalse(class2.isEnum());
      assertFalse(class2.isSynthetic());
      
      Class class3 = (Class).Gson.Types.resolve((Type) null, class0, class2);
      assertNotNull(class3);
      assertFalse(genericArrayType0.equals((Object)genericArrayType1));
      assertFalse(class1.equals((Object)class2));
      assertFalse(genericArrayType1.equals((Object)genericArrayType0));
      assertFalse(class2.equals((Object)class1));
      assertNotSame(genericArrayType0, genericArrayType1);
      assertNotSame(class1, class2);
      assertNotSame(genericArrayType1, genericArrayType0);
      assertNotSame(class2, class1);
      assertEquals(1, class1.getModifiers());
      assertFalse(class1.isSynthetic());
      assertFalse(class1.isEnum());
      assertFalse(class1.isInterface());
      assertFalse(class1.isArray());
      assertEquals("class java.lang.Object", class1.toString());
      assertFalse(class1.isPrimitive());
      assertFalse(class1.isAnnotation());
      assertFalse(class2.isAnnotation());
      assertFalse(class2.isPrimitive());
      assertEquals("class [Ljava.lang.Object;", class2.toString());
      assertFalse(class2.isInterface());
      assertEquals(1041, class2.getModifiers());
      assertTrue(class2.isArray());
      assertFalse(class2.isEnum());
      assertFalse(class2.isSynthetic());
      assertFalse(class3.isInterface());
      assertEquals("class [Ljava.lang.Object;", class3.toString());
      assertFalse(class3.isAnnotation());
      assertFalse(class3.isPrimitive());
      assertEquals(1041, class3.getModifiers());
      assertTrue(class3.isArray());
      assertFalse(class3.isEnum());
      assertFalse(class3.isSynthetic());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      Type[] typeArray0 = new Type[0];
      ParameterizedType parameterizedType0 = .Gson.Types.newParameterizedTypeWithOwner(wildcardType0, genericArrayType0, typeArray0);
      assertNotNull(parameterizedType0);
      assertEquals(0, typeArray0.length);
      
      Type type0 = .Gson.Types.getSupertype(parameterizedType0, class0, class0);
      assertNotNull(type0);
      assertEquals(0, typeArray0.length);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Class<WildcardType> class0 = WildcardType.class;
      Class class1 = (Class).Gson.Types.canonicalize(class0);
      assertNotNull(class1);
      assertFalse(class1.isArray());
      assertEquals("interface java.lang.reflect.WildcardType", class1.toString());
      assertFalse(class1.isPrimitive());
      assertEquals(1537, class1.getModifiers());
      assertFalse(class1.isSynthetic());
      assertTrue(class1.isInterface());
      assertFalse(class1.isEnum());
      assertFalse(class1.isAnnotation());
      
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf(class1);
      assertNotNull(genericArrayType0);
      assertFalse(class1.isArray());
      assertEquals("interface java.lang.reflect.WildcardType", class1.toString());
      assertFalse(class1.isPrimitive());
      assertEquals(1537, class1.getModifiers());
      assertFalse(class1.isSynthetic());
      assertTrue(class1.isInterface());
      assertFalse(class1.isEnum());
      assertFalse(class1.isAnnotation());
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      assertFalse(class1.isArray());
      assertEquals("interface java.lang.reflect.WildcardType", class1.toString());
      assertFalse(class1.isPrimitive());
      assertEquals(1537, class1.getModifiers());
      assertFalse(class1.isSynthetic());
      assertTrue(class1.isInterface());
      assertFalse(class1.isEnum());
      assertFalse(class1.isAnnotation());
      
      Type[] typeArray0 = new Type[0];
      WildcardType wildcardType1 = .Gson.Types.subtypeOf(wildcardType0);
      assertNotNull(wildcardType1);
      assertFalse(wildcardType1.equals((Object)wildcardType0));
      assertNotSame(wildcardType0, wildcardType1);
      assertNotSame(wildcardType1, wildcardType0);
      assertFalse(class1.isArray());
      assertEquals("interface java.lang.reflect.WildcardType", class1.toString());
      assertFalse(class1.isPrimitive());
      assertEquals(1537, class1.getModifiers());
      assertFalse(class1.isSynthetic());
      assertTrue(class1.isInterface());
      assertFalse(class1.isEnum());
      assertFalse(class1.isAnnotation());
      
      ParameterizedType parameterizedType0 = .Gson.Types.newParameterizedTypeWithOwner(wildcardType1, class0, typeArray0);
      assertNotNull(parameterizedType0);
      assertFalse(wildcardType0.equals((Object)wildcardType1));
      assertFalse(wildcardType1.equals((Object)wildcardType0));
      assertEquals(0, typeArray0.length);
      assertNotSame(wildcardType0, wildcardType1);
      assertNotSame(wildcardType1, wildcardType0);
      assertFalse(class1.isArray());
      assertEquals("interface java.lang.reflect.WildcardType", class1.toString());
      assertFalse(class1.isPrimitive());
      assertEquals(1537, class1.getModifiers());
      assertFalse(class1.isSynthetic());
      assertTrue(class1.isInterface());
      assertFalse(class1.isEnum());
      assertFalse(class1.isAnnotation());
      
      Type type0 = .Gson.Types.getSupertype(parameterizedType0, class0, class0);
      assertNotNull(type0);
      assertFalse(wildcardType0.equals((Object)wildcardType1));
      assertFalse(wildcardType1.equals((Object)wildcardType0));
      assertEquals(0, typeArray0.length);
      assertNotSame(wildcardType0, wildcardType1);
      assertNotSame(wildcardType1, wildcardType0);
      assertFalse(class1.isArray());
      assertEquals("interface java.lang.reflect.WildcardType", class1.toString());
      assertFalse(class1.isPrimitive());
      assertEquals(1537, class1.getModifiers());
      assertFalse(class1.isSynthetic());
      assertTrue(class1.isInterface());
      assertFalse(class1.isEnum());
      assertFalse(class1.isAnnotation());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      GenericArrayType genericArrayType0 = .Gson.Types.arrayOf((Type) null);
      assertNotNull(genericArrayType0);
      
      WildcardType wildcardType0 = .Gson.Types.supertypeOf(genericArrayType0);
      assertNotNull(wildcardType0);
      
      Type[] typeArray0 = new Type[3];
      try { 
        PrivateAccess.callMethod((Class<.Gson.Types>) .Gson.Types.class, (.Gson.Types) null, "indexOf", (Object) typeArray0, (Class<?>) Object[].class, (Object) wildcardType0, (Class<?>) Object.class);
        fail("Expecting exception: NoSuchElementException");
      
      } catch(NoSuchElementException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.gson.internal.$Gson$Types", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Type type0 = mock(Type.class, new ViolatedAssumptionAnswer());
      Class<WildcardType> class0 = WildcardType.class;
      Collection<TypeVariable> collection0 = (Collection<TypeVariable>) mock(Collection.class, new ViolatedAssumptionAnswer());
      Class class1 = (Class)PrivateAccess.callMethod((Class<.Gson.Types>) .Gson.Types.class, (.Gson.Types) null, "resolve", (Object) type0, (Class<?>) Type.class, (Object) class0, (Class<?>) Class.class, (Object) class0, (Class<?>) Type.class, (Object) collection0, (Class<?>) Collection.class);
      assertNotNull(class1);
      assertFalse(class1.isArray());
      assertFalse(class1.isAnnotation());
      assertTrue(class1.isInterface());
      assertEquals(1537, class1.getModifiers());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertEquals("interface java.lang.reflect.WildcardType", class1.toString());
      assertFalse(class1.isPrimitive());
      
      Object[] objectArray0 = new Object[2];
      objectArray0[0] = (Object) class0;
      Integer integer0 = (Integer)PrivateAccess.callMethod((Class<.Gson.Types>) .Gson.Types.class, (.Gson.Types) null, "indexOf", (Object) objectArray0, (Class<?>) Object[].class, (Object) class1, (Class<?>) Object.class);
      assertNotNull(integer0);
      assertEquals(2, objectArray0.length);
      assertEquals(0, (int)integer0);
      assertFalse(class1.isArray());
      assertFalse(class1.isAnnotation());
      assertTrue(class1.isInterface());
      assertEquals(1537, class1.getModifiers());
      assertFalse(class1.isEnum());
      assertFalse(class1.isSynthetic());
      assertEquals("interface java.lang.reflect.WildcardType", class1.toString());
      assertFalse(class1.isPrimitive());
  }
}
