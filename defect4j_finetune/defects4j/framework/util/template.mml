list_aor={+,-,*,/,%};
list_lor={&,|,^};
list_sor={<<,>>,>>>};
list_oru={+,-,~};

// Replacements for the AOR operator
BIN(+)->list_aor;
BIN(-)->list_aor;
BIN(*)->list_aor;
BIN(/)->list_aor;
BIN(%)->list_aor;

// Replacements for the SOR operator
BIN(>>)->list_sor;
BIN(<<)->list_sor;
BIN(>>>)->list_sor;

// Replacements for the LOR operator
BIN(&)->list_lor;
BIN(|)->list_lor;
BIN(^)->list_lor;

// Replacements for the ORU operator
UNR(+)->list_oru;
UNR(-)->list_oru;
UNR(~)->list_oru;

// Replacements the for ROR operator
BIN(>)->{>=,!=,FALSE};
BIN(<)->{<=,!=,FALSE};
BIN(>=)->{>,==,TRUE};
BIN(<=)->{<,==,TRUE};
BIN(==)->{<=,>=,FALSE,LHS,RHS};
BIN(!=)->{<,>,TRUE,LHS,RHS};

// Replacements for COR
BIN(&&)->{==,LHS,RHS,FALSE};
BIN(||)->{!=,LHS,RHS,TRUE};

// Deletions for the STD operator (type of statement that should be deleted)
DEL(CALL);
DEL(INC);
DEL(DEC);
DEL(ASSIGN);
// Don't delete return, continue, and break statements by default
//DEL(RETURN);
//DEL(CONT);
//DEL(BREAK);

// Literal types for the LVR operator
LIT(BOOLEAN);
LIT(NUMBER);
// Don't mutate String literals by default
//LIT(STRING);
