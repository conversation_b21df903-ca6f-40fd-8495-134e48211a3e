import numpy as np
import pandas as pd
from collections import Counter
import re
import nltk
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from rouge_score import rouge_scorer
import difflib
from typing import List, Dict, Tuple, Any
import ast
try:
    import javalang
    JAVALANG_AVAILABLE = True
except ImportError:
    JAVALANG_AVAILABLE = False
    print("Warning: javalang not available. Compilation checking will be disabled.")

import warnings

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    try:
        nltk.download('punkt')
    except Exception as e:
        print(f"Warning: Could not download NLTK punkt tokenizer: {e}")
        print("BLEU scores may be less accurate, but evaluation will continue.")

class CodeEvaluationMetrics:
    """
    Comprehensive evaluation metrics for code generation tasks
    Includes BLEU, ROUGE, Top-K accuracy, and code-specific metrics
    """
    
    def __init__(self):
        self.rouge_scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)
        self.smoothing_function = SmoothingFunction().method1
        
    def tokenize_code(self, code_text: str) -> List[str]:
        """
        Tokenize code text for evaluation
        
        Args:
            code_text: Source code as string
            
        Returns:
            List of tokens
        """
        # Remove comments and normalize whitespace
        code_text = re.sub(r'//.*?\n', '\n', code_text)  # Remove single-line comments
        code_text = re.sub(r'/\*.*?\*/', '', code_text, flags=re.DOTALL)  # Remove multi-line comments
        
        # Tokenize by splitting on common delimiters
        tokens = re.findall(r'\w+|[^\w\s]', code_text)
        
        # Filter out empty tokens and normalize
        tokens = [token.lower() for token in tokens if token.strip()]
        
        return tokens
    
    def calculate_bleu_score(self, reference: str, candidate: str, max_n=4) -> Dict[str, float]:
        """
        Calculate BLEU score for code generation
        
        Args:
            reference: Reference (ground truth) code
            candidate: Generated code
            max_n: Maximum n-gram order
            
        Returns:
            Dictionary with BLEU scores
        """
        try:
            # Tokenize both reference and candidate
            ref_tokens = self.tokenize_code(reference)
            cand_tokens = self.tokenize_code(candidate)
            
            if not ref_tokens or not cand_tokens:
                return {f'bleu_{i}': 0.0 for i in range(1, max_n + 1)}
            
            # Calculate BLEU scores for different n-grams
            bleu_scores = {}
            
            for n in range(1, max_n + 1):
                try:
                    weights = [1.0/n] * n + [0.0] * (4-n)  # Uniform weights up to n
                    score = sentence_bleu(
                        [ref_tokens], 
                        cand_tokens, 
                        weights=weights,
                        smoothing_function=self.smoothing_function
                    )
                    bleu_scores[f'bleu_{n}'] = score
                except:
                    bleu_scores[f'bleu_{n}'] = 0.0
            
            return bleu_scores
            
        except Exception as e:
            print(f"Error calculating BLEU score: {e}")
            return {f'bleu_{i}': 0.0 for i in range(1, max_n + 1)}
    
    def calculate_rouge_score(self, reference: str, candidate: str) -> Dict[str, float]:
        """
        Calculate ROUGE scores for code generation
        
        Args:
            reference: Reference (ground truth) code
            candidate: Generated code
            
        Returns:
            Dictionary with ROUGE scores
        """
        try:
            # Tokenize code for ROUGE evaluation
            ref_text = ' '.join(self.tokenize_code(reference))
            cand_text = ' '.join(self.tokenize_code(candidate))
            
            if not ref_text.strip() or not cand_text.strip():
                return {
                    'rouge1_f': 0.0, 'rouge1_p': 0.0, 'rouge1_r': 0.0,
                    'rouge2_f': 0.0, 'rouge2_p': 0.0, 'rouge2_r': 0.0,
                    'rougeL_f': 0.0, 'rougeL_p': 0.0, 'rougeL_r': 0.0
                }
            
            # Calculate ROUGE scores
            scores = self.rouge_scorer.score(ref_text, cand_text)
            
            rouge_results = {}
            for rouge_type, score in scores.items():
                rouge_results[f'{rouge_type}_f'] = score.fmeasure
                rouge_results[f'{rouge_type}_p'] = score.precision
                rouge_results[f'{rouge_type}_r'] = score.recall
            
            return rouge_results
            
        except Exception as e:
            print(f"Error calculating ROUGE score: {e}")
            return {
                'rouge1_f': 0.0, 'rouge1_p': 0.0, 'rouge1_r': 0.0,
                'rouge2_f': 0.0, 'rouge2_p': 0.0, 'rouge2_r': 0.0,
                'rougeL_f': 0.0, 'rougeL_p': 0.0, 'rougeL_r': 0.0
            }
    
    def calculate_exact_match(self, reference: str, candidate: str) -> float:
        """
        Calculate exact match accuracy
        
        Args:
            reference: Reference code
            candidate: Generated code
            
        Returns:
            1.0 if exact match, 0.0 otherwise
        """
        # Normalize whitespace and compare
        ref_normalized = re.sub(r'\s+', ' ', reference.strip())
        cand_normalized = re.sub(r'\s+', ' ', candidate.strip())
        
        return 1.0 if ref_normalized == cand_normalized else 0.0
    
    def calculate_edit_distance(self, reference: str, candidate: str) -> Dict[str, float]:
        """
        Calculate edit distance metrics
        
        Args:
            reference: Reference code
            candidate: Generated code
            
        Returns:
            Dictionary with edit distance metrics
        """
        try:
            ref_lines = reference.split('\n')
            cand_lines = candidate.split('\n')
            
            # Calculate line-level edit distance
            matcher = difflib.SequenceMatcher(None, ref_lines, cand_lines)
            line_similarity = matcher.ratio()
            
            # Calculate character-level edit distance
            char_matcher = difflib.SequenceMatcher(None, reference, candidate)
            char_similarity = char_matcher.ratio()
            
            return {
                'line_similarity': line_similarity,
                'char_similarity': char_similarity,
                'edit_distance': 1.0 - char_similarity
            }
            
        except Exception as e:
            print(f"Error calculating edit distance: {e}")
            return {
                'line_similarity': 0.0,
                'char_similarity': 0.0,
                'edit_distance': 1.0
            }
    
    def calculate_top_k_accuracy(self, reference: str, candidates: List[str], k: int = 5) -> float:
        """
        Calculate Top-K accuracy
        
        Args:
            reference: Reference (ground truth) code
            candidates: List of candidate codes (sorted by confidence/probability)
            k: K value for Top-K accuracy
            
        Returns:
            Top-K accuracy score
        """
        if not candidates or k <= 0:
            return 0.0
        
        # Check if reference matches any of the top-k candidates
        top_k_candidates = candidates[:min(k, len(candidates))]
        
        for candidate in top_k_candidates:
            if self.calculate_exact_match(reference, candidate) > 0.5:
                return 1.0
        
        # If no exact match, use similarity-based matching
        max_similarity = 0.0
        for candidate in top_k_candidates:
            similarity = self.calculate_edit_distance(reference, candidate)['char_similarity']
            max_similarity = max(max_similarity, similarity)
        
        # Consider it a match if similarity is above threshold
        return 1.0 if max_similarity > 0.8 else 0.0
    
    def calculate_compilation_success(self, code: str) -> float:
        """
        Check if generated code can be parsed (simplified compilation check)
        
        Args:
            code: Generated code
            
        Returns:
            1.0 if code can be parsed, 0.0 otherwise
        """
        try:
            # Try to parse Java code using javalang
            try:
                javalang.parse.parse(code)
                return 1.0
            except:
                # If javalang fails, try basic syntax checks
                # Check for balanced braces
                brace_count = code.count('{') - code.count('}')
                paren_count = code.count('(') - code.count(')')
                bracket_count = code.count('[') - code.count(']')
                
                if brace_count == 0 and paren_count == 0 and bracket_count == 0:
                    return 0.8  # Likely valid but not confirmed
                else:
                    return 0.0
                    
        except Exception as e:
            return 0.0
    
    def evaluate_single_prediction(self, reference: str, candidate: str, 
                                 candidates_list: List[str] = None) -> Dict[str, Any]:
        """
        Evaluate a single prediction with all metrics
        
        Args:
            reference: Reference (ground truth) code
            candidate: Generated code (best prediction)
            candidates_list: List of all candidates for Top-K evaluation
            
        Returns:
            Dictionary with all evaluation metrics
        """
        metrics = {}
        
        # BLEU scores
        bleu_scores = self.calculate_bleu_score(reference, candidate)
        metrics.update(bleu_scores)
        
        # ROUGE scores
        rouge_scores = self.calculate_rouge_score(reference, candidate)
        metrics.update(rouge_scores)
        
        # Exact match
        metrics['exact_match'] = self.calculate_exact_match(reference, candidate)
        
        # Edit distance metrics
        edit_metrics = self.calculate_edit_distance(reference, candidate)
        metrics.update(edit_metrics)
        
        # Compilation success
        metrics['compilation_success'] = self.calculate_compilation_success(candidate)
        
        # Top-K accuracy (if candidates list provided)
        if candidates_list:
            for k in [1, 3, 5, 10]:
                if len(candidates_list) >= k:
                    metrics[f'top_{k}_accuracy'] = self.calculate_top_k_accuracy(
                        reference, candidates_list, k
                    )
        
        return metrics
    
    def evaluate_dataset(self, references: List[str], candidates: List[str], 
                        candidates_lists: List[List[str]] = None) -> Dict[str, float]:
        """
        Evaluate entire dataset with all metrics
        
        Args:
            references: List of reference codes
            candidates: List of generated codes (best predictions)
            candidates_lists: List of candidate lists for each example (for Top-K)
            
        Returns:
            Dictionary with averaged metrics
        """
        if len(references) != len(candidates):
            raise ValueError("References and candidates must have the same length")
        
        all_metrics = []

        print(f"Processing {len(references)} predictions...")
        for i, (ref, cand) in enumerate(zip(references, candidates)):
            if i % 5 == 0:  # Progress every 5 items
                print(f"  Progress: {i+1}/{len(references)}")

            try:
                cand_list = candidates_lists[i] if candidates_lists else None
                metrics = self.evaluate_single_prediction(ref, cand, cand_list)
                all_metrics.append(metrics)
            except Exception as e:
                print(f"  Error processing item {i}: {e}")
                # Add empty metrics to maintain list length
                all_metrics.append({
                    'bleu_1': 0.0, 'bleu_2': 0.0, 'bleu_4': 0.0,
                    'rouge1_f': 0.0, 'rouge2_f': 0.0, 'rougeL_f': 0.0,
                    'exact_match': 0.0, 'char_similarity': 0.0,
                    'compilation_success': 0.0
                })
        
        # Calculate average metrics
        avg_metrics = {}
        if all_metrics:
            for key in all_metrics[0].keys():
                values = [m[key] for m in all_metrics if key in m and m[key] is not None]
                if values:
                    avg_metrics[key] = np.mean(values)
                else:
                    avg_metrics[key] = 0.0
        
        # Add count information
        avg_metrics['total_samples'] = len(references)
        avg_metrics['valid_samples'] = len([m for m in all_metrics if m])
        
        return avg_metrics

def evaluate_model_results(results_df: pd.DataFrame, 
                          reference_col: str = 'expected_fixed_code',
                          prediction_col: str = 'predicted_fixed_code') -> Dict[str, float]:
    """
    Evaluate model results from DataFrame
    
    Args:
        results_df: DataFrame with model results
        reference_col: Column name for reference/ground truth code
        prediction_col: Column name for predicted code
        
    Returns:
        Dictionary with evaluation metrics
    """
    evaluator = CodeEvaluationMetrics()
    
    references = results_df[reference_col].tolist()
    predictions = results_df[prediction_col].tolist()
    
    # Filter out None/NaN values
    valid_pairs = [(ref, pred) for ref, pred in zip(references, predictions) 
                   if pd.notna(ref) and pd.notna(pred) and ref and pred]
    
    if not valid_pairs:
        print("No valid reference-prediction pairs found")
        return {}
    
    references, predictions = zip(*valid_pairs)
    
    print(f"Evaluating {len(valid_pairs)} valid predictions...")

    try:
        metrics = evaluator.evaluate_dataset(list(references), list(predictions))
        print(f"✅ Evaluation completed successfully")
        return metrics
    except Exception as e:
        print(f"❌ Error during evaluation: {e}")
        import traceback
        traceback.print_exc()
        return {}
