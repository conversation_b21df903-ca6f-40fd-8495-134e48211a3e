#!/usr/bin/env python3
"""
Real Defects4J Dataset Extractor for GPT and T5 Fine-tuning
This script extracts real bug-fix pairs from Defects4J repository.
Integrates with existing gpt_fine_tuner.py and update_t5_tuner.py scripts.
"""

import os
import subprocess
import pandas as pd
import json
from tqdm import tqdm
import tempfile
import shutil
import sys

# Use same paths as existing scripts
BASE_DIR = "defect4j_finetune"
DATA_DIR = os.path.join(BASE_DIR, "data")
DEFECT4J_DIR = os.path.join(BASE_DIR, "defects4j")

# Create directories
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(BASE_DIR, exist_ok=True)

# All available Defects4J projects with bug counts
PROJECT_INFO = {
    "Chart": 26,     # JFreeChart
    "Closure": 176,  # Google Closure Compiler  
    "Lang": 65,      # Apache Commons Lang
    "Math": 106,     # Apache Commons Math
    "Mockito": 38,   # Mockito
    "Time": 27,      # Joda-Time
}

# Import configuration (user can modify dataset_config.py)
try:
    from dataset_config import SELECTED_PROJECTS, MAX_BUGS_PER_PROJECT, PROJECT_INFO, print_configuration
    DEFAULT_PROJECTS = SELECTED_PROJECTS
    DEFAULT_MAX_BUGS = MAX_BUGS_PER_PROJECT
except ImportError:
    # Fallback configuration if dataset_config.py not available
    DEFAULT_PROJECTS = ["Lang", "Math", "Chart"]
    DEFAULT_MAX_BUGS = 25
    PROJECT_INFO = {
        "Chart": 26, "Closure": 176, "Lang": 65,
        "Math": 106, "Mockito": 38, "Time": 27
    }

def check_java_version():
    """Check if Java version is compatible with Defects4J"""
    try:
        result = subprocess.run(["java", "-version"], capture_output=True, text=True)
        output = result.stderr  # Java version goes to stderr

        # Extract version number
        import re
        # Try newer format first (Java 9+)
        version_match = re.search(r'"(\d+)', output)
        if version_match:
            major = int(version_match.group(1))
            if major == 11:
                return True, "Java 11"
            elif major == 8:
                return True, "Java 8"
            else:
                return False, f"Java {major}"

        # Try older format (Java 8)
        version_match = re.search(r'"1\.(\d+)', output)
        if version_match:
            minor = int(version_match.group(1))
            if minor == 8:
                return True, "Java 8"
            else:
                return False, f"Java 1.{minor}"

        return False, "Unknown Java version"
    except Exception as e:
        return False, f"Java not found: {e}"

def show_java_setup_instructions():
    """Show instructions for setting up the correct Java version"""
    print("\n" + "="*60)
    print("🔧 JAVA SETUP INSTRUCTIONS")
    print("="*60)

    print("\nDefects4J requires Java 11. You have Java 23.")
    print("\n📋 Options to fix this:")

    print("\n1️⃣  Install Java 11 (Recommended):")
    print("   macOS: brew install openjdk@11")
    print("   Ubuntu: sudo apt install openjdk-11-jdk")
    print("   Windows: Download from https://adoptium.net/")

    print("\n2️⃣  Use JAVA_HOME to switch versions:")
    print("   export JAVA_HOME=/path/to/java11")
    print("   export PATH=$JAVA_HOME/bin:$PATH")

    print("\n3️⃣  Use jenv (Java version manager):")
    print("   brew install jenv")
    print("   jenv add /path/to/java11")
    print("   jenv local 11")

    print("\n4️⃣  Check available Java versions:")
    print("   macOS: /usr/libexec/java_home -V")
    print("   Linux: update-alternatives --list java")

    print("\n💡 After installing Java 11:")
    print("   1. Restart terminal")
    print("   2. Run: java -version")
    print("   3. Verify it shows Java 11")
    print("   4. Run this script again")

    print("\n🔗 Helpful links:")
    print("   - Java 11 download: https://adoptium.net/temurin/releases/?version=11")
    print("   - jenv setup: https://github.com/jenv/jenv")

def setup_defects4j():
    """Setup Defects4J repository and tools with proper Java version handling"""
    print("Setting up Defects4J...")

    # Check Java version first
    java_compatible, java_version = check_java_version()
    print(f"Detected: {java_version}")

    if not java_compatible:
        print("❌ INCOMPATIBLE JAVA VERSION")
        print(f"   Required: Java 11")
        print(f"   You have: {java_version}")
        print("   Defects4J will not work with this version.")

        show_java_setup_instructions()

        print("\n❌ Cannot proceed with current Java version.")
        print("Please install Java 11 and try again.")
        return None

    # Clone repository if not exists
    if not os.path.exists(DEFECT4J_DIR):
        print("Cloning Defects4J repository...")
        try:
            # Try shallow clone first (faster, less data)
            subprocess.run([
                "git", "clone", "--depth", "1", "--single-branch",
                "https://github.com/rjust/defects4j.git", DEFECT4J_DIR
            ], check=True, capture_output=True, text=True, timeout=300)
            print(f"✅ Successfully cloned Defects4J repository")
        except subprocess.CalledProcessError as e:
            print(f"❌ Git clone failed: {e}")
            print("\n💡 Windows troubleshooting:")
            print("   1. Install Git for Windows: https://git-scm.com/download/win")
            print("   2. Run Command Prompt as Administrator")
            print("   3. Check firewall/antivirus settings")
            print("   4. Enable long paths: git config --global core.longpaths true")
            print("   5. Try: git config --global http.sslverify false (if SSL issues)")
            return None
        except subprocess.TimeoutExpired:
            print("❌ Git clone timed out (slow connection)")
            print("💡 Try again with better internet connection")
            return None
        except FileNotFoundError:
            print("❌ Git not found in PATH")
            print("💡 Install Git for Windows:")
            print("   - Download: https://git-scm.com/download/win")
            print("   - During installation, select 'Git from command line and 3rd-party software'")
            print("   - Restart Command Prompt after installation")
            return None
        except Exception as e:
            print(f"❌ Unexpected error during Git clone: {e}")
            return None
    else:
        print(f"✅ Defects4J repository already exists")

    # Setup command path
    bin_dir = os.path.join(DEFECT4J_DIR, "framework", "bin")
    defects4j_cmd = os.path.join(bin_dir, "defects4j")

    # Make executable on Unix systems
    if os.path.exists(defects4j_cmd) and os.name != 'nt':
        try:
            subprocess.run(["chmod", "+x", defects4j_cmd], check=True)
        except:
            pass

    # Add to PATH
    os.environ["PATH"] = bin_dir + os.pathsep + os.environ.get("PATH", "")

    # Test if defects4j command works
    print("Testing Defects4J command...")
    try:
        result = subprocess.run([defects4j_cmd, "info", "-p", "Lang"],
                              capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            print("✅ Defects4J command is working!")
            return defects4j_cmd
        else:
            print(f"❌ Defects4J command failed (return code: {result.returncode})")
            print(f"Error output: {result.stderr}")

            if "Java 11 is required" in result.stderr:
                print("\n💡 SOLUTION: Install Java 11")
                show_java_setup_instructions()
            elif "JAVA_HOME" in result.stderr:
                print("💡 Try setting JAVA_HOME environment variable")
            elif "java.lang.UnsupportedClassVersionError" in result.stderr:
                print("💡 Java version incompatibility - install Java 11")

            return None

    except subprocess.TimeoutExpired:
        print("❌ Defects4J command timed out")
        return None
    except Exception as e:
        print(f"❌ Defects4J command test failed: {e}")
        return None

def get_project_bug_count(defects4j_cmd, project):
    """Get the actual number of bugs for a project"""
    try:
        result = subprocess.run([
            defects4j_cmd, "info", "-p", project
        ], capture_output=True, text=True, check=True)

        # Parse output to get number of bugs
        lines = result.stdout.split('\n')
        for line in lines:
            if "Number of bugs:" in line:
                num_bugs = int(line.split(':')[1].strip())
                return num_bugs

        # Fallback to known counts
        if isinstance(PROJECT_INFO.get(project), dict):
            return PROJECT_INFO[project].get('bugs', 0)
        else:
            return PROJECT_INFO.get(project, 0)

    except Exception as e:
        print(f"Warning: Could not get bug count for {project}: {e}")
        print(f"Using fallback count for {project}")

        # Fallback to known counts
        if isinstance(PROJECT_INFO.get(project), dict):
            return PROJECT_INFO[project].get('bugs', 0)
        else:
            return PROJECT_INFO.get(project, 0)

def extract_bug_fix_pair(defects4j_cmd, project, bug_id):
    """Extract buggy and fixed versions of a specific bug"""
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create temporary directories for buggy and fixed versions
            buggy_dir = os.path.join(temp_dir, f"{project}_{bug_id}_buggy")
            fixed_dir = os.path.join(temp_dir, f"{project}_{bug_id}_fixed")
            
            # Checkout buggy version
            subprocess.run([
                defects4j_cmd, "checkout", "-p", project, "-v", f"{bug_id}b", "-w", buggy_dir
            ], check=True, capture_output=True, text=True)
            
            # Checkout fixed version  
            subprocess.run([
                defects4j_cmd, "checkout", "-p", project, "-v", f"{bug_id}f", "-w", fixed_dir
            ], check=True, capture_output=True, text=True)
            
            # Get modified classes
            result = subprocess.run([
                defects4j_cmd, "export", "-p", "classes.modified", "-w", buggy_dir
            ], capture_output=True, text=True, check=True)
            
            modified_classes = [cls.strip() for cls in result.stdout.strip().split('\n') if cls.strip()]
            
            bug_fix_pairs = []
            
            for class_name in modified_classes:
                if not class_name or class_name.startswith("#"):
                    continue
                    
                # Convert class name to file path
                file_path = class_name.replace('.', '/') + '.java'
                
                # Find the actual file in source directories
                buggy_file = None
                fixed_file = None

                # Different projects use different source directory structures
                for src_dir in ['src/main/java', 'src/java', 'src', 'source']:
                    buggy_candidate = os.path.join(buggy_dir, src_dir, file_path)
                    fixed_candidate = os.path.join(fixed_dir, src_dir, file_path)

                    if os.path.exists(buggy_candidate) and os.path.exists(fixed_candidate):
                        buggy_file = buggy_candidate
                        fixed_file = fixed_candidate
                        break
                
                if buggy_file and fixed_file:
                    # Read file contents
                    try:
                        with open(buggy_file, 'r', encoding='utf-8', errors='ignore') as f:
                            buggy_code = f.read()
                        
                        with open(fixed_file, 'r', encoding='utf-8', errors='ignore') as f:
                            fixed_code = f.read()
                        
                        # Only include if there are actual differences
                        if buggy_code.strip() != fixed_code.strip():
                            bug_fix_pairs.append({
                                'project': project,
                                'bug_id': bug_id,
                                'file': file_path,
                                'class_name': class_name,
                                'buggy_code': buggy_code,
                                'fixed_code': fixed_code
                            })
                    except Exception as e:
                        print(f"Error reading files for {class_name}: {e}")
                        continue
            
            return bug_fix_pairs
            
    except Exception as e:
        print(f"Error extracting {project} bug {bug_id}: {e}")
        return []

def interactive_configuration():
    """Interactive configuration for dataset extraction"""
    print("\n" + "="*60)
    print("🔧 DEFECTS4J DATASET CONFIGURATION")
    print("="*60)

    # Show current configuration from dataset_config.py
    try:
        print("\n📋 Current configuration (from dataset_config.py):")
        print_configuration()

        use_config = input("\nUse current configuration? (y/n): ").lower().strip()
        if use_config == 'y':
            return DEFAULT_PROJECTS, DEFAULT_MAX_BUGS
    except:
        pass

    print("\nAvailable projects:")
    if isinstance(PROJECT_INFO, dict):
        for project, info in PROJECT_INFO.items():
            if isinstance(info, dict):
                count = info.get('bugs', 0)
            else:
                count = info
            print(f"  - {project}: {count} bugs")
    else:
        print("  - Lang: 65 bugs")
        print("  - Math: 106 bugs")
        print("  - Chart: 26 bugs")
        print("  - Time: 27 bugs")
        print("  - Closure: 176 bugs")
        print("  - Mockito: 38 bugs")
    
    # Project selection
    print(f"\nDefault projects: {DEFAULT_PROJECTS}")
    use_default = input("Use default projects? (y/n): ").lower().strip()
    
    if use_default == 'y':
        selected_projects = DEFAULT_PROJECTS
    else:
        print("Enter projects (comma-separated, e.g., Lang,Math,Chart):")
        project_input = input("> ").strip()
        selected_projects = [p.strip() for p in project_input.split(',') if p.strip() in PROJECT_INFO]
        
        if not selected_projects:
            print("No valid projects selected. Using default.")
            selected_projects = DEFAULT_PROJECTS
    
    # Bug count selection
    print(f"\nDefault max bugs per project: {DEFAULT_MAX_BUGS}")
    print("Options:")
    print("  - Enter a number (e.g., 10, 50)")
    print("  - Enter 'all' for all available bugs")
    print("  - Press Enter for default")
    
    bug_input = input("> ").strip().lower()
    
    if bug_input == 'all':
        max_bugs = None
    elif bug_input.isdigit():
        max_bugs = int(bug_input)
    elif bug_input == '':
        max_bugs = DEFAULT_MAX_BUGS
    else:
        print("Invalid input. Using default.")
        max_bugs = DEFAULT_MAX_BUGS
    
    # Calculate total expected examples
    total_expected = 0
    for project in selected_projects:
        if isinstance(PROJECT_INFO.get(project), dict):
            project_bugs = PROJECT_INFO[project].get('bugs', 0)
        else:
            project_bugs = PROJECT_INFO.get(project, 0)

        if max_bugs:
            project_bugs = min(project_bugs, max_bugs)
        total_expected += project_bugs
    
    print(f"\n📊 Configuration Summary:")
    print(f"   Projects: {selected_projects}")
    print(f"   Max bugs per project: {max_bugs or 'All'}")
    print(f"   Expected total examples: ~{total_expected}")
    
    # Cost estimation for GPT
    if total_expected <= 50:
        cost_estimate = "$5-10"
    elif total_expected <= 100:
        cost_estimate = "$10-20"
    elif total_expected <= 200:
        cost_estimate = "$20-40"
    else:
        cost_estimate = "$40-100+"
    
    print(f"   Estimated GPT fine-tuning cost: {cost_estimate}")
    print(f"   T5 fine-tuning cost: Free (local)")
    
    confirm = input("\nProceed with extraction? (y/n): ").lower().strip()
    if confirm != 'y':
        print("❌ Extraction cancelled.")
        return None, None
    
    return selected_projects, max_bugs

def extract_real_defects4j_dataset(selected_projects=None, max_bugs_per_project=None):
    """Extract the real Defects4J dataset with user configuration"""
    print("\n🚀 Starting Real Defects4J Dataset Extraction...")

    # Use interactive configuration if not provided
    if selected_projects is None or max_bugs_per_project is None:
        selected_projects, max_bugs_per_project = interactive_configuration()
        if selected_projects is None:
            return None

    # Setup Defects4J
    defects4j_cmd = setup_defects4j()
    if not defects4j_cmd:
        print("❌ Defects4J setup failed. Cannot extract real data.")
        print("💡 Please fix Java/Defects4J setup and try again.")
        return None

    all_data = []

    for project in selected_projects:
        print(f"\n📁 Processing project: {project}")

        # Get actual bug count
        actual_bugs = get_project_bug_count(defects4j_cmd, project)
        bugs_to_process = min(actual_bugs, max_bugs_per_project) if max_bugs_per_project else actual_bugs

        print(f"   Total bugs available: {actual_bugs}")
        print(f"   Processing: {bugs_to_process} bugs")

        # Extract each bug
        project_data = []
        for bug_id in tqdm(range(1, bugs_to_process + 1), desc=f"Extracting {project}"):
            bug_pairs = extract_bug_fix_pair(defects4j_cmd, project, bug_id)
            project_data.extend(bug_pairs)

            # Progress update every 5 bugs
            if bug_id % 5 == 0:
                print(f"   Extracted {len(project_data)} pairs from {bug_id} bugs")

        all_data.extend(project_data)
        print(f"   ✅ {project}: {len(project_data)} bug-fix pairs extracted")

    if not all_data:
        print("❌ No bug-fix pairs extracted!")
        return None

    # Create DataFrame and save
    df = pd.DataFrame(all_data)

    # Save to the same location as existing scripts expect
    output_path = os.path.join(DATA_DIR, 'defect4j_dataset.csv')
    df.to_csv(output_path, index=False)

    print(f"\n✅ Extraction Complete!")
    print(f"📊 Total bug-fix pairs extracted: {len(df)}")
    print(f"📁 Dataset saved to: {output_path}")
    print(f"📈 Projects covered: {list(df['project'].unique())}")
    print(f"📋 Bug counts per project:")
    for project in df['project'].unique():
        count = len(df[df['project'] == project])
        print(f"   {project}: {count} pairs")

    return df

def update_existing_scripts_info():
    """Show information about how existing scripts will use the new data"""
    print("\n" + "="*60)
    print("📝 INTEGRATION WITH EXISTING SCRIPTS")
    print("="*60)

    print("\n✅ Your existing scripts will automatically use the new dataset:")

    print("\n🔧 gpt_fine_tuner.py:")
    print("   - Will load 'defect4j_dataset.csv' (the real data)")
    print("   - No changes needed - just run: python3 gpt_fine_tuner.py")
    print("   - Uses same 80/20 train/validation split")

    print("\n🔧 update_t5_tuner.py:")
    print("   - Will load 'defect4j_dataset.csv' (the real data)")
    print("   - No changes needed - just run: python3 update_t5_tuner.py")
    print("   - Uses same training parameters")

    print("\n🔧 benchmark_comparison.py:")
    print("   - Will compare models trained on real data")
    print("   - Results will be much more meaningful")

    print("\n💡 Training Tips for Larger Datasets:")
    print("   - GPT: Consider reducing epochs to 2-3 for larger datasets")
    print("   - T5: May need 10-15 epochs depending on dataset size")
    print("   - Monitor validation loss to avoid overfitting")
    print("   - Start with smaller datasets to test pipeline")

def main():
    """Main function with user-friendly interface"""
    print("🎯 Real Defects4J Dataset Extractor")
    print("="*50)
    print("This tool extracts real bug-fix pairs from Defects4J")
    print("to replace the current 12 curated examples.")
    print()
    print("The extracted data will be used by:")
    print("  - gpt_fine_tuner.py (GPT-4o fine-tuning)")
    print("  - update_t5_tuner.py (T5 fine-tuning)")
    print("  - benchmark_comparison.py (model comparison)")
    print()
    print("💡 Configuration:")
    print("  - Edit dataset_config.py to set your preferences")
    print("  - Or use interactive mode below")

    # Check if dataset already exists
    existing_dataset = os.path.join(DATA_DIR, 'defect4j_dataset.csv')
    if os.path.exists(existing_dataset):
        df = pd.read_csv(existing_dataset)
        print(f"\n⚠️  Existing dataset found: {len(df)} examples")
        print("This will overwrite the existing dataset.")

        overwrite = input("Continue and overwrite? (y/n): ").lower().strip()
        if overwrite != 'y':
            print("❌ Extraction cancelled.")
            return

    try:
        # Extract dataset
        df = extract_real_defects4j_dataset()

        if df is not None:
            # Show integration info
            update_existing_scripts_info()

            print(f"\n🎉 SUCCESS! Real dataset ready with {len(df)} examples!")
            print("\n📋 Next Steps:")
            print("1. Train GPT: python3 gpt_fine_tuner.py")
            print("2. Train T5: python3 update_t5_tuner.py")
            print("3. Compare: python3 benchmark_comparison.py")

    except KeyboardInterrupt:
        print("\n❌ Extraction interrupted by user.")
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        print("\n💡 Troubleshooting:")
        print("   - Ensure Java is installed (java -version)")
        print("   - Ensure Git is installed (git --version)")
        print("   - Check internet connection")
        print("   - Ensure sufficient disk space (~2GB)")

if __name__ == "__main__":
    main()
