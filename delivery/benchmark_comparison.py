import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import os
from typing import Dict, List, Tuple, Any
import json
from evaluation_metrics import CodeEvaluationMetrics, evaluate_model_results

# Set style for plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class ModelBenchmark:
    """
    Comprehensive benchmarking comparison between T5 and GPT models
    """
    
    def __init__(self, output_dir="benchmark_results"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.evaluator = CodeEvaluationMetrics()
        
    def load_model_results(self, t5_results_path: str, gpt_results_path: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Load results from both models
        
        Args:
            t5_results_path: Path to T5 evaluation results CSV
            gpt_results_path: Path to GPT evaluation results CSV
            
        Returns:
            Tuple of (T5 DataFrame, GPT DataFrame)
        """
        try:
            t5_df = pd.read_csv(t5_results_path)
            print(f"Loaded T5 results: {len(t5_df)} samples")
        except Exception as e:
            print(f"Error loading T5 results: {e}")
            t5_df = pd.DataFrame()
        
        try:
            gpt_df = pd.read_csv(gpt_results_path)
            print(f"Loaded GPT results: {len(gpt_df)} samples")
        except Exception as e:
            print(f"Error loading GPT results: {e}")
            gpt_df = pd.DataFrame()
        
        return t5_df, gpt_df
    
    def calculate_metrics_comparison(self, t5_df: pd.DataFrame, gpt_df: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """
        Calculate and compare metrics for both models
        
        Args:
            t5_df: T5 results DataFrame
            gpt_df: GPT results DataFrame
            
        Returns:
            Dictionary with metrics for both models
        """
        results = {
            'T5': {},
            'GPT': {}
        }
        
        # Calculate T5 metrics
        if not t5_df.empty and 'expected_fixed_code' in t5_df.columns and 'predicted_fixed_code' in t5_df.columns:
            print("Calculating T5 metrics...")
            try:
                t5_metrics = evaluate_model_results(t5_df)
                results['T5'] = t5_metrics
                print(f"✅ T5 metrics calculated successfully")
            except Exception as e:
                print(f"❌ Error calculating T5 metrics: {e}")
                results['T5'] = {}
        else:
            print("T5 data not available or missing required columns")

        # Calculate GPT metrics
        if not gpt_df.empty and 'expected_fixed_code' in gpt_df.columns and 'predicted_fixed_code' in gpt_df.columns:
            print("Calculating GPT metrics...")
            try:
                gpt_metrics = evaluate_model_results(gpt_df)
                results['GPT'] = gpt_metrics
                print(f"✅ GPT metrics calculated successfully")
            except Exception as e:
                print(f"❌ Error calculating GPT metrics: {e}")
                results['GPT'] = {}
        else:
            print("GPT data not available or missing required columns")
        
        return results
    
    def create_metrics_comparison_table(self, metrics: Dict[str, Dict[str, float]]) -> pd.DataFrame:
        """
        Create a comparison table of metrics
        
        Args:
            metrics: Dictionary with metrics for both models
            
        Returns:
            DataFrame with comparison table
        """
        # Key metrics to compare
        key_metrics = [
            'bleu_1', 'bleu_2', 'bleu_4',
            'rouge1_f', 'rouge2_f', 'rougeL_f',
            'exact_match', 'char_similarity', 'compilation_success',
            'top_1_accuracy', 'top_3_accuracy', 'top_5_accuracy'
        ]
        
        comparison_data = []
        
        for metric in key_metrics:
            row = {'Metric': metric.replace('_', ' ').title()}
            
            # Get values for both models
            t5_value = metrics.get('T5', {}).get(metric, 0.0)
            gpt_value = metrics.get('GPT', {}).get(metric, 0.0)
            
            row['T5'] = f"{t5_value:.4f}"
            row['GPT'] = f"{gpt_value:.4f}"
            
            # Calculate improvement
            if t5_value > 0:
                improvement = ((gpt_value - t5_value) / t5_value) * 100
                row['GPT vs T5 (%)'] = f"{improvement:+.2f}%"
            else:
                row['GPT vs T5 (%)'] = "N/A"
            
            # Determine winner
            if gpt_value > t5_value:
                row['Winner'] = 'GPT'
            elif t5_value > gpt_value:
                row['Winner'] = 'T5'
            else:
                row['Winner'] = 'Tie'
            
            comparison_data.append(row)
        
        comparison_df = pd.DataFrame(comparison_data)
        
        # Save comparison table
        comparison_df.to_csv(os.path.join(self.output_dir, 'metrics_comparison.csv'), index=False)
        
        return comparison_df
    
    def create_visualization_plots(self, metrics: Dict[str, Dict[str, float]]):
        """
        Create visualization plots for model comparison
        
        Args:
            metrics: Dictionary with metrics for both models
        """
        # Prepare data for plotting
        plot_metrics = [
            'bleu_1', 'bleu_2', 'bleu_4',
            'rouge1_f', 'rouge2_f', 'rougeL_f',
            'exact_match', 'char_similarity', 'compilation_success'
        ]
        
        t5_values = [metrics.get('T5', {}).get(m, 0.0) for m in plot_metrics]
        gpt_values = [metrics.get('GPT', {}).get(m, 0.0) for m in plot_metrics]
        metric_labels = [m.replace('_', ' ').title() for m in plot_metrics]
        
        # Create comparison bar plot
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # Bar plot comparison
        x = np.arange(len(metric_labels))
        width = 0.35
        
        ax1.bar(x - width/2, t5_values, width, label='T5', alpha=0.8)
        ax1.bar(x + width/2, gpt_values, width, label='GPT', alpha=0.8)
        
        ax1.set_xlabel('Metrics')
        ax1.set_ylabel('Score')
        ax1.set_title('Model Performance Comparison')
        ax1.set_xticks(x)
        ax1.set_xticklabels(metric_labels, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Radar plot
        angles = np.linspace(0, 2 * np.pi, len(plot_metrics), endpoint=False)
        angles = np.concatenate((angles, [angles[0]]))  # Complete the circle
        
        t5_values_radar = t5_values + [t5_values[0]]
        gpt_values_radar = gpt_values + [gpt_values[0]]
        
        ax2 = plt.subplot(122, projection='polar')
        ax2.plot(angles, t5_values_radar, 'o-', linewidth=2, label='T5')
        ax2.fill(angles, t5_values_radar, alpha=0.25)
        ax2.plot(angles, gpt_values_radar, 'o-', linewidth=2, label='GPT')
        ax2.fill(angles, gpt_values_radar, alpha=0.25)
        
        ax2.set_xticks(angles[:-1])
        ax2.set_xticklabels(metric_labels)
        ax2.set_ylim(0, 1)
        ax2.set_title('Performance Radar Chart')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'model_comparison_plots.png'), dpi=300, bbox_inches='tight')
        plt.show()
        
        # Create Top-K accuracy plot
        top_k_metrics = ['top_1_accuracy', 'top_3_accuracy', 'top_5_accuracy']
        if any(metrics.get('T5', {}).get(m, 0) > 0 or metrics.get('GPT', {}).get(m, 0) > 0 for m in top_k_metrics):
            fig, ax = plt.subplots(figsize=(10, 6))
            
            k_values = [1, 3, 5]
            t5_top_k = [metrics.get('T5', {}).get(f'top_{k}_accuracy', 0.0) for k in k_values]
            gpt_top_k = [metrics.get('GPT', {}).get(f'top_{k}_accuracy', 0.0) for k in k_values]
            
            ax.plot(k_values, t5_top_k, 'o-', linewidth=2, markersize=8, label='T5')
            ax.plot(k_values, gpt_top_k, 's-', linewidth=2, markersize=8, label='GPT')
            
            ax.set_xlabel('K (Top-K Accuracy)')
            ax.set_ylabel('Accuracy')
            ax.set_title('Top-K Accuracy Comparison')
            ax.set_xticks(k_values)
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(os.path.join(self.output_dir, 'top_k_accuracy_comparison.png'), dpi=300, bbox_inches='tight')
            plt.show()
    
    def perform_statistical_tests(self, t5_df: pd.DataFrame, gpt_df: pd.DataFrame) -> Dict[str, Any]:
        """
        Perform statistical significance tests
        
        Args:
            t5_df: T5 results DataFrame
            gpt_df: GPT results DataFrame
            
        Returns:
            Dictionary with statistical test results
        """
        statistical_results = {}
        
        # Ensure both dataframes have the same samples for fair comparison
        if not t5_df.empty and not gpt_df.empty:
            # Find common samples (by project and bug_id)
            if 'project' in t5_df.columns and 'bug_id' in t5_df.columns:
                t5_df['sample_id'] = t5_df['project'].astype(str) + '_' + t5_df['bug_id'].astype(str)
                gpt_df['sample_id'] = gpt_df['project'].astype(str) + '_' + gpt_df['bug_id'].astype(str)
                
                common_samples = set(t5_df['sample_id']) & set(gpt_df['sample_id'])
                
                if common_samples:
                    t5_common = t5_df[t5_df['sample_id'].isin(common_samples)]
                    gpt_common = gpt_df[gpt_df['sample_id'].isin(common_samples)]
                    
                    # Calculate metrics for common samples
                    t5_scores = []
                    gpt_scores = []
                    
                    for sample_id in common_samples:
                        t5_row = t5_common[t5_common['sample_id'] == sample_id].iloc[0]
                        gpt_row = gpt_common[gpt_common['sample_id'] == sample_id].iloc[0]
                        
                        # Calculate BLEU score for this sample
                        if pd.notna(t5_row['expected_fixed_code']) and pd.notna(t5_row['predicted_fixed_code']):
                            t5_bleu = self.evaluator.calculate_bleu_score(
                                t5_row['expected_fixed_code'], 
                                t5_row['predicted_fixed_code']
                            )['bleu_1']
                            t5_scores.append(t5_bleu)
                        
                        if pd.notna(gpt_row['expected_fixed_code']) and pd.notna(gpt_row['predicted_fixed_code']):
                            gpt_bleu = self.evaluator.calculate_bleu_score(
                                gpt_row['expected_fixed_code'], 
                                gpt_row['predicted_fixed_code']
                            )['bleu_1']
                            gpt_scores.append(gpt_bleu)
                    
                    # Perform paired t-test if we have enough samples
                    if len(t5_scores) >= 5 and len(gpt_scores) >= 5 and len(t5_scores) == len(gpt_scores):
                        t_stat, p_value = stats.ttest_rel(gpt_scores, t5_scores)
                        
                        statistical_results['paired_t_test'] = {
                            't_statistic': t_stat,
                            'p_value': p_value,
                            'significant': p_value < 0.05,
                            'sample_size': len(t5_scores),
                            'mean_difference': np.mean(gpt_scores) - np.mean(t5_scores)
                        }
                        
                        # Wilcoxon signed-rank test (non-parametric alternative)
                        wilcoxon_stat, wilcoxon_p = stats.wilcoxon(gpt_scores, t5_scores)
                        
                        statistical_results['wilcoxon_test'] = {
                            'statistic': wilcoxon_stat,
                            'p_value': wilcoxon_p,
                            'significant': wilcoxon_p < 0.05
                        }
        
        return statistical_results
    
    def generate_comprehensive_report(self, t5_results_path: str, gpt_results_path: str) -> str:
        """
        Generate comprehensive benchmark report
        
        Args:
            t5_results_path: Path to T5 results
            gpt_results_path: Path to GPT results
            
        Returns:
            Path to generated report
        """
        print("Generating comprehensive benchmark report...")
        
        # Load results
        t5_df, gpt_df = self.load_model_results(t5_results_path, gpt_results_path)
        
        # Calculate metrics
        metrics = self.calculate_metrics_comparison(t5_df, gpt_df)
        
        # Create comparison table
        comparison_df = self.create_metrics_comparison_table(metrics)
        
        # Create visualizations
        self.create_visualization_plots(metrics)
        
        # Perform statistical tests
        statistical_results = self.perform_statistical_tests(t5_df, gpt_df)
        
        # Generate report
        report_path = os.path.join(self.output_dir, 'benchmark_report.md')
        
        with open(report_path, 'w') as f:
            f.write("# Model Benchmark Report: T5 vs GPT\n\n")
            f.write(f"Generated on: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Summary
            f.write("## Executive Summary\n\n")
            
            if metrics.get('T5') and metrics.get('GPT'):
                # Determine overall winner
                t5_wins = 0
                gpt_wins = 0
                
                for _, row in comparison_df.iterrows():
                    if row['Winner'] == 'T5':
                        t5_wins += 1
                    elif row['Winner'] == 'GPT':
                        gpt_wins += 1
                
                if gpt_wins > t5_wins:
                    f.write(f"**Overall Winner: GPT** (wins in {gpt_wins}/{len(comparison_df)} metrics)\n\n")
                elif t5_wins > gpt_wins:
                    f.write(f"**Overall Winner: T5** (wins in {t5_wins}/{len(comparison_df)} metrics)\n\n")
                else:
                    f.write("**Result: Tie** - Both models perform similarly overall\n\n")
            
            # Detailed metrics
            f.write("## Detailed Metrics Comparison\n\n")
            f.write(comparison_df.to_markdown(index=False))
            f.write("\n\n")
            
            # Statistical significance
            if statistical_results:
                f.write("## Statistical Significance Tests\n\n")
                
                if 'paired_t_test' in statistical_results:
                    t_test = statistical_results['paired_t_test']
                    f.write(f"### Paired T-Test Results\n")
                    f.write(f"- Sample size: {t_test['sample_size']}\n")
                    f.write(f"- T-statistic: {t_test['t_statistic']:.4f}\n")
                    f.write(f"- P-value: {t_test['p_value']:.4f}\n")
                    f.write(f"- Statistically significant: {'Yes' if t_test['significant'] else 'No'}\n")
                    f.write(f"- Mean difference (GPT - T5): {t_test['mean_difference']:.4f}\n\n")
            
            # Recommendations
            f.write("## Recommendations\n\n")
            
            if metrics.get('GPT', {}).get('bleu_1', 0) > metrics.get('T5', {}).get('bleu_1', 0):
                f.write("- GPT shows superior performance in code generation quality (BLEU scores)\n")
            
            if metrics.get('GPT', {}).get('compilation_success', 0) > metrics.get('T5', {}).get('compilation_success', 0):
                f.write("- GPT generates more syntactically correct code\n")
            
            if metrics.get('T5', {}).get('exact_match', 0) > metrics.get('GPT', {}).get('exact_match', 0):
                f.write("- T5 shows better exact match accuracy\n")
            
            f.write("\n## Files Generated\n\n")
            f.write("- `metrics_comparison.csv`: Detailed metrics comparison table\n")
            f.write("- `model_comparison_plots.png`: Performance visualization charts\n")
            f.write("- `top_k_accuracy_comparison.png`: Top-K accuracy comparison\n")
            f.write("- `benchmark_report.md`: This comprehensive report\n")
        
        print(f"Comprehensive report generated: {report_path}")
        return report_path

def main():
    """
    Main function to run benchmark comparison
    """
    # Initialize benchmark
    benchmark = ModelBenchmark()
    
    # Define paths to results files
    base_dir = "defect4j_finetune"
    t5_results = os.path.join(base_dir, "output", "t5_evaluation_results.csv")
    gpt_results = os.path.join(base_dir, "gpt_output", "gpt_evaluation_results.csv")
    
    # Check if files exist and provide helpful guidance
    t5_exists = os.path.exists(t5_results)
    gpt_exists = os.path.exists(gpt_results)

    if not t5_exists and not gpt_exists:
        print(f"Neither T5 nor GPT results found.")
        print(f"T5 results expected at: {t5_results}")
        print(f"GPT results expected at: {gpt_results}")
        print("\nPlease run both fine-tuning scripts first:")
        print("1. python3 gpt_fine_tuner.py")
        print("2. python3 update_t5_tuner.py")
        return
    elif not t5_exists:
        print(f"T5 results not found at {t5_results}")
        print("Will create comparison with GPT results only.")
        print("To get full comparison, run: python3 update_t5_tuner.py")
    elif not gpt_exists:
        print(f"GPT results not found at {gpt_results}")
        print("Will create comparison with T5 results only.")
        print("To get full comparison, run: python3 gpt_fine_tuner.py")
    
    # Generate comprehensive report
    report_path = benchmark.generate_comprehensive_report(t5_results, gpt_results)
    
    print(f"\nBenchmark comparison complete!")
    print(f"Report available at: {report_path}")
    print(f"Additional files in: {benchmark.output_dir}")

if __name__ == "__main__":
    main()
