K 10
svn:author
V 7
mungady
K 8
svn:date
V 27
2013-08-04T06:39:27.372260Z
K 7
svn:log
V 849
2013-08-04  <PERSON>  <<EMAIL>>

    * org/jfree/chart/ChartFactory.java
    (createPieChart): Simplified version (less parameters),
    (createPieChart3D): Likewise, 
    (createBarChart): Likewise, 
    (createStackedBarChart): Likewise,
    (createBarChart3D): Likewise,
    (createStackedBarChart3D): Likewise,
    (createAreaChart): Likewise,
    (createStacked<PERSON>rea<PERSON>hart): Likewise,
    (createLineChart): Likewise,
    (createLineChart3D): Likewise,
    (createGanttChart): Likewise,
    (createScatterPlot): Likewise,
    (createXYBar<PERSON>hart): Likewise,
    (createXY<PERSON>rea<PERSON>hart): Likewise,
    (createStackedXY<PERSON>rea<PERSON>hart): Likewise,
    (createXYLine<PERSON>hart): Likewise,
    (createXYStep<PERSON>hart): Likewise,
    (createXYStep<PERSON><PERSON><PERSON><PERSON>): Likewise,
    (createTimeSeries<PERSON>hart): Likewise,
    (createB<PERSON><PERSON><PERSON><PERSON>): Likewise.
END
