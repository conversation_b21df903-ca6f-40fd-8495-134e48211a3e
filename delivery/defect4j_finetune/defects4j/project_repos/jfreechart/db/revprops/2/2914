K 10
svn:author
V 7
mungady
K 8
svn:date
V 27
2013-09-12T15:17:26.876877Z
K 7
svn:log
V 925
2013-09-12  <PERSON>  <<EMAIL>>

    * org/jfree/chart/LocalizationBundle.properties
    (FILE_EXISTS_CONFIRM_OVERWRITE): New entry,
    (PDF_Files): Likewise,
    (SVG_Files): Likewise,
    * org/jfree/chart/ChartPanel.java
    (SAVE_AS_PNG_COMMAND): New field,
    (SAVE_AS_SVG_COMMAND): Likewise,
    (SAVE_AS_PDF_COMMAND): Likewise,
    (actionPerformed): Handle SVG and PDF options,
    (saveAsSVG): New private method,
    (generateSVG): New method,
    (createSVGGraphics2D): New private method,
    (saveAsPDF): Likewise,
    (isOrsonPDFAvailable): Likewise,
    (writeAsPDF): Likewise,
    (createPopupMenu): Add SVG and PDF options when available,
    * org/jfree/chart/plot/PiePlot.java
    (draw): Check for KEY_SUPPRESS_SHADOW_GENERATION rendering hint,
    * org/jfree/chart/plot/CategoryPlot.java
    (draw): Likewise,
    * org/jfree/chart/plot/XYPlot.java
    (draw): Likewise.
E<PERSON>
