#!/usr/bin/env python3
"""
Quick test script to debug benchmark comparison issues
"""

import pandas as pd
import os
from evaluation_metrics import evaluate_model_results

def test_evaluation_metrics():
    """Test evaluation metrics with minimal data"""
    print("🧪 Testing evaluation metrics...")
    
    # Create minimal test data
    test_data = {
        'project': ['Test'],
        'bug_id': [1],
        'file': ['Test.java'],
        'buggy_code': ['public class Test { int x = 1; }'],
        'expected_fixed_code': ['public class Test { int x = 2; }'],
        'predicted_fixed_code': ['public class Test { int x = 2; }']
    }
    
    df = pd.DataFrame(test_data)
    
    try:
        print("Testing evaluate_model_results...")
        metrics = evaluate_model_results(df)
        print(f"✅ Metrics calculated: {len(metrics)} metrics")
        for key, value in metrics.items():
            print(f"  {key}: {value}")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_existing_results():
    """Check existing result files"""
    print("\n📁 Checking existing result files...")
    
    base_dir = "defect4j_finetune"
    t5_results = os.path.join(base_dir, "output", "t5_evaluation_results.csv")
    gpt_results = os.path.join(base_dir, "gpt_output", "gpt_evaluation_results.csv")
    
    print(f"T5 results: {t5_results}")
    print(f"  Exists: {os.path.exists(t5_results)}")
    if os.path.exists(t5_results):
        try:
            df = pd.read_csv(t5_results)
            print(f"  Rows: {len(df)}")
            print(f"  Columns: {list(df.columns)}")
        except Exception as e:
            print(f"  Error reading: {e}")
    
    print(f"\nGPT results: {gpt_results}")
    print(f"  Exists: {os.path.exists(gpt_results)}")
    if os.path.exists(gpt_results):
        try:
            df = pd.read_csv(gpt_results)
            print(f"  Rows: {len(df)}")
            print(f"  Columns: {list(df.columns)}")
        except Exception as e:
            print(f"  Error reading: {e}")

def test_benchmark_with_real_data():
    """Test benchmark with actual result files"""
    print("\n🎯 Testing benchmark with real data...")
    
    base_dir = "defect4j_finetune"
    t5_results = os.path.join(base_dir, "output", "t5_evaluation_results.csv")
    gpt_results = os.path.join(base_dir, "gpt_output", "gpt_evaluation_results.csv")
    
    if not os.path.exists(t5_results) and not os.path.exists(gpt_results):
        print("❌ No result files found")
        return False
    
    try:
        from benchmark_comparison import ModelBenchmark
        
        benchmark = ModelBenchmark(output_dir="test_benchmark_results")
        
        # Load results
        print("Loading results...")
        t5_df, gpt_df = benchmark.load_model_results(t5_results, gpt_results)
        
        # Test metrics calculation
        print("Testing metrics calculation...")
        metrics = benchmark.calculate_metrics_comparison(t5_df, gpt_df)
        
        print("✅ Benchmark test completed successfully!")
        print(f"T5 metrics: {len(metrics.get('T5', {}))}")
        print(f"GPT metrics: {len(metrics.get('GPT', {}))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Benchmark test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🔧 Benchmark Debugging Tests")
    print("=" * 40)
    
    # Test 1: Basic evaluation metrics
    test1_passed = test_evaluation_metrics()
    
    # Test 2: Check existing files
    check_existing_results()
    
    # Test 3: Test with real data
    test3_passed = test_benchmark_with_real_data()
    
    print("\n" + "=" * 40)
    print("📊 Test Summary:")
    print(f"  Basic metrics test: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"  Benchmark test: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    
    if test1_passed and test3_passed:
        print("\n🎉 All tests passed! Benchmark should work.")
    else:
        print("\n⚠️  Some tests failed. Check errors above.")

if __name__ == "__main__":
    main()
