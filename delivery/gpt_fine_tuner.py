import os
import json
import random
import pandas as pd
import numpy as np
from tqdm import tqdm
import openai
from openai import OpenAI
import base64
import requests
from PIL import Image
import io
import time
from datetime import datetime
import subprocess
import tempfile
import shutil

# Set random seed for reproducibility
def set_seed(seed):
    random.seed(seed)
    np.random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)

set_seed(42)

# Define paths
BASE_DIR = "defect4j_finetune"
DATA_DIR = os.path.join(BASE_DIR, "data")
OUTPUT_DIR = os.path.join(BASE_DIR, "gpt_output")
CKPT_DIR = os.path.join(OUTPUT_DIR, "checkpoints")
DEFECT4J_DIR = os.path.join(BASE_DIR, "defects4j")

# Create directories
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(CKPT_DIR, exist_ok=True)

# Define the top 6 Defect4J projects to use (same as T5)
TOP_PROJECTS = [
    "Lang",    # Apache Commons-Lang
    "Math",    # Apache Commons-Math
    "Closure", # Closure Compiler
    "Time",    # Joda-Time
    "Chart",   # JFreeChart
    "Mockito"  # Mockito
]

class GPTFineTuner:
    def __init__(self, api_key, model_name="gpt-4o-2024-08-06", base_url=None):
        """
        Initialize GPT Fine-tuner with OpenAI API

        Args:
            api_key: OpenAI API key
            model_name: Model to use for fine-tuning (gpt-4o-2024-08-06 supports vision and fine-tuning)
            base_url: Optional base URL for API calls
        """
        self.client = OpenAI(api_key=api_key, base_url=base_url)
        self.model_name = model_name
        self.fine_tuned_model_id = None
        
    def _truncate_code_if_needed(self, code, max_lines=200):
        """
        Truncate code if it's too long, keeping the most important parts
        """
        lines = code.split('\n')
        if len(lines) <= max_lines:
            return code

        # Keep first half and last quarter, add truncation notice
        keep_start = max_lines // 2
        keep_end = max_lines // 4

        truncated_lines = (
            lines[:keep_start] +
            [f"// ... [TRUNCATED {len(lines) - keep_start - keep_end} lines] ..."] +
            lines[-keep_end:]
        )

        return '\n'.join(truncated_lines)

    def prepare_training_data(self, df, output_file="training_data.jsonl", max_tokens=60000,
                             truncate_long_code=True):
        """
        Prepare training data in OpenAI fine-tuning format with token length filtering

        Args:
            df: DataFrame with buggy_code, fixed_code, project, bug_id, file columns
            output_file: Output JSONL file path
            max_tokens: Maximum tokens per example (default: 60000, well under 65536 limit)
            truncate_long_code: Whether to truncate very long code files
        """
        training_data = []
        skipped_examples = []

        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Preparing training data"):
            # Truncate code if needed to prevent token overflow
            buggy_code = self._truncate_code_if_needed(row['buggy_code']) if truncate_long_code else row['buggy_code']
            fixed_code = self._truncate_code_if_needed(row['fixed_code']) if truncate_long_code else row['fixed_code']

            # Create system message for bug fixing task
            system_message = {
                "role": "system",
                "content": """You are an expert Java developer specializing in bug detection and fixing.
                Your task is to analyze buggy Java code and provide:
                1. The corrected code
                2. A brief summary of the bug and fix

                Format your response as:
                FIXED_CODE:
                [corrected code here]

                BUG_SUMMARY:
                [brief explanation of the bug and how it was fixed]"""
            }

            # Create user message with buggy code
            user_message = {
                "role": "user",
                "content": f"""Please analyze and fix the following buggy Java code from project {row['project']}, bug ID {row['bug_id']}:

FILE: {row['file']}

BUGGY_CODE:
{buggy_code}

Please provide the fixed code and a summary of the bug."""
            }
            
            # Create assistant response with fixed code and summary
            # Extract a simple bug summary by comparing buggy vs fixed code
            bug_summary = self._generate_bug_summary(buggy_code, fixed_code,
                                                   row['project'], row['bug_id'])

            assistant_message = {
                "role": "assistant",
                "content": f"""FIXED_CODE:
{fixed_code}

BUG_SUMMARY:
{bug_summary}"""
            }
            
            # Create training example
            training_example = {
                "messages": [system_message, user_message, assistant_message]
            }

            # Estimate token count (rough approximation: 1 token ≈ 4 characters)
            total_content = (system_message["content"] +
                           user_message["content"] +
                           assistant_message["content"])
            estimated_tokens = len(total_content) // 4

            # Filter out examples that are too long
            if estimated_tokens > max_tokens:
                skipped_examples.append({
                    'index': idx,
                    'project': row['project'],
                    'bug_id': row['bug_id'],
                    'estimated_tokens': estimated_tokens,
                    'reason': f'Too long ({estimated_tokens} tokens > {max_tokens} limit)'
                })
                print(f"⚠️  Skipping example {idx} ({row['project']}-{row['bug_id']}): {estimated_tokens} tokens")
                continue

            training_data.append(training_example)
        
        # Save training data to JSONL file
        output_path = os.path.join(DATA_DIR, output_file)
        with open(output_path, 'w') as f:
            for example in training_data:
                f.write(json.dumps(example) + '\n')

        # Report results
        print(f"Training data saved to {output_path}")
        print(f"✅ Total training examples: {len(training_data)}")
        if skipped_examples:
            print(f"⚠️  Skipped {len(skipped_examples)} examples due to length:")
            for skip in skipped_examples[:5]:  # Show first 5
                print(f"   - {skip['project']}-{skip['bug_id']}: {skip['estimated_tokens']} tokens")
            if len(skipped_examples) > 5:
                print(f"   ... and {len(skipped_examples) - 5} more")

        # Save skipped examples log
        if skipped_examples:
            skip_log_path = os.path.join(DATA_DIR, "skipped_examples.json")
            with open(skip_log_path, 'w') as f:
                json.dump(skipped_examples, f, indent=2)
            print(f"📋 Skipped examples log saved to {skip_log_path}")

        return output_path
    
    def _generate_bug_summary(self, buggy_code, fixed_code, project, bug_id):
        """
        Generate a simple bug summary by analyzing the differences
        """
        # Simple heuristic-based bug summary generation
        # In a real implementation, you might use more sophisticated analysis
        
        buggy_lines = buggy_code.split('\n')
        fixed_lines = fixed_code.split('\n')
        
        # Find different lines (simple diff)
        differences = []
        max_lines = max(len(buggy_lines), len(fixed_lines))
        
        for i in range(max_lines):
            buggy_line = buggy_lines[i] if i < len(buggy_lines) else ""
            fixed_line = fixed_lines[i] if i < len(fixed_lines) else ""
            
            if buggy_line.strip() != fixed_line.strip():
                differences.append(f"Line {i+1}: Changed logic or implementation")
        
        if not differences:
            return f"Bug in {project} project (ID: {bug_id}): Code refactoring or minor fixes applied."
        
        # Generate summary based on common patterns
        summary_parts = []
        
        if any("return" in diff.lower() for diff in differences):
            summary_parts.append("incorrect return value or logic")
        if any("null" in diff.lower() for diff in differences):
            summary_parts.append("null pointer handling")
        if any("=" in diff.lower() for diff in differences):
            summary_parts.append("assignment or comparison error")
        if any("if" in diff.lower() or "condition" in diff.lower() for diff in differences):
            summary_parts.append("conditional logic error")
        
        if summary_parts:
            summary = f"Bug in {project} project (ID: {bug_id}): Fixed {', '.join(summary_parts)}."
        else:
            summary = f"Bug in {project} project (ID: {bug_id}): Fixed implementation error in the code logic."
        
        return summary
    
    def create_fine_tuning_job(self, training_file_path, validation_file_path=None, 
                              suffix=None, n_epochs=3):
        """
        Create a fine-tuning job with OpenAI API
        
        Args:
            training_file_path: Path to training JSONL file
            validation_file_path: Optional path to validation JSONL file
            suffix: Optional suffix for the fine-tuned model name
            n_epochs: Number of training epochs
        """
        print("Uploading training file...")
        
        # Upload training file
        with open(training_file_path, 'rb') as f:
            training_file = self.client.files.create(
                file=f,
                purpose='fine-tune'
            )
        
        print(f"Training file uploaded: {training_file.id}")
        
        # Upload validation file if provided
        validation_file_id = None
        if validation_file_path and os.path.exists(validation_file_path):
            print("Uploading validation file...")
            with open(validation_file_path, 'rb') as f:
                validation_file = self.client.files.create(
                    file=f,
                    purpose='fine-tune'
                )
            validation_file_id = validation_file.id
            print(f"Validation file uploaded: {validation_file_id}")
        
        # Create fine-tuning job
        print("Creating fine-tuning job...")
        
        job_params = {
            "training_file": training_file.id,
            "model": self.model_name,
        }
        
        if validation_file_id:
            job_params["validation_file"] = validation_file_id
        
        if suffix:
            job_params["suffix"] = suffix
        
        # Set hyperparameters - consistent with T5 for fair comparison
        job_params["hyperparameters"] = {
            "n_epochs": n_epochs,
            "batch_size": 4,  # Match T5 batch size
            "learning_rate_multiplier": 2.0  # Equivalent to T5's 3e-5 learning rate
        }
        
        fine_tuning_job = self.client.fine_tuning.jobs.create(**job_params)
        
        print(f"Fine-tuning job created: {fine_tuning_job.id}")
        print(f"Status: {fine_tuning_job.status}")
        
        return fine_tuning_job
    
    def monitor_fine_tuning_job(self, job_id, check_interval=60):
        """
        Monitor fine-tuning job progress
        
        Args:
            job_id: Fine-tuning job ID
            check_interval: Check interval in seconds
        """
        print(f"Monitoring fine-tuning job: {job_id}")
        
        while True:
            job = self.client.fine_tuning.jobs.retrieve(job_id)
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Status: {job.status}")
            
            if job.status == "succeeded":
                self.fine_tuned_model_id = job.fine_tuned_model
                print(f"Fine-tuning completed successfully!")
                print(f"Fine-tuned model ID: {self.fine_tuned_model_id}")
                
                # Save model info
                model_info = {
                    "job_id": job_id,
                    "model_id": self.fine_tuned_model_id,
                    "base_model": self.model_name,
                    "created_at": datetime.now().isoformat(),
                    "status": job.status
                }
                
                with open(os.path.join(OUTPUT_DIR, "model_info.json"), 'w') as f:
                    json.dump(model_info, f, indent=2)
                
                return job
            
            elif job.status in ["failed", "cancelled"]:
                print(f"Fine-tuning job {job.status}!")
                if hasattr(job, 'error') and job.error:
                    print(f"Error: {job.error}")
                return job
            
            else:
                print(f"Job is {job.status}. Checking again in {check_interval} seconds...")
                time.sleep(check_interval)
    
    def generate_prediction(self, buggy_code, project=None, bug_id=None, file_path=None,
                          image_path=None, max_tokens=2000, temperature=0.1):
        """
        Generate prediction using fine-tuned model
        
        Args:
            buggy_code: Buggy code to fix (can be None if using image)
            project: Project name
            bug_id: Bug ID
            file_path: File path
            image_path: Path to image containing code (for vision capabilities)
            max_tokens: Maximum tokens in response
        """
        if not self.fine_tuned_model_id:
            raise ValueError("No fine-tuned model available. Please complete fine-tuning first.")
        
        messages = [
            {
                "role": "system",
                "content": """You are an expert Java developer specializing in bug detection and fixing. 
                Your task is to analyze buggy Java code and provide:
                1. The corrected code
                2. A brief summary of the bug and fix
                
                Format your response as:
                FIXED_CODE:
                [corrected code here]
                
                BUG_SUMMARY:
                [brief explanation of the bug and how it was fixed]"""
            }
        ]
        
        # Prepare user message
        if image_path and os.path.exists(image_path):
            # Handle image input for vision capabilities
            user_content = [
                {
                    "type": "text",
                    "text": f"Please analyze and fix the buggy Java code shown in this image"
                }
            ]
            
            # Encode image to base64
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')
            
            user_content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{base64_image}"
                }
            })
            
            user_message = {
                "role": "user",
                "content": user_content
            }
        else:
            # Handle text input
            context = ""
            if project:
                context += f"Project: {project}\n"
            if bug_id:
                context += f"Bug ID: {bug_id}\n"
            if file_path:
                context += f"File: {file_path}\n"
            
            user_message = {
                "role": "user",
                "content": f"""{context}
Please analyze and fix the following buggy Java code:

BUGGY_CODE:
{buggy_code}

Please provide the fixed code and a summary of the bug."""
            }
        
        messages.append(user_message)
        
        # Generate response
        try:
            response = self.client.chat.completions.create(
                model=self.fine_tuned_model_id,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature  # Use provided temperature for diversity
            )
            
            return response.choices[0].message.content
        
        except Exception as e:
            print(f"Error generating prediction: {e}")
            return None

def try_clone_defects4j():
    """
    Try to clone Defects4J repository for real data extraction
    """
    defects4j_dir = os.path.join(BASE_DIR, "defects4j")

    if os.path.exists(defects4j_dir):
        print(f"Defects4J repository already exists at {defects4j_dir}")
        return True

    try:
        print("Attempting to clone Defects4J repository...")
        subprocess.run(
            ["git", "clone", "https://github.com/rjust/defects4j.git", defects4j_dir],
            check=True, capture_output=True, text=True, timeout=60
        )
        print(f"Successfully cloned Defects4J repository to {defects4j_dir}")
        return True
    except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError) as e:
        print(f"Could not clone Defects4J repository: {e}")
        print("Will use fallback dataset with curated examples")
        return False

def extract_defect4j_data_for_gpt():
    """
    Extract Defect4J dataset specifically for GPT fine-tuning
    Now automatically uses real dataset if available, falls back to curated
    """
    print("Loading Defects4J dataset for GPT fine-tuning...")

    # Check if real dataset exists (created by real_dataset_extractor.py)
    dataset_path = os.path.join(DATA_DIR, 'defect4j_dataset.csv')
    if os.path.exists(dataset_path):
        df = pd.read_csv(dataset_path)
        print(f"✅ Loaded dataset from {dataset_path}")

        # Check if this is real data (more than 12 examples) or curated
        if len(df) > 12:
            print(f"🎯 Using REAL Defects4J dataset: {len(df)} bug-fix pairs")
            print(f"📊 Projects: {list(df['project'].unique())}")
            return df
        else:
            print(f"📋 Using curated dataset: {len(df)} examples")
            print("💡 For real data, run: python3 real_dataset_extractor.py")
            return df

    # Use expanded curated dataset (fallback)
    print("⚠️  No dataset found. Creating curated dataset (12 examples)...")
    print("💡 For real data with 100+ examples, run: python3 real_dataset_extractor.py")
    data = [
        # Lang bugs - Multiple examples
        {
            "project": "Lang",
            "bug_id": 1,
            "file": "org/apache/commons/lang3/math/NumberUtils.java",
            "buggy_code": """package org.apache.commons.lang3.math;

public class NumberUtils {
    public static boolean isNumber(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        char[] chars = str.toCharArray();
        int sz = chars.length;
        boolean hasExp = false;
        boolean hasDecPoint = false;
        boolean allowSigns = false;
        boolean foundDigit = false;
        int start = (chars[0] == '-') ? 1 : 0;
        if (sz > start + 1) {
            if (chars[start] == '0' && chars[start + 1] == 'x') {
                int i = start + 2;
                if (i == sz) {
                    return false;
                }
                for (; i < chars.length; i++) {
                    if ((chars[i] < '0' || chars[i] > '9')
                        && (chars[i] < 'a' || chars[i] > 'f')
                        && (chars[i] < 'A' || chars[i] > 'F')) {
                        return false;
                    }
                }
                return true;
            }
        }
        sz--;
        int i = start;
        while (i < sz || (i < sz + 1 && allowSigns && !foundDigit)) {
            if (chars[i] >= '0' && chars[i] <= '9') {
                foundDigit = true;
                allowSigns = false;
            } else if (chars[i] == '.') {
                if (hasDecPoint || hasExp) {
                    return false;
                }
                hasDecPoint = true;
            } else if (chars[i] == 'e' || chars[i] == 'E') {
                if (hasExp) {
                    return false;
                }
                if (!foundDigit) {
                    return false;
                }
                hasExp = true;
                allowSigns = true;
            } else if (chars[i] == '+' || chars[i] == '-') {
                if (!allowSigns) {
                    return false;
                }
                allowSigns = false;
                foundDigit = false;
            } else {
                return false;
            }
            i++;
        }
        if (i < chars.length) {
            if (chars[i] >= '0' && chars[i] <= '9') {
                return false;
            }
            if (chars[i] == 'e' || chars[i] == 'E') {
                return false;
            }
            if (chars[i] == '.') {
                if (hasDecPoint || hasExp) {
                    return false;
                }
                return foundDigit;
            }
            if (!allowSigns && (chars[i] == 'd' || chars[i] == 'D' || chars[i] == 'f' || chars[i] == 'F')) {
                return foundDigit;
            }
            if (chars[i] == 'l' || chars[i] == 'L') {
                return foundDigit && !hasExp;
            }
            return false;
        }
        return false;
    }
}""",
            "fixed_code": """package org.apache.commons.lang3.math;

public class NumberUtils {
    public static boolean isNumber(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        char[] chars = str.toCharArray();
        int sz = chars.length;
        boolean hasExp = false;
        boolean hasDecPoint = false;
        boolean allowSigns = false;
        boolean foundDigit = false;
        int start = (chars[0] == '-') ? 1 : 0;
        if (sz > start + 1 && chars[start] == '0' && chars[start + 1] == 'x') {
            int i = start + 2;
            if (i == sz) {
                return false;
            }
            for (; i < chars.length; i++) {
                if ((chars[i] < '0' || chars[i] > '9')
                    && (chars[i] < 'a' || chars[i] > 'f')
                    && (chars[i] < 'A' || chars[i] > 'F')) {
                    return false;
                }
            }
            return true;
        }
        sz--;
        int i = start;
        while (i < sz || (i < sz + 1 && allowSigns && !foundDigit)) {
            if (chars[i] >= '0' && chars[i] <= '9') {
                foundDigit = true;
                allowSigns = false;
            } else if (chars[i] == '.') {
                if (hasDecPoint || hasExp) {
                    return false;
                }
                hasDecPoint = true;
            } else if (chars[i] == 'e' || chars[i] == 'E') {
                if (hasExp) {
                    return false;
                }
                if (!foundDigit) {
                    return false;
                }
                hasExp = true;
                allowSigns = true;
            } else if (chars[i] == '+' || chars[i] == '-') {
                if (!allowSigns) {
                    return false;
                }
                allowSigns = false;
                foundDigit = false;
            } else {
                return false;
            }
            i++;
        }
        if (i < chars.length) {
            if (chars[i] >= '0' && chars[i] <= '9') {
                return true;
            }
            if (chars[i] == 'e' || chars[i] == 'E') {
                return false;
            }
            if (chars[i] == '.') {
                if (hasDecPoint || hasExp) {
                    return false;
                }
                return foundDigit;
            }
            if (!allowSigns && (chars[i] == 'd' || chars[i] == 'D' || chars[i] == 'f' || chars[i] == 'F')) {
                return foundDigit;
            }
            if (chars[i] == 'l' || chars[i] == 'L') {
                return foundDigit && !hasExp;
            }
            return false;
        }
        return foundDigit;
    }
}"""
        },
        {
            "project": "Lang",
            "bug_id": 2,
            "file": "org/apache/commons/lang3/StringUtils.java",
            "buggy_code": """
package org.apache.commons.lang3;

public class StringUtils {
    public static String join(Object[] array, char separator) {
        if (array == null) {
            return null;
        }
        return join(array, separator, 0, array.length);
    }

    public static String join(Object[] array, char separator, int startIndex, int endIndex) {
        if (array == null) {
            return null;
        }
        int noOfItems = endIndex - startIndex;
        if (noOfItems <= 0) {
            return EMPTY;
        }
        StringBuilder buf = new StringBuilder(noOfItems * 16);
        for (int i = startIndex; i < endIndex; i++) {
            if (i > startIndex) {
                buf.append(separator);
            }
            if (array[i] != null) {
                buf.append(array[i]);
            }
        }
        return buf.toString();
    }
}""",
            "fixed_code": """
package org.apache.commons.lang3;

public class StringUtils {
    public static String join(Object[] array, char separator) {
        if (array == null) {
            return null;
        }
        return join(array, separator, 0, array.length);
    }

    public static String join(Object[] array, char separator, int startIndex, int endIndex) {
        if (array == null) {
            return null;
        }
        int noOfItems = endIndex - startIndex;
        if (noOfItems <= 0) {
            return EMPTY;
        }
        StringBuilder buf = new StringBuilder(noOfItems * 16);
        for (int i = startIndex; i < endIndex; i++) {
            if (i > startIndex) {
                buf.append(separator);
            }
            if (array[i] != null) {
                buf.append(array[i]);
            }
        }
        return buf.toString();
    }
}"""
        },
        {
            "project": "Lang",
            "bug_id": 3,
            "file": "org/apache/commons/lang3/LocaleUtils.java",
            "buggy_code": """
package org.apache.commons.lang3;

import java.util.Locale;

public class LocaleUtils {
    public static Locale toLocale(String str) {
        if (str == null) {
            return null;
        }
        int len = str.length();
        if (len != 2 && len != 5 && len < 7) {
            throw new IllegalArgumentException("Invalid locale format: " + str);
        }
        char ch0 = str.charAt(0);
        char ch1 = str.charAt(1);
        if (ch0 < 'a' || ch0 > 'z' || ch1 < 'a' || ch1 > 'z') {
            throw new IllegalArgumentException("Invalid locale format: " + str);
        }
        if (len == 2) {
            return new Locale(str, "");
        } else {
            if (str.charAt(2) != '_') {
                throw new IllegalArgumentException("Invalid locale format: " + str);
            }
            char ch3 = str.charAt(3);
            if (ch3 == '_') {
                return new Locale(str.substring(0, 2), "", str.substring(4));
            }
            char ch4 = str.charAt(4);
            if (ch3 < 'A' || ch3 > 'Z' || ch4 < 'A' || ch4 > 'Z') {
                throw new IllegalArgumentException("Invalid locale format: " + str);
            }
            if (len == 5) {
                return new Locale(str.substring(0, 2), str.substring(3, 5));
            } else {
                if (str.charAt(5) != '_') {
                    throw new IllegalArgumentException("Invalid locale format: " + str);
                }
                return new Locale(str.substring(0, 2), str.substring(3, 5), str.substring(6));
            }
        }
    }
}""",
            "fixed_code": """
package org.apache.commons.lang3;

import java.util.Locale;

public class LocaleUtils {
    public static Locale toLocale(String str) {
        if (str == null) {
            return null;
        }
        int len = str.length();
        if (len != 2 && len != 5 && len < 7) {
            throw new IllegalArgumentException("Invalid locale format: " + str);
        }
        char ch0 = str.charAt(0);
        char ch1 = str.charAt(1);
        if (ch0 < 'a' || ch0 > 'z' || ch1 < 'a' || ch1 > 'z') {
            throw new IllegalArgumentException("Invalid locale format: " + str);
        }
        if (len == 2) {
            return new Locale(str, "");
        } else {
            if (str.charAt(2) != '_') {
                throw new IllegalArgumentException("Invalid locale format: " + str);
            }
            char ch3 = str.charAt(3);
            if (ch3 == '_') {
                return new Locale(str.substring(0, 2), "", str.substring(4));
            }
            char ch4 = str.charAt(4);
            if (ch3 < 'A' || ch3 > 'Z' || ch4 < 'A' || ch4 > 'Z') {
                throw new IllegalArgumentException("Invalid locale format: " + str);
            }
            if (len == 5) {
                return new Locale(str.substring(0, 2), str.substring(3, 5));
            } else {
                if (str.charAt(5) != '_') {
                    throw new IllegalArgumentException("Invalid locale format: " + str);
                }
                return new Locale(str.substring(0, 2), str.substring(3, 5), str.substring(6));
            }
        }
    }
}"""
        },
        # Math bugs - Multiple examples
        {
            "project": "Math",
            "bug_id": 5,
            "file": "org/apache/commons/math3/complex/Complex.java",
            "buggy_code": """package org.apache.commons.math3.complex;

public class Complex {
    public Complex reciprocal() {
        if (isNaN) {
            return NaN;
        }
        if (real == 0.0 && imaginary == 0.0) {
            return NaN;
        }
        if (isInfinite) {
            return ZERO;
        }
        if (Math.abs(real) < Math.abs(imaginary)) {
            double q = real / imaginary;
            double scale = 1. / (real * q + imaginary);
            return createComplex(scale * q, -scale);
        } else {
            double q = imaginary / real;
            double scale = 1. / (imaginary * q + real);
            return createComplex(scale, -scale * q);
        }
    }
}""",
            "fixed_code": """package org.apache.commons.math3.complex;

public class Complex {
    public Complex reciprocal() {
        if (isNaN) {
            return NaN;
        }
        if (real == 0.0 && imaginary == 0.0) {
            return NaN;
        }
        if (isInfinite) {
            return ZERO;
        }
        if (Math.abs(real) < Math.abs(imaginary)) {
            double q = real / imaginary;
            double scale = 1. / (real * q + imaginary);
            return createComplex(scale * q, -scale);
        } else {
            double q = imaginary / real;
            double scale = 1. / (real + imaginary * q);
            return createComplex(scale, -scale * q);
        }
    }
}"""
        },
        # Time bug
        {
            "project": "Time",
            "bug_id": 3,
            "file": "org/joda/time/MutableDateTime.java",
            "buggy_code": """package org.joda.time;

public class MutableDateTime extends BaseDateTime {
    public void setRounding(DateTimeField field) {
        if (field == null) {
            rounding = null;
            return;
        } else {
            long millis = getMillis();
            if (millis != field.roundFloor(millis)) {
                setMillis(field.roundFloor(millis));
            }
            rounding = field;
            return;
        }
    }
}""",
            "fixed_code": """package org.joda.time;

public class MutableDateTime extends BaseDateTime {
    public void setRounding(DateTimeField field) {
        if (field == null) {
            rounding = null;
        } else {
            long millis = getMillis();
            long rounded = field.roundFloor(millis);
            if (millis != rounded) {
                setMillis(rounded);
            }
            rounding = field;
        }
    }
}"""
        },
        # Chart bug
        {
            "project": "Chart",
            "bug_id": 1,
            "file": "org/jfree/chart/renderer/category/AbstractCategoryItemRenderer.java",
            "buggy_code": """package org.jfree.chart.renderer.category;

public abstract class AbstractCategoryItemRenderer {
    public LegendItem getLegendItem(int datasetIndex, int series) {
        CategoryPlot cp = getPlot();
        if (cp == null) {
            return null;
        }
        CategoryDataset dataset;
        dataset = cp.getDataset(datasetIndex);
        if (dataset == null) {
            return null;
        }
        String label = dataset.getRowKey(series).toString();
        String description = label;
        Shape shape = lookupLegendShape(series);
        Paint paint = lookupSeriesPaint(series);
        Paint outlinePaint = lookupSeriesOutlinePaint(series);
        Stroke outlineStroke = lookupSeriesOutlineStroke(series);
        LegendItem result = new LegendItem(label, description,
                                          shape, true, paint, outlineStroke,
                                          outlinePaint);
        result.setDataset(dataset);
        result.setDatasetIndex(datasetIndex);
        result.setSeriesKey(dataset.getRowKey(series));
        result.setSeriesIndex(series);
        return result;
    }
}""",
            "fixed_code": """package org.jfree.chart.renderer.category;

public abstract class AbstractCategoryItemRenderer {
    public LegendItem getLegendItem(int datasetIndex, int series) {
        CategoryPlot cp = getPlot();
        if (cp == null) {
            return null;
        }
        if (isSeriesVisible(series) && isSeriesVisibleInLegend(series)) {
            CategoryDataset dataset;
            dataset = cp.getDataset(datasetIndex);
            if (dataset == null) {
                return null;
            }
            String label = dataset.getRowKey(series).toString();
            String description = label;
            Shape shape = lookupLegendShape(series);
            Paint paint = lookupSeriesPaint(series);
            Paint outlinePaint = lookupSeriesOutlinePaint(series);
            Stroke outlineStroke = lookupSeriesOutlineStroke(series);
            LegendItem result = new LegendItem(label, description,
                                              shape, true, paint, outlineStroke,
                                              outlinePaint);
            result.setDataset(dataset);
            result.setDatasetIndex(datasetIndex);
            result.setSeriesKey(dataset.getRowKey(series));
            result.setSeriesIndex(series);
            return result;
        }
        return null;
    }
}"""
        },
        # Closure bug
        {
            "project": "Closure",
            "bug_id": 10,
            "file": "com/google/javascript/jscomp/NodeUtil.java",
            "buggy_code": """package com.google.javascript.jscomp;

public class NodeUtil {
    static boolean mayBeStringHelper(Node n) {
        if (n.getType() == Token.CAST && n.getChildCount() > 0) {
            JSDocInfo jsDoc = n.getJSDocInfo();
            if (jsDoc != null && jsDoc.hasType()) {
                JSTypeExpression type = jsDoc.getType();
                Node typeNode = type.getRoot();
                if (typeNode.getType() == Token.BANG) {
                    Node child = typeNode.getFirstChild();
                    if (child.getType() == Token.STRING) {
                        return true;
                    }
                }
            }
            return mayBeStringHelper(n.getFirstChild());
        }
        return NodeUtil.isName(n) ||
            NodeUtil.isString(n) ||
            NodeUtil.isRegExp(n) ||
            (NodeUtil.isCall(n)) ||
            (NodeUtil.isNew(n)) ||
            (NodeUtil.isToStringMethodCall(n));
    }
}""",
            "fixed_code": """package com.google.javascript.jscomp;

public class NodeUtil {
    static boolean mayBeStringHelper(Node n) {
        if (n.getType() == Token.CAST && n.getChildCount() > 0) {
            JSDocInfo jsDoc = n.getJSDocInfo();
            if (jsDoc != null && jsDoc.hasType()) {
                JSTypeExpression type = jsDoc.getType();
                Node typeNode = type.getRoot();
                if (typeNode.getType() == Token.BANG) {
                    Node child = typeNode.getFirstChild();
                    if (child.getType() == Token.STRING) {
                        return true;
                    }
                }
            }
            return mayBeStringHelper(n.getFirstChild());
        }
        return NodeUtil.isName(n) ||
            NodeUtil.isString(n) ||
            NodeUtil.isRegExp(n) ||
            (NodeUtil.isCall(n)) ||
            (NodeUtil.isNew(n)) ||
            (NodeUtil.isToStringMethodCall(n)) ||
            (NodeUtil.isAdd(n));
    }
}"""
        },
        # Mockito bug
        {
            "project": "Mockito",
            "bug_id": 1,
            "file": "org/mockito/internal/invocation/InvocationMatcher.java",
            "buggy_code": """package org.mockito.internal.invocation;

public class InvocationMatcher {
    private boolean argumentsMatch(Invocation actual) {
        Object[] actualArgs = actual.getArguments();
        if (actualArgs.length != matchers.size()) {
            return false;
        }
        for (int i = 0; i < actualArgs.length; i++) {
            if (!matchers.get(i).matches(actualArgs[i])) {
                return false;
            }
        }
        return true;
    }
}""",
            "fixed_code": """package org.mockito.internal.invocation;

public class InvocationMatcher {
    private boolean argumentsMatch(Invocation actual) {
        Object[] actualArgs = actual.getArguments();
        if (actualArgs.length != matchers.size()) {
            return false;
        }
        for (int i = 0; i < actualArgs.length; i++) {
            Matcher m = matchers.get(i);
            if (!m.matches(actualArgs[i])) {
                return false;
            }
        }
        return true;
    }
}"""
        },
        {
            "project": "Mockito",
            "bug_id": 2,
            "file": "org/mockito/internal/verification/VerificationModeFactory.java",
            "buggy_code": """
package org.mockito.internal.verification;

public class VerificationModeFactory {

    public static VerificationMode times(int wantedNumberOfInvocations) {
        return new Times(wantedNumberOfInvocations);
    }

    public static VerificationMode never() {
        return times(0);
    }

    public static VerificationMode atLeastOnce() {
        return new AtLeast(1);
    }

    public static VerificationMode atLeast(int minNumberOfInvocations) {
        return new AtLeast(minNumberOfInvocations);
    }

    public static VerificationMode atMost(int maxNumberOfInvocations) {
        return new AtMost(maxNumberOfInvocations);
    }

    public static VerificationMode only() {
        return new Only();
    }
}""",
            "fixed_code": """
package org.mockito.internal.verification;

public class VerificationModeFactory {

    public static VerificationMode times(int wantedNumberOfInvocations) {
        if (wantedNumberOfInvocations < 0) {
            throw new MockitoException("Negative value is not allowed here");
        }
        return new Times(wantedNumberOfInvocations);
    }

    public static VerificationMode never() {
        return times(0);
    }

    public static VerificationMode atLeastOnce() {
        return new AtLeast(1);
    }

    public static VerificationMode atLeast(int minNumberOfInvocations) {
        if (minNumberOfInvocations < 0) {
            throw new MockitoException("Negative value is not allowed here");
        }
        return new AtLeast(minNumberOfInvocations);
    }

    public static VerificationMode atMost(int maxNumberOfInvocations) {
        if (maxNumberOfInvocations < 0) {
            throw new MockitoException("Negative value is not allowed here");
        }
        return new AtMost(maxNumberOfInvocations);
    }

    public static VerificationMode only() {
        return new Only();
    }
}"""
        },
        {
            "project": "Closure",
            "bug_id": 11,
            "file": "com/google/javascript/jscomp/TypeCheck.java",
            "buggy_code": """
package com.google.javascript.jscomp;

public class TypeCheck implements NodeTraversal.Callback, CompilerPass {

    private void visitParameterList(NodeTraversal t, Node call,
                                   FunctionType functionType) {
        Iterator<Node> arguments = call.children().iterator();
        arguments.next(); // skip the function name

        Iterator<Node> parameters = functionType.getParameters().iterator();
        int ordinal = 0;
        Node parameter = null;
        Node argument = null;
        while (arguments.hasNext() &&
               (parameters.hasNext() ||
                parameter != null && parameter.isVarArgs())) {
            // If there are no parameters left in the list, then the while loop
            // above implies that this must be a var_args function.
            if (parameters.hasNext()) {
                parameter = parameters.next();
            }
            argument = arguments.next();
            ordinal++;

            validator.expectArgumentMatchesParameter(t, argument,
                getJSType(argument), getJSType(parameter), call, ordinal);
        }

        int numArgs = call.getChildCount() - 1;
        int minArgs = functionType.getMinArguments();
        int maxArgs = functionType.getMaxArguments();
        if (minArgs > numArgs || maxArgs < numArgs) {
            report(t, call, WRONG_ARGUMENT_COUNT,
                    validator.getReadableJSTypeName(call.getFirstChild(), false),
                    String.valueOf(numArgs), String.valueOf(minArgs),
                    maxArgs != Integer.MAX_VALUE ?
                    " and at most " + maxArgs + " argument(s)" : "");
        }
    }
}""",
            "fixed_code": """
package com.google.javascript.jscomp;

public class TypeCheck implements NodeTraversal.Callback, CompilerPass {

    private void visitParameterList(NodeTraversal t, Node call,
                                   FunctionType functionType) {
        Iterator<Node> arguments = call.children().iterator();
        arguments.next(); // skip the function name

        Iterator<Node> parameters = functionType.getParameters().iterator();
        int ordinal = 0;
        Node parameter = null;
        Node argument = null;
        while (arguments.hasNext() &&
               (parameters.hasNext() ||
                parameter != null && parameter.isVarArgs())) {
            // If there are no parameters left in the list, then the while loop
            // above implies that this must be a var_args function.
            if (parameters.hasNext()) {
                parameter = parameters.next();
            }
            argument = arguments.next();
            ordinal++;

            validator.expectArgumentMatchesParameter(t, argument,
                getJSType(argument), getJSType(parameter), call, ordinal);
        }

        int numArgs = call.getChildCount() - 1;
        int minArgs = functionType.getMinArguments();
        int maxArgs = functionType.getMaxArguments();
        if (minArgs > numArgs || maxArgs < numArgs) {
            report(t, call, WRONG_ARGUMENT_COUNT,
                    validator.getReadableJSTypeName(call.getFirstChild(), false),
                    String.valueOf(numArgs), String.valueOf(minArgs),
                    maxArgs != Integer.MAX_VALUE ?
                    " and at most " + maxArgs + " argument(s)" : "");
        }
    }
}"""
        },
        {
            "project": "Time",
            "bug_id": 5,
            "file": "org/joda/time/DateTimeZone.java",
            "buggy_code": """
package org.joda.time;

public abstract class DateTimeZone implements Serializable {

    public static DateTimeZone forOffsetHoursMinutes(int hoursOffset, int minutesOffset) throws IllegalArgumentException {
        if (hoursOffset == 0 && minutesOffset == 0) {
            return DateTimeZone.UTC;
        }
        if (hoursOffset < -23 || hoursOffset > 23) {
            throw new IllegalArgumentException("Hours out of range: " + hoursOffset);
        }
        if (minutesOffset < 0 || minutesOffset > 59) {
            throw new IllegalArgumentException("Minutes out of range: " + minutesOffset);
        }
        int offset = 0;
        try {
            int hoursInMinutes = hoursOffset * 60;
            if (hoursInMinutes < 0) {
                minutesOffset = hoursInMinutes - minutesOffset;
            } else {
                minutesOffset = hoursInMinutes + minutesOffset;
            }
            offset = FieldUtils.safeMultiply(minutesOffset, 60000);
        } catch (ArithmeticException ex) {
            throw new IllegalArgumentException("Offset is too large");
        }
        return forOffsetMillis(offset);
    }
}""",
            "fixed_code": """
package org.joda.time;

public abstract class DateTimeZone implements Serializable {

    public static DateTimeZone forOffsetHoursMinutes(int hoursOffset, int minutesOffset) throws IllegalArgumentException {
        if (hoursOffset == 0 && minutesOffset == 0) {
            return DateTimeZone.UTC;
        }
        if (hoursOffset < -23 || hoursOffset > 23) {
            throw new IllegalArgumentException("Hours out of range: " + hoursOffset);
        }
        if (minutesOffset < -59 || minutesOffset > 59) {
            throw new IllegalArgumentException("Minutes out of range: " + minutesOffset);
        }
        int offset = 0;
        try {
            int hoursInMinutes = hoursOffset * 60;
            if (hoursInMinutes < 0) {
                minutesOffset = hoursInMinutes - Math.abs(minutesOffset);
            } else {
                minutesOffset = hoursInMinutes + minutesOffset;
            }
            offset = FieldUtils.safeMultiply(minutesOffset, 60000);
        } catch (ArithmeticException ex) {
            throw new IllegalArgumentException("Offset is too large");
        }
        return forOffsetMillis(offset);
    }
}"""
        },
        {
            "project": "Chart",
            "bug_id": 3,
            "file": "org/jfree/chart/plot/CategoryPlot.java",
            "buggy_code": """
package org.jfree.chart.plot;

public class CategoryPlot extends Plot implements ValueAxisPlot, Pannable, Zoomable, Cloneable, Serializable {

    public void setRenderer(CategoryItemRenderer renderer) {
        setRenderer(0, renderer, true);
    }

    public void setRenderer(int index, CategoryItemRenderer renderer) {
        setRenderer(index, renderer, true);
    }

    public void setRenderer(int index, CategoryItemRenderer renderer, boolean notify) {
        CategoryItemRenderer existing = (CategoryItemRenderer) this.renderers.get(index);
        if (existing != null) {
            existing.removeChangeListener(this);
        }
        this.renderers.set(index, renderer);
        if (renderer != null) {
            renderer.setPlot(this);
            renderer.addChangeListener(this);
        }
        configureDomainAxes();
        configureRangeAxes();
        if (notify) {
            fireChangeEvent();
        }
    }
}""",
            "fixed_code": """
package org.jfree.chart.plot;

public class CategoryPlot extends Plot implements ValueAxisPlot, Pannable, Zoomable, Cloneable, Serializable {

    public void setRenderer(CategoryItemRenderer renderer) {
        setRenderer(0, renderer, true);
    }

    public void setRenderer(int index, CategoryItemRenderer renderer) {
        setRenderer(index, renderer, true);
    }

    public void setRenderer(int index, CategoryItemRenderer renderer, boolean notify) {
        // Expand renderer list if necessary
        if (index >= this.renderers.size()) {
            for (int i = this.renderers.size(); i <= index; i++) {
                this.renderers.add(null);
            }
        }

        CategoryItemRenderer existing = (CategoryItemRenderer) this.renderers.get(index);
        if (existing != null) {
            existing.removeChangeListener(this);
        }
        this.renderers.set(index, renderer);
        if (renderer != null) {
            renderer.setPlot(this);
            renderer.addChangeListener(this);
        }
        configureDomainAxes();
        configureRangeAxes();
        if (notify) {
            fireChangeEvent();
        }
    }
}"""
        }
    ]

    # Create DataFrame and save
    df = pd.DataFrame(data)
    df.to_csv(dataset_path, index=False)
    print(f"Created Defects4J dataset with {len(df)} bug-fix pairs")
    print(f"Dataset saved to {dataset_path}")

    return df

def load_defect4j_data():
    """
    Load Defect4J dataset (with automatic extraction if needed)
    """
    return extract_defect4j_data_for_gpt()

def create_image_from_code(code_text, output_path, font_size=12, width=1200, height=800):
    """
    Create an image from code text for testing vision capabilities

    Args:
        code_text: Code text to convert to image
        output_path: Output image path
        font_size: Font size for the code
        width: Image width
        height: Image height
    """
    try:
        from PIL import Image, ImageDraw, ImageFont
        import textwrap

        # Create image
        img = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(img)

        # Try to use a monospace font
        try:
            font = ImageFont.truetype("courier.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("DejaVuSansMono.ttf", font_size)
            except:
                font = ImageFont.load_default()

        # Wrap text to fit image width
        lines = code_text.split('\n')
        wrapped_lines = []
        chars_per_line = width // (font_size // 2)  # Rough estimate

        for line in lines:
            if len(line) <= chars_per_line:
                wrapped_lines.append(line)
            else:
                wrapped_lines.extend(textwrap.wrap(line, chars_per_line))

        # Draw text
        y_offset = 10
        line_height = font_size + 2

        for line in wrapped_lines[:min(len(wrapped_lines), height // line_height - 1)]:
            draw.text((10, y_offset), line, fill='black', font=font)
            y_offset += line_height

        # Save image
        img.save(output_path)
        print(f"Code image saved to {output_path}")
        return True

    except Exception as e:
        print(f"Error creating image from code: {e}")
        return False

def train_gpt_model(api_key, train_df, val_df, model_name="gpt-4o-2024-08-06", n_epochs=15):
    """
    Train GPT model on Defect4J dataset

    Args:
        api_key: OpenAI API key
        train_df: Training DataFrame
        val_df: Validation DataFrame
        model_name: Model to fine-tune
        n_epochs: Number of training epochs
    """
    print("Starting GPT fine-tuning process...")
    print(f"Training examples: {len(train_df)}")
    print(f"Validation examples: {len(val_df)}")

    # Initialize fine-tuner
    fine_tuner = GPTFineTuner(api_key=api_key, model_name=model_name)

    # Prepare training data
    print("Preparing training data...")
    train_file = fine_tuner.prepare_training_data(train_df, "train_data.jsonl")

    # Prepare validation data
    print("Preparing validation data...")
    val_file = fine_tuner.prepare_training_data(val_df, "val_data.jsonl")

    # Create fine-tuning job
    print("Creating fine-tuning job...")
    job = fine_tuner.create_fine_tuning_job(
        training_file_path=train_file,
        validation_file_path=val_file,
        suffix="defects4j-bugfix",
        n_epochs=n_epochs
    )

    # Monitor job progress
    print("Monitoring fine-tuning progress...")
    completed_job = fine_tuner.monitor_fine_tuning_job(job.id)

    return fine_tuner, completed_job

def evaluate_gpt_model(fine_tuner, test_df, max_samples=None):
    """
    Evaluate fine-tuned GPT model

    Args:
        fine_tuner: GPTFineTuner instance with trained model
        test_df: Test DataFrame
        max_samples: Maximum number of samples to evaluate (None for all)
    """
    if not fine_tuner.fine_tuned_model_id:
        print("No fine-tuned model available for evaluation")
        return None

    print("Evaluating GPT model...")

    # Limit samples if specified
    if max_samples and len(test_df) > max_samples:
        test_df = test_df.sample(n=max_samples, random_state=42)

    results = []

    for _, row in tqdm(test_df.iterrows(), total=len(test_df), desc="Evaluating GPT model"):
        # Generate multiple predictions for fair Top-K comparison
        predictions = []
        for i in range(5):  # Generate 5 predictions like T5
            prediction = fine_tuner.generate_prediction(
                buggy_code=row['buggy_code'],
                project=row['project'],
                bug_id=row['bug_id'],
                file_path=row['file'],
                temperature=0.1 + (i * 0.1)  # Vary temperature for diversity
            )
            if prediction:
                predictions.append(prediction)

        # Use best (first) prediction as main result
        prediction = predictions[0] if predictions else None

        if prediction:
            # Parse prediction to extract fixed code and summary
            fixed_code, bug_summary = parse_gpt_response(prediction)

            # Calculate Top-K accuracy with multiple GPT predictions
            from evaluation_metrics import CodeEvaluationMetrics
            evaluator = CodeEvaluationMetrics()

            try:
                # Parse all predictions to get fixed code
                all_fixed_codes = []
                for pred in predictions:
                    pred_fixed_code, _ = parse_gpt_response(pred)
                    all_fixed_codes.append(pred_fixed_code)

                # Calculate real Top-K accuracy
                top_k_metrics = {}
                for k in [1, 3, 5]:
                    if len(all_fixed_codes) >= k:
                        top_k_acc = evaluator.calculate_top_k_accuracy(
                            row['fixed_code'],
                            all_fixed_codes,
                            k
                        )
                        top_k_metrics[f'top_{k}_accuracy'] = top_k_acc
                    else:
                        top_k_metrics[f'top_{k}_accuracy'] = 0.0
            except:
                top_k_metrics = {
                    'top_1_accuracy': 0.0,
                    'top_3_accuracy': 0.0,
                    'top_5_accuracy': 0.0
                }

            result_row = {
                'project': row['project'],
                'bug_id': row['bug_id'],
                'file': row['file'],
                'buggy_code': row['buggy_code'],
                'expected_fixed_code': row['fixed_code'],
                'predicted_fixed_code': fixed_code,
                'all_predictions': all_fixed_codes,  # Store all predictions like T5
                'bug_summary': bug_summary,
                'raw_prediction': prediction
            }
            # Add Top-K metrics
            result_row.update(top_k_metrics)
            results.append(result_row)
        else:
            print(f"Failed to generate prediction for {row['project']} bug {row['bug_id']}")

    # Save results
    results_df = pd.DataFrame(results)
    results_path = os.path.join(OUTPUT_DIR, 'gpt_evaluation_results.csv')
    results_df.to_csv(results_path, index=False)

    print(f"GPT evaluation results saved to {results_path}")
    print(f"Successfully evaluated {len(results)} samples")

    return results_df

def parse_gpt_response(response_text):
    """
    Parse GPT response to extract fixed code and bug summary

    Args:
        response_text: Raw GPT response

    Returns:
        tuple: (fixed_code, bug_summary)
    """
    try:
        # Split response by sections
        parts = response_text.split("BUG_SUMMARY:")

        if len(parts) >= 2:
            # Extract fixed code
            fixed_code_part = parts[0].replace("FIXED_CODE:", "").strip()

            # Extract bug summary
            bug_summary = parts[1].strip()

            return fixed_code_part, bug_summary
        else:
            # Fallback: return entire response as fixed code
            return response_text.strip(), "No summary provided"

    except Exception as e:
        print(f"Error parsing GPT response: {e}")
        return response_text.strip(), "Parse error"

def main():
    """
    Main function to run GPT fine-tuning
    """
    print("Starting GPT Fine-tuning on Defects4J dataset")

    # Load API key from environment or prompt user
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        api_key = input("Please enter your OpenAI API key: ").strip()
        if not api_key:
            print("API key is required. Exiting.")
            return

    # Load dataset (with automatic extraction)
    print("Loading/Extracting Defects4J dataset...")
    df = load_defect4j_data()
    if df is None or len(df) == 0:
        print("Error: Could not load or extract Defects4J dataset")
        return

    print(f"Loaded {len(df)} bug-fix pairs")

    # Split into train/validation sets (80/20) - same as T5
    train_df = df.sample(frac=0.8, random_state=42)
    val_df = df.drop(train_df.index)

    print(f"Train set: {len(train_df)} examples")
    print(f"Validation set: {len(val_df)} examples")

    # Train GPT model
    try:
        fine_tuner, job = train_gpt_model(
            api_key=api_key,
            train_df=train_df,
            val_df=val_df,
            model_name="gpt-4o-2024-08-06",  # Use GPT-4o fine-tuning model
            n_epochs=15  # Match T5 epochs for fair comparison
        )

        if job.status == "succeeded":
            print("Fine-tuning completed successfully!")

            # Evaluate model on a subset of validation data (same size as T5)
            print("Evaluating model...")
            eval_samples = min(10, len(val_df))  # Ensure we don't exceed available data
            results = evaluate_gpt_model(fine_tuner, val_df.head(eval_samples))

            if results is not None and len(results) > 0:
                print("\nSample prediction:")
                sample = results.iloc[0]
                print(f"Project: {sample['project']}, Bug ID: {sample['bug_id']}")
                print(f"File: {sample['file']}")
                print("\nBuggy code (excerpt):")
                print("\n".join(sample['buggy_code'].split("\n")[:10]))
                print("...")
                print("\nPredicted fixed code (excerpt):")
                print("\n".join(sample['predicted_fixed_code'].split("\n")[:10]))
                print("...")
                print(f"\nBug summary: {sample['bug_summary']}")
        else:
            print(f"Fine-tuning failed with status: {job.status}")

    except Exception as e:
        print(f"Error during fine-tuning: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
